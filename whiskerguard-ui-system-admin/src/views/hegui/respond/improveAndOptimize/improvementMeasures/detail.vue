<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import { Calendar, Check, Download, Edit, Flag, Message, More,
  Paperclip, Phone, Plus, Printer, Refresh, Upload, User, VideoPlay,
} from '@element-plus/icons-vue'

const searchText = ref('')
const activeTab = ref('progress')

const delayRecords = ref([
  {
    applyTime: '2023-05-10',
    applicant: '张明远',
    originalDate: '2023-05-20',
    delayDate: '2023-05-25',
    reason: '供应商交付延迟',
    status: '已批准',
  },
])

const progressChart = ref<HTMLElement | null>(null)

onMounted(() => {
  nextTick(() => {
    if (progressChart.value) {
      const chart = echarts.init(progressChart.value)
      const option = {
        animation: false,
        tooltip: {
          trigger: 'axis',
          formatter: '{b}<br/>{a0}: {c0}%<br/>{a1}: {c1}%',
        },
        legend: {
          data: ['实际进度', '计划进度'],
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['2023-04-25', '2023-05-01', '2023-05-08', '2023-05-15', '2023-05-22', '2023-05-29', '2023-06-05', '2023-06-12', '2023-06-19', '2023-06-26', '2023-06-30'],
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}%',
          },
        },
        series: [
          {
            name: '实际进度',
            type: 'line',
            smooth: true,
            data: [5, 8, 15, 30, 35, null, null, null, null, null, null],
            itemStyle: {
              color: '#3b82f6',
            },
            lineStyle: {
              width: 3,
            },
            symbolSize: 8,
          },
          {
            name: '计划进度',
            type: 'line',
            smooth: true,
            data: [5, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            itemStyle: {
              color: '#e5e7eb',
            },
            lineStyle: {
              width: 2,
              type: 'dashed',
            },
            symbolSize: 6,
          },
        ],
      }
      chart.setOption(option)

      window.addEventListener('resize', () => {
        chart.resize()
      })
    }
  })
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="mr-4 text-xl c-[#000000] font-bold">
              提升客户满意度改进方案
            </h1>
            <el-tag type="success" size="small">
              进行中
            </el-tag>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex items-center space-x-2">
            <el-button v-auth="['improvementMeasures/detail/startExecution']" type="primary" class="rounded-button">
              <el-icon><VideoPlay /></el-icon>
              <span>开始执行</span>
            </el-button>
            <el-button v-auth="['improvementMeasures/detail/updateProgress']" type="primary" class="rounded-button">
              <el-icon>
                <Refresh />
              </el-icon>
              <span>更新进度</span>
            </el-button>
            <el-button v-auth="['improvementMeasures/detail/completeMeasure']" type="primary" class="rounded-button">
              <el-icon>
                <Check />
              </el-icon>
              <span>完成措施</span>
            </el-button>
            <el-button v-auth="['improvementMeasures/detail/edit']" text circle>
              <el-icon>
                <Edit />
              </el-icon>
            </el-button>
            <el-button v-auth="['improvementMeasures/detail/download']" text circle>
              <el-icon>
                <Download />
              </el-icon>
            </el-button>
            <el-button v-auth="['improvementMeasures/detail/print']" text circle>
              <el-icon>
                <Printer />
              </el-icon>
            </el-button>
            <el-dropdown>
              <el-button text circle>
                <el-icon>
                  <More />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-auth="['improvementMeasures/detail/cancelMeasure']">取消措施</el-dropdown-item>
                  <el-dropdown-item v-auth="['improvementMeasures/detail/share']">分享</el-dropdown-item>
                  <el-dropdown-item v-auth="['improvementMeasures/detail/setAsTemplate']">设为模板</el-dropdown-item>
                  <el-dropdown-item v-auth="['improvementMeasures/detail/delete']">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <!-- 基本信息区 -->
            <el-card class="rounded-lg">
              <template #header>
                <div class="f-16 fw-600">
                  基本信息
                </div>
              </template>
              <div class="grid grid-cols-2 gap-6">
                <div>
                  <!-- <h3 class="text-lg font-semibold mb-4">基本信息</h3> -->
                  <div class="space-y-4">
                    <div class="flex">
                      <span class="w-1/3 text-sm text-gray-500">措施编号</span>
                      <span class="w-2/3 text-sm font-medium">CI-2023-0425-001</span>
                    </div>
                    <div class="flex">
                      <span class="w-1/3 text-sm text-gray-500">措施名称</span>
                      <span class="w-2/3 text-sm font-medium">提升客户满意度改进方案</span>
                    </div>
                    <div class="flex">
                      <span class="w-1/3 text-sm text-gray-500">措施类型</span>
                      <span class="w-2/3 text-sm font-medium">客户服务改进</span>
                    </div>
                    <div class="flex">
                      <span class="w-1/3 text-sm text-gray-500">优先级</span>
                      <span class="w-2/3">
                        <el-tag type="danger" size="small">高</el-tag>
                      </span>
                    </div>
                    <div class="flex">
                      <span class="w-1/3 text-sm text-gray-500">来源</span>
                      <el-link type="primary" :underline="false" class="w-2/3 text-sm">
                        客户投诉分析报告-Q1
                      </el-link>
                    </div>
                    <div class="flex">
                      <span class="w-1/3 text-sm text-gray-500">责任部门</span>
                      <span class="w-2/3 text-sm font-medium">客户服务部</span>
                    </div>
                    <div class="flex">
                      <span class="w-1/3 text-sm text-gray-500">负责人</span>
                      <span class="w-2/3 text-sm font-medium">张明远</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 class="mb-4 text-lg font-semibold">
                    时间信息
                  </h3>
                  <div class="space-y-4">
                    <div class="flex">
                      <span class="w-1/3 text-sm text-gray-500">协作人</span>
                      <span class="w-2/3 text-sm font-medium">李静, 王伟, 陈芳</span>
                    </div>
                    <div class="flex">
                      <span class="w-1/3 text-sm text-gray-500">监督人</span>
                      <span class="w-2/3 text-sm font-medium">刘强</span>
                    </div>
                    <div class="flex">
                      <span class="w-1/3 text-sm text-gray-500">开始日期</span>
                      <span class="w-2/3 text-sm font-medium">2023-04-25</span>
                    </div>
                    <div class="flex">
                      <span class="w-1/3 text-sm text-gray-500">计划完成日期</span>
                      <span class="w-2/3 text-sm font-medium">2023-06-30</span>
                    </div>
                    <div class="flex">
                      <span class="w-1/3 text-sm text-gray-500">当前状态</span>
                      <span class="w-2/3">
                        <el-tag type="success" size="small">进行中</el-tag>
                      </span>
                    </div>
                    <div class="flex">
                      <span class="w-1/3 text-sm text-gray-500">实际完成日期</span>
                      <span class="w-2/3 text-sm font-medium">-</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="mt-6">
                <h3 class="mb-4 text-lg font-semibold">
                  改进进度
                </h3>
                <div class="flex items-center space-x-4">
                  <el-progress :percentage="45" :stroke-width="10" class="w-full" />
                  <span class="text-sm font-medium">45%</span>
                  <span class="text-sm text-gray-500">剩余 42 天</span>
                </div>
                <div class="mt-2 flex justify-between">
                  <span class="text-xs text-gray-500">2023-04-25</span>
                  <span class="text-xs text-gray-500">2023-06-30</span>
                </div>
              </div>
            </el-card>

            <!-- 改进内容区 -->
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  改进内容
                </div>
              </template>
              <div class="space-y-6">
                <div>
                  <h4 class="mb-2 text-base font-medium">
                    改进背景
                  </h4>
                  <p class="text-sm text-gray-700 leading-relaxed">
                    根据第一季度客户满意度调查结果显示，我们的客户满意度评分从去年的 92% 下降到了 85%，主要问题集中在客服响应速度慢（占比 45%）、问题解决效率低（占比 30%）和服务态度不佳（占比
                    25%）。客户投诉量同比增加了 35%，其中 60% 的投诉与客服体验相关。这一趋势如果持续下去，将严重影响公司品牌形象和客户留存率。
                  </p>
                </div>
                <div>
                  <h4 class="mb-2 text-base font-medium">
                    改进目标
                  </h4>
                  <p class="text-sm text-gray-700 leading-relaxed">
                    1. 在 3 个月内将客户满意度评分提升至 90% 以上<br>
                    2. 将客服平均响应时间从目前的 8 分钟缩短至 3 分钟以内<br>
                    3. 提高首次问题解决率从 65% 提升至 85%<br>
                    4. 降低客户投诉率至去年同期水平（减少 35%）
                  </p>
                </div>
                <div>
                  <h4 class="mb-2 text-base font-medium">
                    改进方案
                  </h4>
                  <p class="text-sm text-gray-700 leading-relaxed">
                    1. 优化客服工作流程：重新设计客服工单处理流程，减少不必要的审批环节，实施分级响应机制<br>
                    2. 技术升级：部署智能客服系统，实现常见问题自动回复，复杂问题智能路由<br>
                    3. 人员培训：开展为期两周的集中培训，重点提升沟通技巧和问题解决能力<br>
                    4. 绩效考核改革：将客户满意度指标纳入绩效考核，占比提升至 40%<br>
                    5. 客户反馈机制：实施 24 小时内回访制度，确保问题闭环处理
                  </p>
                </div>
                <div>
                  <h4 class="mb-2 text-base font-medium">
                    预期效果
                  </h4>
                  <p class="text-sm text-gray-700 leading-relaxed">
                    通过上述改进措施，预计可以在 3 个月内显著提升客户满意度，具体表现为：<br>
                    - 客户满意度评分提升至 90% 以上<br>
                    - 客服响应时间缩短至 3 分钟以内<br>
                    - 首次问题解决率提升至 85%<br>
                    - 客户投诉量减少 35%<br>
                    长期来看，这将有助于提升客户留存率，预计年度客户流失率可从当前的 15% 降至 10% 以下。
                  </p>
                </div>
                <div>
                  <h4 class="mb-2 text-base font-medium">
                    验收标准
                  </h4>
                  <p class="text-sm text-gray-700 leading-relaxed">
                    1. 客户满意度调查评分达到 90% 以上（基于季度调查结果）<br>
                    2. 客服响应时间统计数据显示 95% 的请求在 3 分钟内得到响应<br>
                    3. 首次问题解决率系统记录达到 85%<br>
                    4. 客户投诉量同比减少 35%（基于投诉管理系统数据）<br>
                    5. 改进措施实施后 3 个月内达成上述指标，并持续稳定 1 个月
                  </p>
                </div>
              </div>
            </el-card>

            <!-- 内容标签页区 -->
            <el-card class="mt-20">
              <el-tabs v-model="activeTab">
                <el-tab-pane label="进度跟踪" name="progress">
                  <div class="mb-6 flex items-center justify-between">
                    <h3 class="text-lg font-semibold">
                      进度更新记录
                    </h3>
                    <el-button v-auth="['improvementMeasures/detail/updateProgress']" type="primary" class="rounded-button">
                      <el-icon>
                        <Plus />
                      </el-icon>
                      <span>更新进度</span>
                    </el-button>
                  </div>

                  <div class="space-y-6">
                    <!-- 时间线记录 -->
                    <div class="timeline-item relative pl-8">
                      <div
                        class="bg-primary absolute left-0 top-0 h-6 w-6 flex items-center justify-center rounded-full text-white"
                      >
                        <el-icon>
                          <Check />
                        </el-icon>
                      </div>
                      <div class="pb-6">
                        <div class="flex justify-between">
                          <span class="text-sm font-medium">2023-05-15</span>
                          <span class="text-sm text-gray-500">张明远</span>
                        </div>
                        <div class="mt-1 text-sm text-gray-700">
                          <p>完成客服工作流程优化设计，已通过部门评审，进入实施阶段。当前进度达到 30%。</p>
                          <div class="mt-2 flex items-center text-xs text-gray-500">
                            <el-icon>
                              <Paperclip />
                            </el-icon>
                            <span class="ml-1">流程优化方案.pdf</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="timeline-item relative pl-8">
                      <div
                        class="bg-primary absolute left-0 top-0 h-6 w-6 flex items-center justify-center rounded-full text-white"
                      >
                        <el-icon>
                          <Check />
                        </el-icon>
                      </div>
                      <div class="pb-6">
                        <div class="flex justify-between">
                          <span class="text-sm font-medium">2023-05-08</span>
                          <span class="text-sm text-gray-500">李静</span>
                        </div>
                        <div class="mt-1 text-sm text-gray-700">
                          <p>完成智能客服系统选型，已与供应商签订合同，预计两周内完成部署。当前进度达到 15%。</p>
                        </div>
                      </div>
                    </div>

                    <div class="timeline-item relative pl-8">
                      <div
                        class="bg-primary absolute left-0 top-0 h-6 w-6 flex items-center justify-center rounded-full text-white"
                      >
                        <el-icon>
                          <Check />
                        </el-icon>
                      </div>
                      <div class="pb-6">
                        <div class="flex justify-between">
                          <span class="text-sm font-medium">2023-04-25</span>
                          <span class="text-sm text-gray-500">张明远</span>
                        </div>
                        <div class="mt-1 text-sm text-gray-700">
                          <p>改进措施正式启动，召开项目启动会，明确各成员职责分工。当前进度 5%。</p>
                          <div class="mt-2 flex items-center text-xs text-gray-500">
                            <el-icon>
                              <Paperclip />
                            </el-icon>
                            <span class="ml-1">项目启动会纪要.docx</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 进度图表 -->
                  <div class="mt-8">
                    <h3 class="mb-4 text-lg font-semibold">
                      进度趋势
                    </h3>
                    <div ref="progressChart" style="width: 100%; height: 300px;" />
                  </div>

                  <!-- 延期记录 -->
                  <div class="mt-8">
                    <h3 class="mb-4 text-lg font-semibold">
                      延期记录
                    </h3>
                    <el-table :data="delayRecords" style="width: 100%;">
                      <el-table-column prop="applyTime" label="申请时间" width="120" />
                      <el-table-column prop="applicant" label="申请人" width="100" />
                      <el-table-column prop="originalDate" label="原计划日期" width="120" />
                      <el-table-column prop="delayDate" label="延期日期" width="120" />
                      <el-table-column prop="reason" label="延期原因" />
                      <el-table-column prop="status" label="审批状态" width="120">
                        <template #default="{ row }">
                          <el-tag type="success" size="small">
                            {{ row.status }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="80">
                        <template #default>
                          <el-link v-auth="['improvementMeasures/detail/viewDelay']" type="primary" :underline="false">
                            查看
                          </el-link>
                        </template>
                      </el-table-column>
                    </el-table>
                    <div class="mt-4">
                      <el-button v-auth="['improvementMeasures/detail/applyDelay']" type="primary" plain class="rounded-button">
                        <el-icon>
                          <Plus />
                        </el-icon>
                        <span>申请延期</span>
                      </el-button>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="任务分解" name="tasks">
                  任务分解
                </el-tab-pane>
                <el-tab-pane label="资源需求" name="resources">
                  资源需求
                </el-tab-pane>
                <el-tab-pane label="效果评估" name="evaluation">
                  效果评估
                </el-tab-pane>
                <el-tab-pane label="相关资料" name="materials">
                  相关资料
                </el-tab-pane>
              </el-tabs>
            </el-card>
          </el-col>
          <el-col :span="6">
            <!-- 改进时间线 -->
            <el-card class="rounded-lg">
              <template #header>
                <div class="f-16 fw-600">
                  改进时间线
                </div>
              </template>
              <div class="space-y-4">
                <div class="flex items-start">
                  <div class="bg-primary mr-3 h-6 w-6 flex items-center justify-center rounded-full text-white">
                    <el-icon>
                      <Check />
                    </el-icon>
                  </div>
                  <div>
                    <p class="text-sm font-medium">
                      创建时间
                    </p>
                    <p class="text-xs text-gray-500">
                      2023-04-20
                    </p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div class="bg-primary mr-3 h-6 w-6 flex items-center justify-center rounded-full text-white">
                    <el-icon>
                      <Check />
                    </el-icon>
                  </div>
                  <div>
                    <p class="text-sm font-medium">
                      开始时间
                    </p>
                    <p class="text-xs text-gray-500">
                      2023-04-25
                    </p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div class="mr-3 h-6 w-6 flex items-center justify-center rounded-full bg-yellow-400 text-white">
                    <el-icon>
                      <More />
                    </el-icon>
                  </div>
                  <div>
                    <p class="text-sm font-medium">
                      当前阶段
                    </p>
                    <p class="text-xs text-gray-500">
                      实施阶段
                    </p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div class="mr-3 h-6 w-6 flex items-center justify-center rounded-full bg-gray-200 text-gray-500">
                    <el-icon>
                      <Flag />
                    </el-icon>
                  </div>
                  <div>
                    <p class="text-sm font-medium">
                      计划完成
                    </p>
                    <p class="text-xs text-gray-500">
                      2023-06-30
                    </p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div class="mr-3 h-6 w-6 flex items-center justify-center rounded-full bg-gray-200 text-gray-500">
                    <el-icon>
                      <Flag />
                    </el-icon>
                  </div>
                  <div>
                    <p class="text-sm font-medium">
                      实际完成
                    </p>
                    <p class="text-xs text-gray-500">
                      -
                    </p>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 相关人员 -->
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关人员
                </div>
              </template>
              <div class="space-y-4">
                <div class="flex items-center">
                  <el-avatar size="small" class="bg-primary mr-3">
                    张
                  </el-avatar>
                  <div>
                    <p class="text-sm font-medium">
                      张明远
                    </p>
                    <p class="text-xs text-gray-500">
                      负责人 · 客户服务部
                    </p>
                  </div>
                  <div class="ml-auto">
                    <el-button v-auth="['improvementMeasures/detail/callPerson']" text circle>
                      <el-icon>
                        <Phone />
                      </el-icon>
                    </el-button>
                    <el-button v-auth="['improvementMeasures/detail/messagePerson']" text circle>
                      <el-icon>
                        <Message />
                      </el-icon>
                    </el-button>
                  </div>
                </div>
                <div class="flex items-center">
                  <el-avatar size="small" class="mr-3 bg-green-500">
                    李
                  </el-avatar>
                  <div>
                    <p class="text-sm font-medium">
                      李静
                    </p>
                    <p class="text-xs text-gray-500">
                      协作人 · 技术部
                    </p>
                  </div>
                  <div class="ml-auto">
                    <el-button v-auth="['improvementMeasures/detail/callPerson']" text circle>
                      <el-icon>
                        <Phone />
                      </el-icon>
                    </el-button>
                    <el-button v-auth="['improvementMeasures/detail/messagePerson']" text circle>
                      <el-icon>
                        <Message />
                      </el-icon>
                    </el-button>
                  </div>
                </div>
                <div class="flex items-center">
                  <el-avatar size="small" class="mr-3 bg-purple-500">
                    王
                  </el-avatar>
                  <div>
                    <p class="text-sm font-medium">
                      王伟
                    </p>
                    <p class="text-xs text-gray-500">
                      协作人 · 人力资源部
                    </p>
                  </div>
                  <div class="ml-auto">
                    <el-button v-auth="['improvementMeasures/detail/callPerson']" text circle>
                      <el-icon>
                        <Phone />
                      </el-icon>
                    </el-button>
                    <el-button v-auth="['improvementMeasures/detail/messagePerson']" text circle>
                      <el-icon>
                        <Message />
                      </el-icon>
                    </el-button>
                  </div>
                </div>
                <div class="flex items-center">
                  <el-avatar size="small" class="mr-3 bg-yellow-500">
                    刘
                  </el-avatar>
                  <div>
                    <p class="text-sm font-medium">
                      刘强
                    </p>
                    <p class="text-xs text-gray-500">
                      监督人 · 质量管理部
                    </p>
                  </div>
                  <div class="ml-auto">
                    <el-button v-auth="['improvementMeasures/detail/callPerson']" text circle>
                      <el-icon>
                        <Phone />
                      </el-icon>
                    </el-button>
                    <el-button v-auth="['improvementMeasures/detail/messagePerson']" text circle>
                      <el-icon>
                        <Message />
                      </el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 相关改进措施 -->
            <el-card class="mt-20 rounded-lg">
              <template #header>
                <div class="f-16 fw-600">
                  相关改进
                </div>
              </template>
              <div class="space-y-3">
                <el-card shadow="hover" class="cursor-pointer">
                  <p class="text-sm font-medium">
                    客服人员培训体系优化
                  </p>
                  <div class="mt-1 flex items-center">
                    <el-tag type="primary" size="small" class="mr-2">
                      进行中
                    </el-tag>
                    <span class="text-xs text-gray-500">完成 60%</span>
                  </div>
                </el-card>
                <el-card shadow="hover" class="cursor-pointer">
                  <p class="text-sm font-medium">
                    客户投诉处理流程改进
                  </p>
                  <div class="mt-1 flex items-center">
                    <el-tag type="success" size="small" class="mr-2">
                      已完成
                    </el-tag>
                    <span class="text-xs text-gray-500">2023-03-15</span>
                  </div>
                </el-card>
                <el-card shadow="hover" class="cursor-pointer">
                  <p class="text-sm font-medium">
                    客户满意度调查问卷优化
                  </p>
                  <div class="mt-1 flex items-center">
                    <el-tag size="small" class="mr-2">
                      未开始
                    </el-tag>
                    <span class="text-xs text-gray-500">计划 2023-07-01</span>
                  </div>
                </el-card>
              </div>
            </el-card>

            <!-- 快捷操作 -->
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  快捷操作
                </div>
              </template>
              <div class="space-y-3">
                <div>
                  <el-button v-auth="['improvementMeasures/detail/updateProgress']" type="primary" class="rounded-button w-full">
                    <el-icon>
                      <Refresh />
                    </el-icon>
                    <span>更新进度</span>
                  </el-button>
                </div>
                <div>
                  <el-button v-auth="['improvementMeasures/detail/addTask']" type="primary" plain class="rounded-button w-full">
                    <el-icon>
                      <Plus />
                    </el-icon>
                    <span>添加任务</span>
                  </el-button>
                </div>
                <div>
                  <el-button v-auth="['improvementMeasures/detail/uploadMaterial']" type="primary" plain class="rounded-button w-full">
                    <el-icon>
                      <Upload />
                    </el-icon>
                    <span>上传资料</span>
                  </el-button>
                </div>
                <div>
                  <el-button v-auth="['improvementMeasures/detail/applyDelay']" type="primary" plain class="rounded-button w-full">
                    <el-icon>
                      <Calendar />
                    </el-icon>
                    <span>申请延期</span>
                  </el-button>
                </div>
                <div>
                  <el-button v-auth="['improvementMeasures/detail/initiateMeeting']" type="primary" plain class="rounded-button w-full">
                    <el-icon>
                      <User />
                    </el-icon>
                    <span>发起会议</span>
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .font-pacifico {
    font-family: Pacifico, serif;
  }

  .timeline-item::before {
    position: absolute;
    top: 0;
    left: 11px;
    width: 2px;
    height: 100%;
    content: "";
    background-color: #e5e7eb;
  }

  .timeline-item:last-child::before {
    display: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
</style>
