<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import {
  ArrowDown,
  Bottom,
  CircleCheck,
  Clock,
  Document,
  Loading,
  MagicStick,
  Plus,
  Search,
  Star,
  Top,
  Upload,
  Warning,
} from '@element-plus/icons-vue'
</script>

<script lang="ts">
import * as echarts from 'echarts'

const viewType = ref<'table' | 'card' | 'gantt'>('table')
const ganttDays = computed(() => {
  const days = []
  const today = new Date()
  const startDate = new Date(today)
  startDate.setDate(today.getDate() - 15)
  const endDate = new Date(today)
  endDate.setDate(today.getDate() + 45)
  for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
    days.push({
      date: `${d.getMonth() + 1}/${d.getDate()}`,
      day: ['日', '一', '二', '三', '四', '五', '六'][d.getDay()],
      isToday: d.toDateString() === today.toDateString(),
    })
  }
  return days
})
const draggingItem = ref<any>(null)
function handleDragStart(item: any) {
  draggingItem.value = item
}
function handleDrop(newStartDate: string) {
  if (draggingItem.value) {
    const daysDiff = Math.floor((new Date(newStartDate).getTime() - new Date(draggingItem.value.startDate).getTime()) / (1000 * 60 * 60 * 24))
    const newEndDate = new Date(new Date(draggingItem.value.endDate).getTime() + daysDiff * 24 * 60 * 60 * 1000)
    draggingItem.value.startDate = newStartDate
    draggingItem.value.endDate = newEndDate.toISOString().split('T')[0]
    draggingItem.value = null
  }
}
function getDaysLeft(endDate: string) {
  const end = new Date(endDate)
  const now = new Date()
  const diffTime = end.getTime() - now.getTime()
  return Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)))
}
function getGanttPosition(startDate: string) {
  const start = new Date(startDate)
  const now = new Date()
  const diffDays = Math.floor((start.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays * 4)
}
function getGanttWidth(startDate: string, endDate: string) {
  const start = new Date(startDate)
  const end = new Date(endDate)
  const diffDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))
  return diffDays * 4
}
const updateDialogVisible = ref(false)
const currentItem = ref<any>(null)
const updateForm = ref({
  progress: 0,
  progressNote: '',
  problems: '',
  solutions: '',
  attachments: [],
})
function showUpdateDialog(item: any) {
  currentItem.value = item
  updateForm.value = {
    progress: item.progress,
    progressNote: '',
    problems: '',
    solutions: '',
    attachments: [],
  }
  updateDialogVisible.value = true
}
function handleUpdateSubmit() {
  const item = currentItem.value
  if (item) {
    item.progress = updateForm.value.progress
    if (item.progress === 100) {
      item.status = '已完成'
    }
    updateDialogVisible.value = false
  }
}
const filterTypes = ref({
  process: true,
  system: true,
  position: true,
  tech: true,
  training: true,
  other: true,
})

function toggleAllTypes() {
  const allSelected = Object.values(filterTypes.value).every(v => v)
  Object.keys(filterTypes.value).forEach((key) => {
    filterTypes.value[key] = !allSelected
  })
}

const templates = ref([
  {
    name: '数据安全漏洞修复',
    scenario: '适用于系统安全漏洞修复类改进措施',
    content: '...',
  },
  {
    name: '合规培训计划更新',
    scenario: '适用于年度合规培训计划更新',
    content: '...',
  },
  {
    name: '流程优化标准模板',
    scenario: '适用于各类业务流程优化措施',
    content: '...',
  },
])

function applyTemplate(template) {
  // 应用模板逻辑
}

const departmentStats = ref([
  {
    department: '合规部',
    total: 57,
    completed: 15,
    completionRate: 26,
    effectivenessRate: 82,
  },
  {
    department: '技术部',
    total: 32,
    completed: 8,
    completionRate: 25,
    effectivenessRate: 75,
  },
  {
    department: '人力资源',
    total: 19,
    completed: 6,
    completionRate: 32,
    effectivenessRate: 85,
  },
])

const efficiencyRanking = ref([
  {
    rank: 1,
    owner: '李华',
    count: 12,
    completionRate: '92%',
    avgTime: '18天',
    isCurrentUser: false,
  },
  {
    rank: 2,
    owner: '张明',
    count: 8,
    completionRate: '88%',
    avgTime: '22天',
    isCurrentUser: true,
  },
  {
    rank: 3,
    owner: '王芳',
    count: 10,
    completionRate: '85%',
    avgTime: '25天',
    isCurrentUser: false,
  },
])

const aiSuggestion = ref('')

function analyzeDelayRisk() {
  aiSuggestion.value = 'AI分析：当前有3项措施存在延期风险，建议优先处理"数据安全漏洞修复"和"反洗钱系统升级"两项高优先级措施。'
}

function generateProgressReport() {
  aiSuggestion.value = 'AI已生成进度报告：本月共完成15项改进措施，总体完成率65%，较上月提升5%。'
}

function suggestPriorityMeasures() {
  aiSuggestion.value = 'AI建议：根据当前进度和优先级，建议优先处理"数据安全漏洞修复"(技术部)和"客户投诉处理流程优化"(市场部)。'
}

const tableData = ref([
  {
    id: 'CM2023001',
    name: '数据安全漏洞修复',
    type: '系统升级',
    priority: '高',
    source: '内部审计',
    department: '技术部',
    owner: '张明',
    startDate: '2023-10-15',
    endDate: '2023-12-15',
    status: '进行中',
    progress: 45,
    result: '待评估',
  },
  {
    id: 'CM2023002',
    name: '合规培训计划更新',
    type: '培训教育',
    priority: '中',
    source: '年度计划',
    department: '人力资源',
    owner: '李华',
    startDate: '2023-09-01',
    endDate: '2023-11-30',
    status: '已完成',
    progress: 100,
    result: '有效',
  },
  {
    id: 'CM2023003',
    name: '客户投诉处理流程优化',
    type: '流程优化',
    priority: '中',
    source: '客户反馈',
    department: '市场部',
    owner: '王芳',
    startDate: '2023-11-01',
    endDate: '2023-12-31',
    status: '进行中',
    progress: 30,
    result: '待评估',
  },
  {
    id: 'CM2023004',
    name: '内部审计系统升级',
    type: '系统升级',
    priority: '高',
    source: '内部审计',
    department: '合规部',
    owner: '赵强',
    startDate: '2023-12-01',
    endDate: '2024-01-31',
    status: '未开始',
    progress: 0,
    result: '待评估',
  },
  {
    id: 'CM2023005',
    name: '隐私政策更新',
    type: '政策更新',
    priority: '低',
    source: '法规变更',
    department: '合规部',
    owner: '刘伟',
    startDate: '2023-10-01',
    endDate: '2023-10-31',
    status: '已完成',
    progress: 100,
    result: '部分有效',
  },
  {
    id: 'CM2023006',
    name: '员工合规意识培训',
    type: '培训教育',
    priority: '中',
    source: '年度计划',
    department: '人力资源',
    owner: '陈静',
    startDate: '2023-09-15',
    endDate: '2023-11-15',
    status: '进行中',
    progress: 75,
    result: '待评估',
  },
  {
    id: 'CM2023007',
    name: '供应商合规评估',
    type: '流程优化',
    priority: '高',
    source: '管理层要求',
    department: '采购部',
    owner: '孙丽',
    startDate: '2023-08-01',
    endDate: '2023-10-31',
    status: '已逾期',
    progress: 90,
    result: '无效',
  },
  {
    id: 'CM2023008',
    name: '反洗钱系统升级',
    type: '系统升级',
    priority: '高',
    source: '法规变更',
    department: '合规部',
    owner: '周涛',
    startDate: '2023-11-15',
    endDate: '2024-02-15',
    status: '进行中',
    progress: 20,
    result: '待评估',
  },
  {
    id: 'CM2023009',
    name: '合同管理系统优化',
    type: '系统升级',
    priority: '中',
    source: '内部审计',
    department: '法务部',
    owner: '吴刚',
    startDate: '2023-10-01',
    endDate: '2023-12-31',
    status: '进行中',
    progress: 60,
    result: '待评估',
  },
  {
    id: 'CM2023010',
    name: '合规风险报告流程优化',
    type: '流程优化',
    priority: '低',
    source: '管理层要求',
    department: '合规部',
    owner: '郑红',
    startDate: '2023-09-01',
    endDate: '2023-11-30',
    status: '已完成',
    progress: 100,
    result: '有效',
  },
])
const activeAnalysisTab = ref('source')
const trendViewType = ref('count')
const analysisData = ref([
  {
    source: '内部审计',
    count: 56,
    percentage: '43.8%',
  },
  {
    source: '年度计划',
    count: 32,
    percentage: '25.0%',
  },
  {
    source: '客户反馈',
    count: 18,
    percentage: '14.1%',
  },
  {
    source: '法规变更',
    count: 12,
    percentage: '9.4%',
  },
  {
    source: '管理层要求',
    count: 10,
    percentage: '7.8%',
  },
])
const departmentAnalysisData = ref([
  {
    department: '合规部',
    notStarted: 12,
    inProgress: 28,
    completed: 15,
    overdue: 2,
    total: 57,
  },
  {
    department: '技术部',
    notStarted: 5,
    inProgress: 18,
    completed: 8,
    overdue: 1,
    total: 32,
  },
  {
    department: '人力资源',
    notStarted: 3,
    inProgress: 10,
    completed: 6,
    overdue: 0,
    total: 19,
  },
  {
    department: '市场部',
    notStarted: 2,
    inProgress: 8,
    completed: 3,
    overdue: 1,
    total: 14,
  },
  {
    department: '法务部',
    notStarted: 2,
    inProgress: 4,
    completed: 0,
    overdue: 0,
    total: 6,
  },
])
const trendAnalysisData = ref([
  {
    period: '2023 Q1',
    completedCount: 15,
    totalCount: 25,
    completionRate: '60%',
  },
  {
    period: '2023 Q2',
    completedCount: 22,
    totalCount: 32,
    completionRate: '68.8%',
  },
  {
    period: '2023 Q3',
    completedCount: 28,
    totalCount: 38,
    completionRate: '73.7%',
  },
  {
    period: '2023 Q4',
    completedCount: 35,
    totalCount: 45,
    completionRate: '77.8%',
  },
])
const effectivenessAnalysisData = ref([
  {
    type: '流程优化',
    effective: 12,
    partiallyEffective: 5,
    ineffective: 2,
    total: 19,
  },
  {
    type: '系统升级',
    effective: 8,
    partiallyEffective: 6,
    ineffective: 3,
    total: 17,
  },
  {
    type: '培训教育',
    effective: 15,
    partiallyEffective: 3,
    ineffective: 1,
    total: 19,
  },
  {
    type: '政策更新',
    effective: 10,
    partiallyEffective: 2,
    ineffective: 0,
    total: 12,
  },
])
const priorityTasks = ref([
  {
    urgency: '高',
    name: '数据安全漏洞修复',
    type: '系统升级',
    department: '技术部',
    owner: '张明',
    endDate: '2023-12-15',
    daysLeft: 5,
    progress: 45,
  },
  {
    urgency: '高',
    name: '反洗钱系统升级',
    type: '系统升级',
    department: '合规部',
    owner: '周涛',
    endDate: '2024-02-15',
    daysLeft: 72,
    progress: 20,
  },
  {
    urgency: '中',
    name: '客户投诉处理流程优化',
    type: '流程优化',
    department: '市场部',
    owner: '王芳',
    endDate: '2023-12-31',
    daysLeft: 21,
    progress: 30,
  },
  {
    urgency: '中',
    name: '合同管理系统优化',
    type: '系统升级',
    department: '法务部',
    owner: '吴刚',
    endDate: '2023-12-31',
    daysLeft: 21,
    progress: 60,
  },
  {
    urgency: '低',
    name: '员工合规意识培训',
    type: '培训教育',
    department: '人力资源',
    owner: '陈静',
    endDate: '2023-11-15',
    daysLeft: 0,
    progress: 75,
  },
])
export default {
  setup() {
    const sourceChart = ref<HTMLElement>()
    const departmentChart = ref<HTMLElement>()
    const trendChart = ref<HTMLElement>()
    const effectivenessChart = ref<HTMLElement>()
    const initSourceChart = () => {
      if (!sourceChart.value) { return }
      const chart = echarts.init(sourceChart.value)
      const option = {
        animation: false,
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: analysisData.value.map(item => item.source),
        },
        series: [
          {
            name: '来源分布',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: analysisData.value.map(item => ({
              value: item.count,
              name: item.source,
            })),
          },
        ],
      }
      chart.setOption(option)
    }
    const initDepartmentChart = () => {
      if (!departmentChart.value) { return }
      const chart = echarts.init(departmentChart.value)
      const option = {
        animation: false,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        legend: {
          data: ['未开始', '进行中', '已完成', '已逾期'],
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'value',
        },
        yAxis: {
          type: 'category',
          data: departmentAnalysisData.value.map(item => item.department),
        },
        series: [
          {
            name: '未开始',
            type: 'bar',
            stack: 'total',
            label: {
              show: true,
            },
            emphasis: {
              focus: 'series',
            },
            data: departmentAnalysisData.value.map(item => item.notStarted),
          },
          {
            name: '进行中',
            type: 'bar',
            stack: 'total',
            label: {
              show: true,
            },
            emphasis: {
              focus: 'series',
            },
            data: departmentAnalysisData.value.map(item => item.inProgress),
          },
          {
            name: '已完成',
            type: 'bar',
            stack: 'total',
            label: {
              show: true,
            },
            emphasis: {
              focus: 'series',
            },
            data: departmentAnalysisData.value.map(item => item.completed),
          },
          {
            name: '已逾期',
            type: 'bar',
            stack: 'total',
            label: {
              show: true,
            },
            emphasis: {
              focus: 'series',
            },
            data: departmentAnalysisData.value.map(item => item.overdue),
          },
        ],
      }
      chart.setOption(option)
    }
    const initTrendChart = () => {
      if (!trendChart.value) { return }
      const chart = echarts.init(trendChart.value)
      const option = {
        animation: false,
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          data: ['完成数量', '总数量'],
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: trendAnalysisData.value.map(item => item.period),
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            name: '完成数量',
            type: 'line',
            stack: 'total',
            data: trendAnalysisData.value.map(item => item.completedCount),
          },
          {
            name: '总数量',
            type: 'line',
            stack: 'total',
            data: trendAnalysisData.value.map(item => item.totalCount),
          },
        ],
      }
      chart.setOption(option)
    }
    const initEffectivenessChart = () => {
      if (!effectivenessChart.value) { return }
      const chart = echarts.init(effectivenessChart.value)
      const option = {
        animation: false,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        legend: {
          data: ['有效', '部分有效', '无效'],
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: effectivenessAnalysisData.value.map(item => item.type),
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            name: '有效',
            type: 'bar',
            stack: 'total',
            label: {
              show: true,
              position: 'inside',
            },
            emphasis: {
              focus: 'series',
            },
            data: effectivenessAnalysisData.value.map(item => item.effective),
          },
          {
            name: '部分有效',
            type: 'bar',
            stack: 'total',
            label: {
              show: true,
              position: 'inside',
            },
            emphasis: {
              focus: 'series',
            },
            data: effectivenessAnalysisData.value.map(item => item.partiallyEffective),
          },
          {
            name: '无效',
            type: 'bar',
            stack: 'total',
            label: {
              show: true,
              position: 'inside',
            },
            emphasis: {
              focus: 'series',
            },
            data: effectivenessAnalysisData.value.map(item => item.ineffective),
          },
        ],
      }
      chart.setOption(option)
    }
    onMounted(() => {
      nextTick(() => {
        initSourceChart()
        initDepartmentChart()
        initTrendChart()
        initEffectivenessChart()
      })
    })
    watch(activeAnalysisTab, (newVal) => {
      nextTick(() => {
        if (newVal === 'source') { initSourceChart() }
        if (newVal === 'department') { initDepartmentChart() }
        if (newVal === 'trend') { initTrendChart() }
        if (newVal === 'effectiveness') { initEffectivenessChart() }
      })
    })
    watch(trendViewType, () => {
      if (activeAnalysisTab.value === 'trend') {
        initTrendChart()
      }
    })
    return {
      sourceChart,
      departmentChart,
      trendChart,
      effectivenessChart,
      activeAnalysisTab,
      trendViewType,
    }
  },
}
</script>

<template>
  <div class="flex flex-1 flex-col overflow-hidden">
    <!-- 主内容 -->
    <div class="flex-1 overflow-y-auto p-6">
      <!-- 顶部操作区 -->
      <div class="mb-6 flex items-center justify-between">
        <h1 class="text-xl text-gray-800 font-bold">
          改进措施管理
        </h1>
        <div class="flex space-x-3">
          <button v-auth="['improvementMeasures/index/addMeasure']" class="!rounded-button whitespace-nowrap bg-blue-600 px-4 py-2 text-sm text-white hover:bg-blue-700">
            <el-icon class="mr-1">
              <Plus />
            </el-icon>新增改进措施
          </button>
          <button
            v-auth="['improvementMeasures/index/batchImport']"
            class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-sm text-blue-500 hover:bg-blue-50"
          >
            批量导入
          </button>
          <button
            v-auth="['improvementMeasures/index/export']"
            class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-sm text-blue-500 hover:bg-blue-50"
          >
            导出
          </button>
          <button
            v-auth="['improvementMeasures/index/statisticalAnalysis']"
            class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-sm text-blue-500 hover:bg-blue-50"
          >
            统计分析
          </button>
        </div>
      </div>
      <!-- 统计卡片区 -->
      <div class="grid grid-cols-5 mb-6 gap-4">
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-500">
                总措施数
              </p>
              <p class="mt-1 text-2xl font-bold">
                128
              </p>
            </div>
            <div class="h-10 w-10 flex items-center justify-center rounded-full bg-blue-100">
              <el-icon class="text-blue-500">
                <Document />
              </el-icon>
            </div>
          </div>
          <div class="mt-2 flex items-center text-xs text-green-500">
            <el-icon class="mr-1">
              <Top />
            </el-icon>12% 同比上升
          </div>
        </div>
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-500">
                未开始数
              </p>
              <p class="mt-1 text-2xl font-bold">
                24
              </p>
            </div>
            <div class="h-10 w-10 flex items-center justify-center rounded-full bg-gray-100">
              <el-icon class="text-gray-500">
                <Clock />
              </el-icon>
            </div>
          </div>
          <div class="mt-2 flex items-center text-xs text-green-500">
            <el-icon class="mr-1">
              <Bottom />
            </el-icon>8% 同比下降
          </div>
        </div>
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-500">
                进行中数
              </p>
              <p class="mt-1 text-2xl font-bold">
                68
              </p>
            </div>
            <div class="h-10 w-10 flex items-center justify-center rounded-full bg-blue-100">
              <el-icon class="text-blue-500">
                <Loading />
              </el-icon>
            </div>
          </div>
          <div class="mt-2 flex items-center text-xs text-green-500">
            <el-icon class="mr-1">
              <Top />
            </el-icon>15% 同比上升
          </div>
        </div>
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-500">
                已完成数
              </p>
              <p class="mt-1 text-2xl font-bold">
                32
              </p>
            </div>
            <div class="h-10 w-10 flex items-center justify-center rounded-full bg-green-100">
              <el-icon class="text-green-500">
                <CircleCheck />
              </el-icon>
            </div>
          </div>
          <div class="mt-2 flex items-center text-xs text-green-500">
            <el-icon class="mr-1">
              <Top />
            </el-icon>5% 同比上升
          </div>
        </div>
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-500">
                已逾期数
              </p>
              <p class="mt-1 text-2xl font-bold">
                4
              </p>
            </div>
            <div class="h-10 w-10 flex items-center justify-center rounded-full bg-red-100">
              <el-icon class="text-red-500">
                <Warning />
              </el-icon>
            </div>
          </div>
          <div class="mt-2 flex items-center text-xs text-red-500">
            <el-icon class="mr-1">
              <Bottom />
            </el-icon>20% 同比下降
          </div>
        </div>
      </div>
      <!-- 综合统计 -->
      <div class="grid grid-cols-3 mb-6 gap-4">
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <p class="mb-2 text-sm text-gray-500">
            总体完成率
          </p>
          <div class="h-2 rounded-full bg-gray-200">
            <div class="h-2 rounded-full bg-blue-500" style="width: 65%;" />
          </div>
          <p class="mt-1 text-right text-sm">
            65%
          </p>
        </div>
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <p class="mb-2 text-sm text-gray-500">
            平均完成时间
          </p>
          <p class="text-xl font-bold">
            23 <span class="text-sm text-gray-500 font-normal">天</span>
          </p>
        </div>
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <p class="mb-2 text-sm text-gray-500">
            有效率
          </p>
          <p class="text-xl font-bold">
            82%
          </p>
        </div>
      </div>
      <!-- 快捷访问区 -->
      <div class="grid grid-cols-2 mb-6 gap-4">
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <div class="mb-4 flex items-center justify-between">
            <h3 class="font-medium">
              我负责的措施
            </h3>
            <a v-auth="['improvementMeasures/index/viewAllMyResponsible']" href="#" class="text-sm text-blue-500">查看全部</a>
          </div>
          <div class="space-y-3">
            <div class="border-b pb-3">
              <div class="flex items-center justify-between">
                <p class="font-medium">
                  数据安全漏洞修复
                </p>
                <span class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-600">进行中</span>
              </div>
              <p class="mt-1 text-xs text-gray-500">
                截止日期: 2023-12-15
              </p>
              <div class="mt-2">
                <div class="h-1 rounded-full bg-gray-200">
                  <div class="h-1 rounded-full bg-blue-500" style="width: 45%;" />
                </div>
                <p class="mt-1 text-right text-xs">
                  45%
                </p>
              </div>
            </div>
            <div class="border-b pb-3">
              <div class="flex items-center justify-between">
                <p class="font-medium">
                  合规培训计划更新
                </p>
                <span class="rounded bg-green-100 px-2 py-1 text-xs text-green-600">已完成</span>
              </div>
              <p class="mt-1 text-xs text-gray-500">
                截止日期: 2023-11-30
              </p>
              <div class="mt-2">
                <div class="h-1 rounded-full bg-gray-200">
                  <div class="h-1 rounded-full bg-green-500" style="width: 100%;" />
                </div>
                <p class="mt-1 text-right text-xs">
                  100%
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <div class="mb-4 flex items-center justify-between">
            <h3 class="font-medium">
              我参与的措施
            </h3>
            <a v-auth="['improvementMeasures/index/viewAllMyParticipated']" href="#" class="text-sm text-blue-500">查看全部</a>
          </div>
          <div class="space-y-3">
            <div class="border-b pb-3">
              <div class="flex items-center justify-between">
                <p class="font-medium">
                  客户投诉处理流程优化
                </p>
                <span class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-600">进行中</span>
              </div>
              <p class="mt-1 text-xs text-gray-500">
                负责人: 张明
              </p>
            </div>
            <div class="border-b pb-3">
              <div class="flex items-center justify-between">
                <p class="font-medium">
                  内部审计系统升级
                </p>
                <span class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-600">未开始</span>
              </div>
              <p class="mt-1 text-xs text-gray-500">
                负责人: 李华
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- 筛选区 -->
      <div class="mb-6 rounded-lg bg-white p-4 shadow-sm">
        <div class="flex items-center space-x-3">
          <el-select placeholder="改进状态" size="small" class="w-32">
            <el-option label="全部" value="all" />
            <el-option label="未开始" value="not-started" />
            <el-option label="进行中" value="in-progress" />
            <el-option label="已完成" value="completed" />
            <el-option label="已逾期" value="overdue" />
          </el-select>
          <el-select placeholder="改进类型" size="small" class="w-32">
            <el-option label="全部" value="all" />
            <el-option label="流程优化" value="process" />
            <el-option label="系统升级" value="system" />
            <el-option label="培训教育" value="training" />
            <el-option label="政策更新" value="policy" />
          </el-select>
          <el-select placeholder="责任部门" size="small" class="w-32">
            <el-option label="全部" value="all" />
            <el-option label="合规部" value="compliance" />
            <el-option label="技术部" value="tech" />
            <el-option label="市场部" value="marketing" />
            <el-option label="人力资源" value="hr" />
          </el-select>
          <el-date-picker
            type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            size="small" class="w-64"
          />
          <div class="relative flex-1">
            <input
              type="text" placeholder="搜索措施名称或编号..."
              class="w-full rounded-full border-none bg-gray-100 py-1 pl-8 pr-4 text-sm focus:outline-none focus:ring-2 focus:ring-blue-200"
            >
            <el-icon class="absolute left-2 top-1.5 text-gray-400">
              <Search />
            </el-icon>
          </div>
          <button v-auth="['improvementMeasures/index/advancedFilter']" class="text-sm text-blue-500">
            高级筛选
          </button>
        </div>
      </div>
      <!-- 改进措施列表区 -->
      <div class="rounded-lg bg-white p-4 shadow-sm">
        <div class="mb-4 flex items-center justify-between">
          <div class="flex space-x-2">
            <button
              v-auth="['improvementMeasures/index/tableView']"
              :class="{ 'bg-blue-500 text-white': viewType === 'table', 'border': viewType !== 'table' }"
              class="rounded px-3 py-1 text-sm"
              @click="viewType = 'table'"
            >
              表格视图
            </button>
            <button
              v-auth="['improvementMeasures/index/cardView']"
              :class="{ 'bg-blue-500 text-white': viewType === 'card', 'border': viewType !== 'card' }"
              class="rounded px-3 py-1 text-sm"
              @click="viewType = 'card'"
            >
              卡片视图
            </button>
            <button
              v-auth="['improvementMeasures/index/ganttView']"
              :class="{ 'bg-blue-500 text-white': viewType === 'gantt', 'border': viewType !== 'gantt' }"
              class="rounded px-3 py-1 text-sm"
              @click="viewType = 'gantt'"
            >
              甘特图
            </button>
          </div>
          <div class="text-sm text-gray-500">
            共 128 条记录
          </div>
        </div>
        <!-- 表格视图 -->
        <el-table v-if="viewType === 'table'" :data="tableData" style="width: 100%;">
          <!-- 卡片视图 -->
          <div v-if="viewType === 'card'" class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
            <div
              v-for="item in tableData" :key="item.id"
              class="flex flex-col border border-gray-100 rounded-lg bg-white p-4 shadow-sm transition-shadow hover:shadow-md"
            >
              <div class="mb-3 flex items-start justify-between">
                <h4 class="text-gray-800 font-medium">
                  {{ item.name }}
                </h4>
                <span
                  :class="{
                    'bg-red-100 text-red-800': item.priority === '高',
                    'bg-orange-100 text-orange-800': item.priority === '中',
                    'bg-green-100 text-green-800': item.priority === '低',
                  }" class="rounded px-2 py-1 text-xs"
                >
                  {{ item.priority }}
                </span>
              </div>
              <div class="mb-2 flex items-center text-sm text-gray-600">
                <span class="mr-2 rounded bg-gray-100 px-2 py-1 text-xs text-gray-800">{{ item.type }}</span>
                <span>{{ item.department }} · {{ item.owner }}</span>
              </div>
              <div class="mb-3">
                <div class="mb-1 flex items-center justify-between text-xs text-gray-500">
                  <span>进度</span>
                  <span>{{ item.progress }}%</span>
                </div>
                <div class="h-1 rounded-full bg-gray-200">
                  <div
                    :class="{
                      'bg-blue-500': item.status === '进行中',
                      'bg-green-500': item.status === '已完成',
                      'bg-gray-500': item.status === '未开始',
                      'bg-red-500': item.status === '已逾期',
                    }" class="h-1 rounded-full" :style="`width: ${item.progress}%`"
                  />
                </div>
              </div>
              <div class="mt-auto">
                <div class="mb-3 flex items-center justify-between text-sm">
                  <div>
                    <span class="text-gray-500">截止日期:</span>
                    <span class="ml-1">{{ item.endDate }}</span>
                    <span
                      :class="{
                        'text-red-500': getDaysLeft(item.endDate) <= 7,
                        'text-orange-500': getDaysLeft(item.endDate) > 7 && getDaysLeft(item.endDate) <= 14,
                        'text-gray-500': getDaysLeft(item.endDate) > 14,
                      }" class="ml-2"
                    >
                      (剩余{{ getDaysLeft(item.endDate) }}天)
                    </span>
                  </div>
                  <span
                    :class="{
                      'bg-gray-100 text-gray-800': item.status === '未开始',
                      'bg-blue-100 text-blue-800': item.status === '进行中',
                      'bg-green-100 text-green-800': item.status === '已完成',
                      'bg-red-100 text-red-800': item.status === '已逾期',
                    }" class="rounded px-2 py-1 text-xs"
                  >
                    {{ item.status }}
                  </span>
                </div>
                <div class="flex items-center justify-between border-t pt-3">
                  <el-button v-auth="['improvementMeasures/index/view']" type="text" size="small" class="text-blue-500">
                    <el-icon class="mr-1">
                      <view />
                    </el-icon>查看
                  </el-button>
                  <el-button v-auth="['improvementMeasures/index/edit']" type="text" size="small" class="text-green-500">
                    <el-icon class="mr-1">
                      <edit />
                    </el-icon>编辑
                  </el-button>
                  <el-button v-auth="['improvementMeasures/index/updateProgress']" type="text" size="small" class="text-orange-500" @click="showUpdateDialog(item)">
                    <el-icon class="mr-1">
                      <Upload />
                    </el-icon>更新
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <!-- 甘特图视图 -->
          <div v-if="viewType === 'gantt'" class="rounded-lg bg-white p-4">
            <div class="flex overflow-x-auto">
              <!-- 左侧措施列表 -->
              <div class="w-64 flex-shrink-0 border-r">
                <div class="sticky top-0 z-10 border-b bg-white p-2 font-medium">
                  改进措施
                </div>
                <div
                  v-for="item in tableData" :key="item.id" class="cursor-pointer border-b p-2 hover:bg-gray-50"
                  draggable="true" @dragstart="handleDragStart(item)"
                >
                  <div class="text-sm font-medium">
                    {{ item.name }}
                  </div>
                  <div class="text-xs text-gray-500">
                    {{ item.owner }}
                  </div>
                </div>
              </div>
              <!-- 右侧时间线 -->
              <div class="min-w-0 flex-1 overflow-x-auto">
                <div class="sticky top-0 z-10 bg-white">
                  <div class="flex border-b">
                    <div
                      v-for="day in ganttDays" :key="day.date"
                      class="w-16 flex-shrink-0 border-r p-2 text-center text-xs" :class="{ 'bg-blue-50': day.isToday }"
                    >
                      <div>{{ day.day }}</div>
                      <div>{{ day.date }}</div>
                    </div>
                  </div>
                </div>
                <div class="relative" style="height: 600px;">
                  <div
                    v-for="item in tableData" :key="item.id" class="absolute h-8" :style="{
                      top: `${(tableData.indexOf(item) * 40) + 10}px`,
                      left: `${getGanttPosition(item.startDate)}px`,
                      width: `${getGanttWidth(item.startDate, item.endDate)}px`,
                    }" @drop="handleDrop(item.startDate)" @dragover.prevent
                  >
                    <div
                      class="gantt-bar h-full flex items-center rounded px-2 text-xs" :class="{
                        'bg-blue-500': item.status === '进行中',
                        'bg-green-500': item.status === '已完成',
                        'bg-gray-500': item.status === '未开始',
                        'bg-red-500': item.status === '已逾期',
                      }"
                    >
                      {{ item.name }} ({{ item.progress }}%)
                    </div>
                  </div>
                  <!-- 当前时间线 -->
                  <div
                    class="absolute bottom-0 top-0 z-10 w-0.5 bg-red-500"
                    :style="{ left: `${getGanttPosition(new Date().toISOString().split('T')[0])}px` }"
                  >
                    <div class="absolute text-xs text-red-500 -left-1.5 -top-3">
                      今天
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <el-table-column type="selection" width="40" />
          <el-table-column prop="id" label="措施编号" width="100" />
          <el-table-column prop="name" label="措施名称" width="180" />
          <el-table-column prop="type" label="措施类型" width="120">
            <template #default="{ row }">
              <span class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-800">{{ row.type }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="{ row }">
              <span v-if="row.priority === '高'" class="rounded bg-red-100 px-2 py-1 text-xs text-red-800">高</span>
              <span
                v-else-if="row.priority === '中'"
                class="rounded bg-orange-100 px-2 py-1 text-xs text-orange-800"
              >中</span>
              <span v-else class="rounded bg-green-100 px-2 py-1 text-xs text-green-800">低</span>
            </template>
          </el-table-column>
          <el-table-column prop="source" label="来源" width="120" />
          <el-table-column prop="department" label="责任部门" width="120" />
          <el-table-column prop="owner" label="负责人" width="120" />
          <el-table-column prop="startDate" label="开始日期" width="120" />
          <el-table-column prop="endDate" label="计划完成日期" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <span v-if="row.status === '未开始'" class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-800">未开始</span>
              <span
                v-else-if="row.status === '进行中'"
                class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-800"
              >进行中</span>
              <span
                v-else-if="row.status === '已完成'"
                class="rounded bg-green-100 px-2 py-1 text-xs text-green-800"
              >已完成</span>
              <span v-else class="rounded bg-red-100 px-2 py-1 text-xs text-red-800">已逾期</span>
            </template>
          </el-table-column>
          <el-table-column prop="progress" label="进度" width="120">
            <template #default="{ row }">
              <div class="h-1 rounded-full bg-gray-200">
                <div class="h-1 rounded-full bg-blue-500" :style="`width: ${row.progress}%`" />
              </div>
              <p class="mt-1 text-right text-xs">
                {{ row.progress }}%
              </p>
            </template>
          </el-table-column>
          <el-table-column prop="result" label="评估结果" width="120">
            <template #default="{ row }">
              <span v-if="row.result === '有效'" class="rounded bg-green-100 px-2 py-1 text-xs text-green-800">有效</span>
              <span
                v-else-if="row.result === '部分有效'"
                class="rounded bg-yellow-100 px-2 py-1 text-xs text-yellow-800"
              >部分有效</span>
              <span v-else-if="row.result === '无效'" class="rounded bg-red-100 px-2 py-1 text-xs text-red-800">无效</span>
              <span v-else class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-800">待评估</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default>
              <el-button v-auth="['improvementMeasures/index/view']" type="text" size="small">
                查看
              </el-button>
              <el-button v-auth="['improvementMeasures/index/updateProgress']" type="text" size="small" @click="showUpdateDialog(item)">
                更新进度
              </el-button>
              <el-dropdown>
                <el-button type="text" size="small">
                  更多<el-icon class="el-icon--right">
                    <ArrowDown />
                  </el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-auth="['improvementMeasures/index/edit']">编辑</el-dropdown-item>
                    <el-dropdown-item v-auth="['improvementMeasures/index/evaluate']">评估</el-dropdown-item>
                    <el-dropdown-item v-auth="['improvementMeasures/index/delete']">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <div class="mt-4 flex items-center justify-between">
          <div class="text-sm text-gray-500">
            显示 1 到 10 条，共 128 条
          </div>
          <el-pagination :current-page="1" :page-size="10" :total="128" layout="prev, pager, next" />
        </div>
        <!-- 更新进度弹窗 -->
        <el-dialog v-model="updateDialogVisible" title="改进进度更新" width="600px">
          <div class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="text-sm text-gray-500">
                  措施名称
                </p>
                <p class="font-medium">
                  {{ currentItem?.name }}
                </p>
              </div>
              <div>
                <p class="text-sm text-gray-500">
                  责任部门
                </p>
                <p class="font-medium">
                  {{ currentItem?.department }}
                </p>
              </div>
              <div>
                <p class="text-sm text-gray-500">
                  负责人
                </p>
                <p class="font-medium">
                  {{ currentItem?.owner }}
                </p>
              </div>
              <div>
                <p class="text-sm text-gray-500">
                  当前状态
                </p>
                <p class="font-medium">
                  {{ currentItem?.status }}
                </p>
              </div>
            </div>
            <div>
              <p class="mb-2 text-sm text-gray-500">
                当前进度
              </p>
              <div class="flex items-center space-x-4">
                <el-slider v-model="updateForm.progress" :step="5" show-stops />
                <span class="w-12 text-center">{{ updateForm.progress }}%</span>
              </div>
            </div>
            <div>
              <p class="mb-2 text-sm text-gray-500">
                进度说明
              </p>
              <el-input v-model="updateForm.progressNote" type="textarea" :rows="3" placeholder="请简要描述当前完成的工作内容" />
            </div>
            <div>
              <p class="mb-2 text-sm text-gray-500">
                遇到的问题
              </p>
              <el-input v-model="updateForm.problems" type="textarea" :rows="3" placeholder="请描述在实施过程中遇到的问题" />
            </div>
            <div>
              <p class="mb-2 text-sm text-gray-500">
                解决方案
              </p>
              <el-input v-model="updateForm.solutions" type="textarea" :rows="3" placeholder="请描述针对问题的解决方案或计划" />
            </div>
            <div>
              <p class="mb-2 text-sm text-gray-500">
                上传附件
              </p>
              <el-upload
                v-model:file-list="updateForm.attachments" action="#" multiple :limit="3"
                :on-exceed="handleExceed" :auto-upload="false"
              >
                <el-button v-auth="['improvementMeasures/index/uploadFile']" type="primary" size="small">
                  <el-icon class="mr-1">
                    <Upload />
                  </el-icon>选择文件
                </el-button>
                <template #tip>
                  <div class="el-upload__tip text-xs text-gray-500">
                    支持上传文档、图片等附件，单个文件不超过10MB
                  </div>
                </template>
              </el-upload>
            </div>
          </div>
          <template #footer>
            <div class="flex justify-end space-x-3">
              <el-button v-auth="['improvementMeasures/index/cancelUpdate']" @click="updateDialogVisible = false">
                取消
              </el-button>
              <el-button v-auth="['improvementMeasures/index/submitUpdate']" type="primary" @click="handleUpdateSubmit">
                提交
              </el-button>
            </div>
          </template>
        </el-dialog>
      </div>
    </div>
    <!-- 右侧面板 -->
    <div class="w-80 flex-shrink-0 overflow-y-auto border-l bg-gray-50 p-4 space-y-4">
      <!-- 措施类型筛选 -->
      <div class="rounded-lg bg-white p-4 shadow-sm">
        <h3 class="mb-4 text-base font-bold">
          措施类型
        </h3>
        <div class="space-y-3">
          <el-checkbox v-model="filterTypes.process">
            流程改进
          </el-checkbox>
          <el-checkbox v-model="filterTypes.system">
            制度改进
          </el-checkbox>
          <el-checkbox v-model="filterTypes.position">
            岗位改进
          </el-checkbox>
          <el-checkbox v-model="filterTypes.tech">
            系统改进
          </el-checkbox>
          <el-checkbox v-model="filterTypes.training">
            培训改进
          </el-checkbox>
          <el-checkbox v-model="filterTypes.other">
            其他
          </el-checkbox>
          <div class="pt-2">
            <a v-auth="['improvementMeasures/index/toggleAllTypes']" href="#" class="text-sm text-blue-500" @click="toggleAllTypes">全选/取消全选</a>
          </div>
        </div>
      </div>

      <!-- 改进措施模板 -->
      <div class="rounded-lg bg-white p-4 shadow-sm">
        <h3 class="mb-4 text-base font-bold">
          常用模板
        </h3>
        <div class="space-y-3">
          <div v-for="(template, index) in templates" :key="index" class="border-b pb-3">
            <h4 class="text-sm font-medium">
              {{ template.name }}
            </h4>
            <p class="mt-1 text-xs text-gray-500">
              {{ template.scenario }}
            </p>
            <el-button v-auth="['improvementMeasures/index/applyTemplate']" size="small" class="!rounded-button mt-2 whitespace-nowrap" @click="applyTemplate(template)">
              应用模板
            </el-button>
          </div>
          <div class="pt-2">
            <a v-auth="['improvementMeasures/index/manageTemplate']" href="#" class="text-sm text-blue-500">管理模板</a>
          </div>
        </div>
      </div>

      <!-- 部门完成情况 -->
      <div class="rounded-lg bg-white p-4 shadow-sm">
        <h3 class="mb-4 text-base font-bold">
          部门完成情况
        </h3>
        <el-table :data="departmentStats" style="width: 100%;" size="small">
          <el-table-column prop="department" label="部门" width="80" />
          <el-table-column prop="total" label="总数" width="60" />
          <el-table-column prop="completed" label="完成数" width="60" />
          <el-table-column label="完成率" width="100">
            <template #default="{ row }">
              <div class="h-1 rounded-full bg-gray-200">
                <div class="h-1 rounded-full bg-blue-500" :style="`width: ${row.completionRate}%`" />
              </div>
              <p class="mt-1 text-right text-xs">
                {{ row.completionRate }}%
              </p>
            </template>
          </el-table-column>
          <el-table-column label="有效率" width="100">
            <template #default="{ row }">
              <div class="h-1 rounded-full bg-gray-200">
                <div class="h-1 rounded-full bg-green-500" :style="`width: ${row.effectivenessRate}%`" />
              </div>
              <p class="mt-1 text-right text-xs">
                {{ row.effectivenessRate }}%
              </p>
            </template>
          </el-table-column>
        </el-table>
        <div class="pt-2">
          <a v-auth="['improvementMeasures/index/fullStatistics']" href="#" class="text-sm text-blue-500">完整统计</a>
        </div>
      </div>

      <!-- 人员效率排名 -->
      <div class="rounded-lg bg-white p-4 shadow-sm">
        <h3 class="mb-4 text-base font-bold">
          人员效率排名
        </h3>
        <el-table :data="efficiencyRanking" style="width: 100%;" size="small">
          <el-table-column prop="rank" label="排名" width="50" />
          <el-table-column prop="owner" label="负责人" width="80">
            <template #default="{ row }">
              <span :class="{ 'text-blue-500 font-medium': row.isCurrentUser }">{{ row.owner }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="count" label="措施数" width="60" />
          <el-table-column prop="completionRate" label="完成率" width="80" />
          <el-table-column prop="avgTime" label="平均时间" width="80" />
        </el-table>
        <div class="pt-2">
          <a v-auth="['improvementMeasures/index/fullRanking']" href="#" class="text-sm text-blue-500">完整排名</a>
        </div>
      </div>

      <!-- AI助手 -->
      <div class="rounded-lg bg-white p-4 shadow-sm">
        <h3 class="mb-4 text-base font-bold">
          AI助手
        </h3>
        <div class="space-y-3">
          <el-button v-auth="'improvementMeasures/list/analyzeDelayRisk'" class="!rounded-button w-full whitespace-nowrap" @click="analyzeDelayRisk">
            <el-icon class="mr-1">
              <MagicStick />
            </el-icon>分析延期风险
          </el-button>
          <el-button v-auth="'improvementMeasures/list/generateProgressReport'" class="!rounded-button w-full whitespace-nowrap" @click="generateProgressReport">
            <el-icon class="mr-1">
              <Document />
            </el-icon>生成进度报告
          </el-button>
          <el-button v-auth="'improvementMeasures/list/suggestPriorityMeasures'" class="!rounded-button w-full whitespace-nowrap" @click="suggestPriorityMeasures">
            <el-icon class="mr-1">
              <Star />
            </el-icon>建议优先措施
          </el-button>
          <div v-if="aiSuggestion" class="mt-3 rounded bg-blue-50 p-3 text-sm">
            {{ aiSuggestion }}
          </div>
        </div>
      </div>
    </div>

    <!-- 改进措施分析区 -->
    <div class="mt-6 rounded-lg bg-white p-4 shadow-sm">
      <h3 class="mb-4 font-bold">
        改进措施分析
      </h3>
      <div class="flex border-b">
        <div
          class="cursor-pointer px-4 py-2" :class="{ 'border-b-2 border-blue-500 text-blue-500': activeAnalysisTab === 'source' }"
          @click="activeAnalysisTab = 'source'"
        >
          来源分布
        </div>
        <div
          class="cursor-pointer px-4 py-2" :class="{ 'border-b-2 border-blue-500 text-blue-500': activeAnalysisTab === 'department' }"
          @click="activeAnalysisTab = 'department'"
        >
          部门分布
        </div>
        <div
          class="cursor-pointer px-4 py-2" :class="{ 'border-b-2 border-blue-500 text-blue-500': activeAnalysisTab === 'trend' }"
          @click="activeAnalysisTab = 'trend'"
        >
          完成趋势
        </div>
        <div
          class="cursor-pointer px-4 py-2" :class="{ 'border-b-2 border-blue-500 text-blue-500': activeAnalysisTab === 'effectiveness' }"
          @click="activeAnalysisTab = 'effectiveness'"
        >
          有效性分析
        </div>
      </div>
      <!-- 来源分布 -->
      <div v-if="activeAnalysisTab === 'source'" class="grid grid-cols-3 mt-4 gap-4">
        <div class="col-span-2">
          <div ref="sourceChart" class="h-64 w-full" />
        </div>
        <div>
          <el-table :data="analysisData" style="width: 100%;">
            <el-table-column prop="source" label="来源" />
            <el-table-column prop="count" label="数量" />
            <el-table-column prop="percentage" label="占比" />
          </el-table>
        </div>
      </div>
      <!-- 部门分布 -->
      <div v-if="activeAnalysisTab === 'department'" class="grid grid-cols-3 mt-4 gap-4">
        <div class="col-span-2">
          <div ref="departmentChart" class="h-64 w-full" />
        </div>
        <div>
          <el-table :data="departmentAnalysisData" style="width: 100%;">
            <el-table-column prop="department" label="部门" />
            <el-table-column prop="notStarted" label="未开始" />
            <el-table-column prop="inProgress" label="进行中" />
            <el-table-column prop="completed" label="已完成" />
            <el-table-column prop="overdue" label="已逾期" />
            <el-table-column prop="total" label="总计" />
          </el-table>
        </div>
      </div>
      <!-- 完成趋势 -->
      <div v-if="activeAnalysisTab === 'trend'" class="grid grid-cols-3 mt-4 gap-4">
        <div class="col-span-2">
          <div class="mb-2 flex justify-end">
            <el-radio-group v-model="trendViewType" size="small">
              <el-radio-button label="count">
                数量
              </el-radio-button>
              <el-radio-button label="rate">
                完成率
              </el-radio-button>
            </el-radio-group>
          </div>
          <div ref="trendChart" class="h-64 w-full" />
        </div>
        <div>
          <el-table :data="trendAnalysisData" style="width: 100%;">
            <el-table-column prop="period" label="时间段" />
            <el-table-column prop="completedCount" label="完成数量" />
            <el-table-column prop="totalCount" label="总数量" />
            <el-table-column prop="completionRate" label="完成率" />
          </el-table>
        </div>
      </div>
      <!-- 有效性分析 -->
      <div v-if="activeAnalysisTab === 'effectiveness'" class="grid grid-cols-3 mt-4 gap-4">
        <div class="col-span-2">
          <div ref="effectivenessChart" class="h-64 w-full" />
        </div>
        <div>
          <el-table :data="effectivenessAnalysisData" style="width: 100%;">
            <el-table-column prop="type" label="改进类型" />
            <el-table-column prop="effective" label="有效" />
            <el-table-column prop="partiallyEffective" label="部分有效" />
            <el-table-column prop="ineffective" label="无效" />
            <el-table-column prop="total" label="总计" />
          </el-table>
        </div>
      </div>
    </div>
    <!-- 优先任务区 -->
    <div class="mt-6 rounded-lg bg-white p-4 shadow-sm">
      <h3 class="mb-4 font-bold">
        优先处理任务
      </h3>
      <el-table :data="priorityTasks" style="width: 100%;">
        <el-table-column prop="urgency" label="紧急程度" width="120">
          <template #default="{ row }">
            <span v-if="row.urgency === '高'" class="rounded bg-red-100 px-2 py-1 text-xs text-red-800">高</span>
            <span
              v-else-if="row.urgency === '中'"
              class="rounded bg-orange-100 px-2 py-1 text-xs text-orange-800"
            >中</span>
            <span v-else class="rounded bg-yellow-100 px-2 py-1 text-xs text-yellow-800">低</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="措施名称" width="180" />
        <el-table-column prop="type" label="类型" width="120" />
        <el-table-column prop="department" label="责任部门" width="120" />
        <el-table-column prop="owner" label="负责人" width="120" />
        <el-table-column prop="endDate" label="截止日期" width="120" />
        <el-table-column prop="daysLeft" label="剩余天数" width="120">
          <template #default="{ row }">
            <span
              :class="{ 'text-red-500': row.daysLeft <= 7, 'text-orange-500': row.daysLeft > 7 && row.daysLeft <= 14 }"
            >
              {{ row.daysLeft }}天
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="进度" width="120">
          <template #default="{ row }">
            <div class="h-1 rounded-full bg-gray-200">
              <div class="h-1 rounded-full bg-blue-500" :style="`width: ${row.progress}%`" />
            </div>
            <p class="mt-1 text-right text-xs">
              {{ row.progress }}%
            </p>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default>
            <el-button v-auth="'improvementMeasures/list/handleImmediately'" type="text" size="small" class="text-blue-500">
              立即处理
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<style scoped>
  /* 自定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* 甘特图拖动样式 */
  .gantt-bar {
    cursor: move;
    user-select: none;
  }

  .gantt-bar:hover {
    opacity: 0.8;
  }
</style>
