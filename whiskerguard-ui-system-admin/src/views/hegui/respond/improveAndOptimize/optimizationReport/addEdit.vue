<script lang="ts" setup>
import { nextTick, reactive, ref } from 'vue'

const _activeMenu = ref('1-3-4')
const _isEdit = ref(false)
const inputTagVisible = ref(false)
const inputTagValue = ref('')
const tagInput = ref()
const activeCollapse = ref(['1', '2', '3', '4'])
const form = reactive({
  title: '',
  code: '',
  type: '',
  period: '',
  dateRange: [],
  department: '',
  creator: '张合规',
  createDate: '2023-06-15',
  secretLevel: 'normal',
  tags: ['合规', '改进', '年度报告'],
  // 报告格式设置
  template: '',
  fontFamily: '',
  fontSize: '',
  headerContent: '',
  footerContent: '',
  showPageNumber: true,
  showCover: true,
  showSignature: true,
  // 图表与数据设置
  chartTypes: [],
  dataSources: [],
  dataDateRange: [],
  dataDepartments: [],
  chartTheme: 'default',
  showLegend: true,
  showDataLabel: false,
  // 审批流程设置
  approvalProcess: 'standard',
  approvers: [
    { order: 1, role: '合规主管', approver: '李主管', required: true },
    { order: 2, role: '部门经理', approver: '王经理', required: true },
    { order: 3, role: '合规总监', approver: '张总监', required: false },
  ],
  // 通知与权限设置
  notificationRecipients: [],
  notificationMethods: ['system', 'email'],
  notificationEvents: ['submit', 'approve'],
  viewPermission: 'manager',
  specifiedViewers: [],
  exportPermission: 'approval',
  commentPermission: 'allow',
  summary: '',
  improvementOverview: '',
  resourceInput: '',
  progressOverview: '',
  mainAchievements: '',
  lessonDistribution: '',
  mainProblemAnalysis: '',
  caseStudy: '',
  causeAnalysis: '',
  measureCompletion: '',
  effectEvaluation: '',
  costBenefitAnalysis: '',
  casePresentation: '',
  historyComparison: '',
  problemTrend: '',
  improvementTrend: '',
  forecastAnalysis: '',
  currentProblems: '',
  improvementChallenges: '',
  riskAnalysis: '',
  responseStrategy: '',
  priorityAreas: '',
  specificProjects: '',
  resourcePlan: '',
  timeline: '',
  overallEvaluation: '',
  keyFindings: '',
  recommendations: '',
  outlook: '',
  attachments: [
    {
      name: '2023年度合规数据统计.xlsx',
      type: 'Excel',
      size: '2.5MB',
      uploadTime: '2023-06-10 14:30',
    },
    {
      name: '改进措施效果评估.pdf',
      type: 'PDF',
      size: '1.8MB',
      uploadTime: '2023-06-12 09:15',
    },
  ],
})
function removeTag(tag: string) {
  form.tags = form.tags.filter(t => t !== tag)
}
function showTagInput() {
  inputTagVisible.value = true
  nextTick(() => {
    tagInput.value.focus()
  })
}
function addTag() {
  if (inputTagValue.value) {
    form.tags.push(inputTagValue.value)
    inputTagValue.value = ''
  }
  inputTagVisible.value = false
}
function previewAttachment(attachment: any) {
  // 预览附件逻辑
  return attachment
}
function removeAttachment(attachment: any) {
  form.attachments = form.attachments.filter(a => a.name !== attachment.name)
}
function addApprover() {
  const newOrder = form.approvers.length > 0 ? Math.max(...form.approvers.map(a => a.order)) + 1 : 1
  form.approvers.push({
    order: newOrder,
    role: '',
    approver: '',
    required: false,
  })
}
function removeApprover(approver: any) {
  form.approvers = form.approvers.filter(a => a.order !== approver.order)
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              新增优化报告
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="'optimizationReport/addEdit/saveDraft'" type="primary" class="!rounded-button whitespace-nowrap">
              保存草稿
            </el-button>
            <el-button v-auth="'optimizationReport/addEdit/submitReview'" class="!rounded-button whitespace-nowrap">
              提交审核
            </el-button>
            <el-button v-auth="'optimizationReport/addEdit/cancel'" class="!rounded-button whitespace-nowrap">
              取消
            </el-button>
            <el-button v-auth="'optimizationReport/addEdit/preview'" class="!rounded-button whitespace-nowrap">
              预览
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-18 fw-800">
                  基本信息
                </div>
              </template>
              <el-form :model="form" label-width="120px" label-position="right">
                <div class="grid grid-cols-2 gap-6">
                  <el-form-item label="报告标题" required>
                    <el-input v-model="form.title" placeholder="请输入报告标题" />
                  </el-form-item>
                  <el-form-item label="报告编号">
                    <div class="flex">
                      <el-input v-model="form.code" placeholder="请输入报告编号" />
                      <el-button v-auth="'optimizationReport/addEdit/autoGenerate'" class="ml-2" type="primary" plain>
                        自动生成
                      </el-button>
                    </div>
                  </el-form-item>
                  <el-form-item label="报告类型" required>
                    <el-select v-model="form.type" placeholder="请选择报告类型" class="w-full">
                      <el-option label="月度报告" value="monthly" />
                      <el-option label="季度报告" value="quarterly" />
                      <el-option label="年度报告" value="yearly" />
                      <el-option label="专项报告" value="special" />
                      <el-option label="其他" value="other" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="报告周期" required>
                    <el-select v-model="form.period" placeholder="请选择报告周期" class="w-full">
                      <el-option label="月度" value="month" />
                      <el-option label="季度" value="quarter" />
                      <el-option label="半年度" value="half-year" />
                      <el-option label="年度" value="year" />
                      <el-option label="其他" value="other" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="覆盖时间" required>
                    <el-date-picker
                      v-model="form.dateRange" type="daterange" range-separator="至"
                      start-placeholder="开始日期" end-placeholder="结束日期" class="w-full"
                    />
                  </el-form-item>
                  <el-form-item label="编制部门">
                    <el-select v-model="form.department" placeholder="请选择编制部门" class="w-full">
                      <el-option label="合规部" value="compliance" />
                      <el-option label="财务部" value="finance" />
                      <el-option label="人力资源部" value="hr" />
                      <el-option label="信息技术部" value="it" />
                      <el-option label="市场部" value="marketing" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="编制人">
                    <el-input v-model="form.creator" disabled />
                  </el-form-item>
                  <el-form-item label="编制日期">
                    <el-input v-model="form.createDate" disabled />
                  </el-form-item>
                  <el-form-item label="保密级别">
                    <el-radio-group v-model="form.secretLevel">
                      <el-radio label="normal">
                        普通
                      </el-radio>
                      <el-radio label="internal">
                        内部
                      </el-radio>
                      <el-radio label="secret">
                        保密
                      </el-radio>
                      <el-radio label="confidential">
                        机密
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="报告标签">
                    <el-tag v-for="tag in form.tags" :key="tag" closable class="mr-2" @close="removeTag(tag)">
                      {{ tag }}
                    </el-tag>
                    <el-input
                      v-if="inputTagVisible" ref="tagInput" v-model="inputTagValue" size="small" class="w-28"
                      @keyup.enter="addTag" @blur="addTag"
                    />
                    <el-button v-else v-auth="'optimizationReport/addEdit/addTag'" size="small" @click="showTagInput">
                      + 添加标签
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-18 fw-800">
                  报告摘要
                </div>
              </template>
              <el-input
                v-model="form.summary" type="textarea" :rows="6" placeholder="请输入报告摘要，建议300-500字"
                show-word-limit maxlength="500"
              />
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-18 fw-800">
                    一、改进概况
                  </div>
                  <el-button v-auth="'optimizationReport/addEdit/importImprovementData'" type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                    导入改进数据
                  </el-button>
                </div>
              </template>
              <el-collapse v-model="activeCollapse">
                <el-collapse-item title="报告期内改进工作概述" name="1">
                  <el-input v-model="form.improvementOverview" type="textarea" :rows="4" placeholder="请输入改进工作概述" />
                </el-collapse-item>
                <el-collapse-item title="改进资源投入" name="2">
                  <el-input v-model="form.resourceInput" type="textarea" :rows="4" placeholder="请输入改进资源投入情况" />
                </el-collapse-item>
                <el-collapse-item title="改进进度总览" name="3">
                  <el-input v-model="form.progressOverview" type="textarea" :rows="4" placeholder="请输入改进进度总览" />
                </el-collapse-item>
                <el-collapse-item title="主要成果一览" name="4">
                  <el-input v-model="form.mainAchievements" type="textarea" :rows="4" placeholder="请输入主要成果一览" />
                </el-collapse-item>
              </el-collapse>
            </el-card>

            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-18 fw-800">
                    二、经验教训分析
                  </div>
                  <div class="flex space-x-2">
                    <el-button v-auth="'optimizationReport/addEdit/selectLessons'" type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                      选择经验教训
                    </el-button>
                    <el-button v-auth="'optimizationReport/addEdit/importAnalysisData'" type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                      导入分析数据
                    </el-button>
                  </div>
                </div>
              </template>
              <el-collapse v-model="activeCollapse">
                <el-collapse-item title="经验教训分布情况" name="5">
                  <el-input v-model="form.lessonDistribution" type="textarea" :rows="4" placeholder="请输入经验教训分布情况" />
                </el-collapse-item>
                <el-collapse-item title="主要问题分析" name="6">
                  <el-input v-model="form.mainProblemAnalysis" type="textarea" :rows="4" placeholder="请输入主要问题分析" />
                </el-collapse-item>
                <el-collapse-item title="典型案例介绍" name="7">
                  <el-input v-model="form.caseStudy" type="textarea" :rows="4" placeholder="请输入典型案例介绍" />
                </el-collapse-item>
                <el-collapse-item title="原因归类分析" name="8">
                  <el-input v-model="form.causeAnalysis" type="textarea" :rows="4" placeholder="请输入原因归类分析" />
                </el-collapse-item>
              </el-collapse>
            </el-card>

            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-18 fw-800">
                    三、改进措施效果
                  </div>
                  <div class="flex space-x-2">
                    <el-button v-auth="'optimizationReport/addEdit/selectMeasures'" type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                      选择改进措施
                    </el-button>
                    <el-button v-auth="'optimizationReport/addEdit/importEffectData'" type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                      导入效果数据
                    </el-button>
                  </div>
                </div>
              </template>
              <el-collapse v-model="activeCollapse">
                <el-collapse-item title="措施完成情况" name="9">
                  <el-input v-model="form.measureCompletion" type="textarea" :rows="4" placeholder="请输入措施完成情况" />
                </el-collapse-item>
                <el-collapse-item title="效果评估结果" name="10">
                  <el-input v-model="form.effectEvaluation" type="textarea" :rows="4" placeholder="请输入效果评估结果" />
                </el-collapse-item>
                <el-collapse-item title="成本收益分析" name="11">
                  <el-input v-model="form.costBenefitAnalysis" type="textarea" :rows="4" placeholder="请输入成本收益分析" />
                </el-collapse-item>
                <el-collapse-item title="典型案例展示" name="12">
                  <el-input v-model="form.casePresentation" type="textarea" :rows="4" placeholder="请输入典型案例展示" />
                </el-collapse-item>
              </el-collapse>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-18 fw-800">
                    四、趋势分析
                  </div>
                  <div class="flex space-x-2">
                    <el-button v-auth="'optimizationReport/addEdit/importTrendCharts'" type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                      导入趋势图表
                    </el-button>
                    <el-button v-auth="'optimizationReport/addEdit/generatePrediction'" type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                      生成预测分析
                    </el-button>
                  </div>
                </div>
              </template>
              <el-collapse v-model="activeCollapse">
                <el-collapse-item title="历史对比分析" name="13">
                  <el-input v-model="form.historyComparison" type="textarea" :rows="4" placeholder="请输入历史对比分析" />
                </el-collapse-item>
                <el-collapse-item title="问题趋势变化" name="14">
                  <el-input v-model="form.problemTrend" type="textarea" :rows="4" placeholder="请输入问题趋势变化" />
                </el-collapse-item>
                <el-collapse-item title="改进效果趋势" name="15">
                  <el-input v-model="form.improvementTrend" type="textarea" :rows="4" placeholder="请输入改进效果趋势" />
                </el-collapse-item>
                <el-collapse-item title="预测分析" name="16">
                  <el-input v-model="form.forecastAnalysis" type="textarea" :rows="4" placeholder="请输入预测分析" />
                </el-collapse-item>
              </el-collapse>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-18 fw-800">
                    五、问题与挑战
                  </div>
                </div>
              </template>
              <el-collapse v-model="activeCollapse">
                <el-collapse-item title="当前存在的主要问题" name="17">
                  <el-input v-model="form.currentProblems" type="textarea" :rows="4" placeholder="请输入当前存在的主要问题" />
                </el-collapse-item>
                <el-collapse-item title="改进过程中的挑战" name="18">
                  <el-input v-model="form.improvementChallenges" type="textarea" :rows="4" placeholder="请输入改进过程中的挑战" />
                </el-collapse-item>
                <el-collapse-item title="风险点分析" name="19">
                  <el-input v-model="form.riskAnalysis" type="textarea" :rows="4" placeholder="请输入风险点分析" />
                </el-collapse-item>
                <el-collapse-item title="应对策略" name="20">
                  <el-input v-model="form.responseStrategy" type="textarea" :rows="4" placeholder="请输入应对策略" />
                </el-collapse-item>
              </el-collapse>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-18 fw-800">
                    六、后续计划
                  </div>
                  <el-button v-auth="'optimizationReport/addEdit/importPlanTasks'" type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                    导入计划任务
                  </el-button>
                </div>
              </template>
              <el-collapse v-model="activeCollapse">
                <el-collapse-item title="优先改进领域" name="21">
                  <el-input v-model="form.priorityAreas" type="textarea" :rows="4" placeholder="请输入优先改进领域" />
                </el-collapse-item>
                <el-collapse-item title="具体改进项目" name="22">
                  <el-input v-model="form.specificProjects" type="textarea" :rows="4" placeholder="请输入具体改进项目" />
                </el-collapse-item>
                <el-collapse-item title="资源需求计划" name="23">
                  <el-input v-model="form.resourcePlan" type="textarea" :rows="4" placeholder="请输入资源需求计划" />
                </el-collapse-item>
                <el-collapse-item title="时间表和里程碑" name="24">
                  <el-input v-model="form.timeline" type="textarea" :rows="4" placeholder="请输入时间表和里程碑" />
                </el-collapse-item>
              </el-collapse>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-18 fw-800">
                    七、结论
                  </div>
                </div>
              </template>
              <el-collapse v-model="activeCollapse">
                <el-collapse-item title="总体评估" name="25">
                  <el-input v-model="form.overallEvaluation" type="textarea" :rows="4" placeholder="请输入总体评估" />
                </el-collapse-item>
                <el-collapse-item title="关键发现" name="26">
                  <el-input v-model="form.keyFindings" type="textarea" :rows="4" placeholder="请输入关键发现" />
                </el-collapse-item>
                <el-collapse-item title="建议事项" name="27">
                  <el-input v-model="form.recommendations" type="textarea" :rows="4" placeholder="请输入建议事项" />
                </el-collapse-item>
                <el-collapse-item title="展望" name="28">
                  <el-input v-model="form.outlook" type="textarea" :rows="4" placeholder="请输入展望" />
                </el-collapse-item>
              </el-collapse>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-18 fw-800">
                  报告格式设置
                </div>
              </template>
              <el-form :model="form" label-width="120px" label-position="right">
                <div class="grid grid-cols-2 gap-6">
                  <el-form-item label="报告模板">
                    <el-select v-model="form.template" placeholder="请选择报告模板" class="w-full">
                      <el-option label="标准模板" value="standard" />
                      <el-option label="简洁模板" value="simple" />
                      <el-option label="详细模板" value="detailed" />
                      <el-option label="自定义模板" value="custom" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="字体设置">
                    <el-select v-model="form.fontFamily" placeholder="请选择字体" class="w-full">
                      <el-option label="宋体" value="SimSun" />
                      <el-option label="黑体" value="SimHei" />
                      <el-option label="微软雅黑" value="Microsoft YaHei" />
                      <el-option label="Arial" value="Arial" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="字号设置">
                    <el-select v-model="form.fontSize" placeholder="请选择字号" class="w-full">
                      <el-option label="小号" value="small" />
                      <el-option label="标准" value="medium" />
                      <el-option label="大号" value="large" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="页眉内容">
                    <el-input v-model="form.headerContent" placeholder="请输入页眉内容" />
                  </el-form-item>
                  <el-form-item label="页脚内容">
                    <el-input v-model="form.footerContent" placeholder="请输入页脚内容" />
                  </el-form-item>
                  <el-form-item label="显示页码">
                    <el-switch v-model="form.showPageNumber" />
                  </el-form-item>
                  <el-form-item label="显示封面">
                    <el-switch v-model="form.showCover" />
                  </el-form-item>
                  <el-form-item v-if="form.showCover" label="封面图片">
                    <el-upload action="" :auto-upload="false" :show-file-list="false">
                      <template #trigger>
                        <el-button type="primary">
                          上传封面
                        </el-button>
                      </template>
                    </el-upload>
                  </el-form-item>
                  <el-form-item v-if="form.showCover" label="显示签名区域">
                    <el-switch v-model="form.showSignature" />
                  </el-form-item>
                </div>
              </el-form>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-18 fw-800">
                  图表与数据设置
                </div>
              </template>
              <el-form :model="form" label-width="120px" label-position="right">
                <div class="grid grid-cols-2 gap-6">
                  <el-form-item label="图表类型">
                    <el-checkbox-group v-model="form.chartTypes">
                      <el-checkbox label="柱状图" value="bar" />
                      <el-checkbox label="折线图" value="line" />
                      <el-checkbox label="饼图" value="pie" />
                      <el-checkbox label="雷达图" value="radar" />
                      <el-checkbox label="散点图" value="scatter" />
                      <el-checkbox label="热力图" value="heatmap" />
                    </el-checkbox-group>
                  </el-form-item>
                  <el-form-item label="数据来源">
                    <el-checkbox-group v-model="form.dataSources">
                      <el-checkbox label="经验教训数据" value="lesson" />
                      <el-checkbox label="改进措施数据" value="measure" />
                      <el-checkbox label="评估结果数据" value="evaluation" />
                      <el-checkbox label="历史趋势数据" value="trend" />
                    </el-checkbox-group>
                  </el-form-item>
                  <el-form-item label="时间范围">
                    <el-date-picker
                      v-model="form.dataDateRange" type="daterange" range-separator="至"
                      start-placeholder="开始日期" end-placeholder="结束日期" class="w-full"
                    />
                  </el-form-item>
                  <el-form-item label="部门筛选">
                    <el-select v-model="form.dataDepartments" multiple placeholder="请选择部门" class="w-full">
                      <el-option label="合规部" value="compliance" />
                      <el-option label="财务部" value="finance" />
                      <el-option label="人力资源部" value="hr" />
                      <el-option label="信息技术部" value="it" />
                      <el-option label="市场部" value="marketing" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="颜色主题">
                    <el-select v-model="form.chartTheme" placeholder="请选择颜色主题" class="w-full">
                      <el-option label="默认主题" value="default" />
                      <el-option label="明亮主题" value="light" />
                      <el-option label="暗色主题" value="dark" />
                      <el-option label="自定义主题" value="custom" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="显示图例">
                    <el-switch v-model="form.showLegend" />
                  </el-form-item>
                  <el-form-item label="显示数据标签">
                    <el-switch v-model="form.showDataLabel" />
                  </el-form-item>
                </div>
              </el-form>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-18 fw-800">
                  审批流程设置
                </div>
              </template>
              <el-form :model="form" label-width="120px" label-position="right">
                <el-form-item label="审批流程">
                  <el-select v-model="form.approvalProcess" placeholder="请选择审批流程" class="w-full">
                    <el-option label="标准流程" value="standard" />
                    <el-option label="简化流程" value="simple" />
                    <el-option label="严格流程" value="strict" />
                    <el-option label="自定义流程" value="custom" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-table :data="form.approvers" border style="width: 100%;">
                    <el-table-column prop="order" label="审批顺序" width="120" />
                    <el-table-column prop="role" label="审批角色" width="180" />
                    <el-table-column prop="approver" label="审批人" width="180" />
                    <el-table-column prop="required" label="必选" width="100">
                      <template #default="scope">
                        <el-checkbox v-model="scope.row.required" disabled />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="120">
                      <template #default="scope">
                        <el-button v-auth="'optimizationReport/addEdit/deleteApprover'" size="small" type="danger" @click="removeApprover(scope.row)">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
                <el-form-item>
                  <el-button v-auth="'optimizationReport/addEdit/addApprover'" type="primary" class="!rounded-button whitespace-nowrap" @click="addApprover">
                    添加审批人
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-18 fw-800">
                  通知与权限设置
                </div>
              </template>
              <el-form :model="form" label-width="120px" label-position="right">
                <div class="grid grid-cols-2 gap-6">
                  <el-form-item label="通知对象">
                    <el-select
                      v-model="form.notificationRecipients" multiple placeholder="请选择需要通知的人员或部门"
                      class="w-full"
                    >
                      <el-option label="合规部" value="compliance" />
                      <el-option label="财务部" value="finance" />
                      <el-option label="人力资源部" value="hr" />
                      <el-option label="信息技术部" value="it" />
                      <el-option label="市场部" value="marketing" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="通知方式">
                    <el-checkbox-group v-model="form.notificationMethods">
                      <el-checkbox label="系统消息" value="system" />
                      <el-checkbox label="电子邮件" value="email" />
                      <el-checkbox label="短信" value="sms" />
                      <el-checkbox label="其他" value="other" />
                    </el-checkbox-group>
                  </el-form-item>
                  <el-form-item label="通知事件">
                    <el-checkbox-group v-model="form.notificationEvents">
                      <el-checkbox label="报告提交" value="submit" />
                      <el-checkbox label="报告审批" value="approve" />
                      <el-checkbox label="报告发布" value="publish" />
                      <el-checkbox label="报告更新" value="update" />
                    </el-checkbox-group>
                  </el-form-item>
                  <el-form-item label="查看权限">
                    <el-radio-group v-model="form.viewPermission">
                      <el-radio label="all">
                        全公司
                      </el-radio>
                      <el-radio label="manager">
                        部门主管
                      </el-radio>
                      <el-radio label="related">
                        改进相关人员
                      </el-radio>
                      <el-radio label="specific">
                        指定人员
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item v-if="form.viewPermission === 'specific'" label="指定查看人员">
                    <el-select v-model="form.specifiedViewers" multiple placeholder="请选择指定查看人员" class="w-full">
                      <el-option label="张合规" value="zhang" />
                      <el-option label="李主管" value="li" />
                      <el-option label="王经理" value="wang" />
                      <el-option label="张总监" value="zhangDirector" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="导出权限">
                    <el-radio-group v-model="form.exportPermission">
                      <el-radio label="allow">
                        允许
                      </el-radio>
                      <el-radio label="deny">
                        不允许
                      </el-radio>
                      <el-radio label="approval">
                        需审批
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="评论权限">
                    <el-radio-group v-model="form.commentPermission">
                      <el-radio label="allow">
                        允许
                      </el-radio>
                      <el-radio label="deny">
                        不允许
                      </el-radio>
                      <el-radio label="specific">
                        仅特定人员
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
              </el-form>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-18 fw-800">
                    八、附件
                  </div>
                  <div class="flex space-x-2">
                    <el-button v-auth="'optimizationReport/addEdit/addAttachment'" type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                      添加附件
                    </el-button>
                    <el-button v-auth="'optimizationReport/addEdit/selectFromSystem'" type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                      从系统选择
                    </el-button>
                    <el-button v-auth="'optimizationReport/addEdit/addExternalLink'" type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                      添加外部链接
                    </el-button>
                  </div>
                </div>
              </template>
              <el-table :data="form.attachments" border style="width: 100%;">
                <el-table-column prop="name" label="附件名称" width="180" />
                <el-table-column prop="type" label="类型" width="100" />
                <el-table-column prop="size" label="大小" width="100" />
                <el-table-column prop="uploadTime" label="上传时间" width="180" />
                <el-table-column label="操作" width="180">
                  <template #default="scope">
                    <el-button v-auth="'optimizationReport/addEdit/previewAttachment'" size="small" @click="previewAttachment(scope.row)">
                      预览
                    </el-button>
                    <el-button v-auth="'optimizationReport/addEdit/deleteAttachment'" size="small" type="danger" @click="removeAttachment(scope.row)">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-18 fw-800">
                  报告模板
                </div>
              </template>
              <ul class="mb-4 space-y-2">
                <li class="cursor-pointer hover:text-blue-500">
                  标准模板
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  简洁模板
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  详细模板
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  年度报告模板
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  专项报告模板
                </li>
              </ul>
              <el-button
                v-auth="'optimizationReport/addEdit/saveAsTemplate'" type="primary" size="small"
                class="!rounded-button mb-2 w-full whitespace-nowrap"
              >
                保存为模板
              </el-button>
              <el-link v-auth="'optimizationReport/addEdit/manageTemplate'" type="primary" class="mt-16">
                管理模板
              </el-link>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-18 fw-800">
                  内容助手
                </div>
              </template>
              <ul class="mb-4 space-y-3">
                <li class="cursor-pointer hover:text-blue-500">
                  <div class="font-medium">
                    改进概况结构
                  </div>
                  <div class="text-xs text-gray-500">
                    包含资源投入、进度总览等标准结构
                  </div>
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  <div class="font-medium">
                    经验教训分析
                  </div>
                  <div class="text-xs text-gray-500">
                    问题分类、原因分析等标准内容
                  </div>
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  <div class="font-medium">
                    改进措施效果
                  </div>
                  <div class="text-xs text-gray-500">
                    完成情况、效果评估等标准内容
                  </div>
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  <div class="font-medium">
                    趋势分析框架
                  </div>
                  <div class="text-xs text-gray-500">
                    历史对比、预测分析等标准框架
                  </div>
                </li>
              </ul>
              <el-link type="primary" class="mt-16">
                自定义内容块
              </el-link>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-18 fw-800">
                  AI辅助
                </div>
              </template>
              <div class="mb-4 space-y-2">
                <div>
                  <el-button
                    type="primary" plain size="small"
                    class="!rounded-button w-full whitespace-nowrap"
                  >
                    AI完善内容
                  </el-button>
                </div>
                <div>
                  <el-button
                    type="primary" plain size="small"
                    class="!rounded-button w-full whitespace-nowrap"
                  >
                    AI格式优化
                  </el-button>
                </div>
                <div>
                  <el-button
                    type="primary" plain size="small"
                    class="!rounded-button w-full whitespace-nowrap"
                  >
                    AI生成摘要
                  </el-button>
                </div>
                <div>
                  <el-button
                    type="primary" plain size="small"
                    class="!rounded-button w-full whitespace-nowrap"
                  >
                    AI数据分析
                  </el-button>
                </div>
              </div>
              <div class="rounded bg-gray-50 p-3 text-sm">
                <h4 class="mb-2 font-medium">
                  AI建议：
                </h4>
                <p class="text-gray-600">
                  当前报告可以增加更多数据可视化内容，建议在"趋势分析"部分添加图表。
                </p>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-18 fw-800">
                  报告检查
                </div>
              </template>
              <div class="mb-4 space-y-2">
                <div>
                  <el-button
                    type="primary" plain size="small"
                    class="!rounded-button w-full whitespace-nowrap"
                  >
                    拼写检查
                  </el-button>
                </div>
                <div>
                  <el-button
                    type="primary" plain size="small"
                    class="!rounded-button w-full whitespace-nowrap"
                  >
                    格式检查
                  </el-button>
                </div>
                <div>
                  <el-button
                    type="primary" plain size="small"
                    class="!rounded-button w-full whitespace-nowrap"
                  >
                    完整性检查
                  </el-button>
                </div>
                <div>
                  <el-button
                    type="primary" plain size="small"
                    class="!rounded-button w-full whitespace-nowrap"
                  >
                    逻辑一致性检查
                  </el-button>
                </div>
              </div>
              <div class="rounded bg-gray-50 p-3 text-sm">
                <h4 class="mb-2 font-medium">
                  检查结果：
                </h4>
                <ul class="text-gray-600 space-y-1">
                  <li>发现3处拼写错误</li>
                  <li>2处格式不一致</li>
                  <li>1处数据逻辑问题</li>
                </ul>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-18 fw-800">
                  参考资料
                </div>
              </template>
              <ul class="mb-4 space-y-3">
                <li class="cursor-pointer hover:text-blue-500">
                  <div class="font-medium">
                    2023年度合规报告
                  </div>
                  <div class="text-xs text-gray-500">
                    可作为年度报告参考
                  </div>
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  <div class="font-medium">
                    改进措施实施指南
                  </div>
                  <div class="text-xs text-gray-500">
                    包含改进措施实施标准
                  </div>
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  <div class="font-medium">
                    数据分析方法手册
                  </div>
                  <div class="text-xs text-gray-500">
                    趋势分析方法参考
                  </div>
                  <span class="ml-2 rounded bg-blue-100 px-2 py-1 text-xs text-blue-600">系统推荐</span>
                </li>
              </ul>
              <el-link type="primary" class="mt-16">
                查看更多
              </el-link>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-menu-vertical {
    border-right: none;
  }

  .el-menu-item.is-active {
    background-color: rgb(64 158 255 / 10%) !important;
  }

  .el-collapse-item__header {
    font-weight: 500;
  }

  .el-form-item {
    margin-bottom: 24px;
  }

  .el-input__inner {
    height: 32px;
    line-height: 32px;
  }

  .el-textarea__inner {
    min-height: 120px;
  }
</style>
