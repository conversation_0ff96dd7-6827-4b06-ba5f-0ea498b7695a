<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import {
  Collection as ElIconCollection,
  DataAnalysis as ElIconDataAnalysis,
  Document as ElIconDocument,
  Download as ElIconDownload,
  Files as ElIconFiles,
  Grid as ElIconGrid,
  MagicStick as ElIconMagicStick,
  Menu as ElIconMenu,
  PieChart as ElIconPieChart,
  Plus as ElIconPlus,
  Search as ElIconSearch,
  Star as ElIconStar,
  TrendCharts as ElIconTrendCharts,
  // TrendingUp as ElIconTrendingUp,
  Upload as ElIconUpload,
  View as ElIconView,
} from '@element-plus/icons-vue'

const reportData = ref([
  { id: 'RP202406001', title: '2024年6月合规风险分析报告', type: '月度报告', period: '2024年6月', dateRange: '2024-06-01 至 2024-06-30', author: '张明远', createDate: '2024-06-28', status: '已发布', views: 156, citations: 12 },
  { id: 'RP2024Q2001', title: '2024年第二季度合规运营总结', type: '季度报告', period: '2024年Q2', dateRange: '2024-04-01 至 2024-06-30', author: '李静怡', createDate: '2024-06-25', status: '审核中', views: 89, citations: 5 },
  { id: 'RP2024SP001', title: '数据隐私合规专项检查报告', type: '专项报告', period: '2024年', dateRange: '2024-05-15 至 2024-06-15', author: '王伟', createDate: '2024-06-20', status: '已发布', views: 234, citations: 18 },
  { id: 'RP202406002', title: '反洗钱合规月度监测报告', type: '月度报告', period: '2024年6月', dateRange: '2024-06-01 至 2024-06-30', author: '陈思', createDate: '2024-06-18', status: '已发布', views: 187, citations: 9 },
  { id: 'RP2024SP002', title: '信息安全合规整改报告', type: '专项报告', period: '2024年', dateRange: '2024-04-01 至 2024-06-30', author: '赵强', createDate: '2024-06-15', status: '已归档', views: 321, citations: 23 },
  { id: 'RP202406003', title: '员工行为规范月度评估', type: '月度报告', period: '2024年6月', dateRange: '2024-06-01 至 2024-06-30', author: '周敏', createDate: '2024-06-12', status: '草稿', views: 0, citations: 0 },
  { id: 'RP2024Q2002', title: '2024年第二季度合规培训效果评估', type: '季度报告', period: '2024年Q2', dateRange: '2024-04-01 至 2024-06-30', author: '吴芳', createDate: '2024-06-10', status: '已发布', views: 142, citations: 7 },
  { id: 'RP2024SP003', title: '合同管理合规性专项审计报告', type: '专项报告', period: '2024年', dateRange: '2024-05-01 至 2024-06-30', author: '郑浩', createDate: '2024-06-08', status: '已发布', views: 178, citations: 11 },
  { id: 'RP202406004', title: '2024年6月合规政策更新报告', type: '月度报告', period: '2024年6月', dateRange: '2024-06-01 至 2024-06-30', author: '孙丽', createDate: '2024-06-05', status: '已发布', views: 165, citations: 8 },
  { id: 'RP2024Q2003', title: '2024年第二季度合规风险指标分析', type: '季度报告', period: '2024年Q2', dateRange: '2024-04-01 至 2024-06-30', author: '刘洋', createDate: '2024-06-01', status: '已发布', views: 203, citations: 15 },
])
const topReports = ref([
  { rank: 1, title: '2024年第一季度合规运营全面分析', type: '季度报告', views: 456, createDate: '2024-04-05', author: '张明远' },
  { rank: 2, title: '数据隐私合规年度评估报告', type: '年度报告', views: 389, createDate: '2024-03-15', author: '李静怡' },
  { rank: 3, title: '反洗钱合规专项检查报告', type: '专项报告', views: 367, createDate: '2024-05-20', author: '王伟' },
  { rank: 4, title: '2024年1月合规风险监测报告', type: '月度报告', views: 342, createDate: '2024-02-01', author: '陈思' },
  { rank: 5, title: '信息安全合规整改效果评估', type: '专项报告', views: 321, createDate: '2024-04-18', author: '赵强' },
])
const recentViewed = ref([
  { id: 'RP202406001', title: '2024年6月合规风险分析报告', type: '月度报告', viewTime: '10分钟前', lastView: '2024-06-28 14:30' },
  { id: 'RP2024SP001', title: '数据隐私合规专项检查报告', type: '专项报告', viewTime: '1小时前', lastView: '2024-06-28 13:45' },
  { id: 'RP2024Q2001', title: '2024年第二季度合规运营总结', type: '季度报告', viewTime: '3小时前', lastView: '2024-06-28 11:20' },
  { id: 'RP2024SP002', title: '信息安全合规整改报告', type: '专项报告', viewTime: '昨天', lastView: '2024-06-27 16:10' },
  { id: 'RP202406002', title: '反洗钱合规月度监测报告', type: '月度报告', viewTime: '昨天', lastView: '2024-06-27 09:30' },
  { id: 'RP2024Q2002', title: '2024年第二季度合规培训效果评估', type: '季度报告', viewTime: '2天前', lastView: '2024-06-26 14:15' },
])

const myReports = ref({
  created: [
    { id: 'RP202406001', title: '2024年6月合规风险分析报告', status: '已发布', date: '06-28' },
    { id: 'RP202406003', title: '员工行为规范月度评估', status: '草稿', date: '06-12' },
    { id: 'RP2024SP003', title: '合同管理合规性专项审计报告', status: '已发布', date: '06-08' },
  ],
  pending: [
    { id: 'RP2024Q2001', title: '2024年第二季度合规运营总结', date: '06-25' },
    { id: 'RP202406004', title: '2024年6月合规政策更新报告', date: '06-05' },
  ],
  published: [
    { id: 'RP2024SP001', title: '数据隐私合规专项检查报告', date: '06-20' },
    { id: 'RP202406002', title: '反洗钱合规月度监测报告', date: '06-18' },
    { id: 'RP2024Q2002', title: '2024年第二季度合规培训效果评估', date: '06-10' },
  ],
})

const hotReports = ref([
  { id: 'RP2024SP001', title: '数据隐私合规专项检查报告', views: 367, date: '06-20' },
  { id: 'RP202406001', title: '2024年6月合规风险分析报告', views: 156, date: '06-28' },
  { id: 'RP202406002', title: '反洗钱合规月度监测报告', views: 187, date: '06-18' },
])

const hotReportPeriod = ref('week')
const trendChart = ref<HTMLElement>()
const pieChart = ref<HTMLElement>()
function getTypeClass(type: string) {
  const classes: Record<string, string> = {
    月度报告: 'text-blue-500',
    季度报告: 'text-green-500',
    年度报告: 'text-purple-500',
    专项报告: 'text-orange-500',
  }
  return classes[type] || 'text-gray-500'
}
function getStatusClass(status: string) {
  const classes: Record<string, string> = {
    草稿: 'text-gray-500',
    审核中: 'text-blue-500',
    已发布: 'text-green-500',
    已归档: 'text-indigo-500',
  }
  return classes[status] || 'text-gray-500'
}
onMounted(() => {
  nextTick(() => {
    if (trendChart.value) {
      const chart = echarts.init(trendChart.value)
      chart.setOption({
        animation: false,
        title: { text: '报告数量趋势', left: 'center' },
        tooltip: { trigger: 'axis' },
        legend: { data: ['月度报告', '季度报告', '专项报告'], bottom: 10 },
        grid: { left: '3%', right: '4%', bottom: '15%', top: '15%', containLabel: true },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月'],
          axisLine: { lineStyle: { color: '#ccc' } },
        },
        yAxis: {
          type: 'value',
          axisLine: { lineStyle: { color: '#ccc' } },
          splitLine: { lineStyle: { type: 'dashed' } },
        },
        series: [
          { name: '月度报告', type: 'line', data: [12, 15, 18, 14, 16, 20], smooth: true, lineStyle: { width: 3 }, symbolSize: 8 },
          { name: '季度报告', type: 'line', data: [3, 2, 4, 5, 3, 4], smooth: true, lineStyle: { width: 3 }, symbolSize: 8 },
          { name: '专项报告', type: 'line', data: [5, 4, 6, 8, 7, 9], smooth: true, lineStyle: { width: 3 }, symbolSize: 8 },
        ],
      })
    }
    if (pieChart.value) {
      const chart = echarts.init(pieChart.value)
      chart.setOption({
        animation: false,
        title: { text: '报告类型分布', left: 'center' },
        tooltip: { trigger: 'item' },
        legend: { orient: 'vertical', right: 10, top: 'center' },
        series: [
          {
            name: '报告类型',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: { borderRadius: 10, borderColor: '#fff', borderWidth: 2 },
            label: { show: false, position: 'center' },
            emphasis: { label: { show: true, fontSize: '18', fontWeight: 'bold' } },
            labelLine: { show: false },
            data: [
              { value: 684, name: '月度报告' },
              { value: 240, name: '季度报告' },
              { value: 180, name: '年度报告' },
              { value: 180, name: '专项报告' },
            ],
          },
        ],
      })
    }
  })
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              优化报告
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="'optimizationReport/index/add'" type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <ElIconPlus />
              </el-icon>
              新增报告
            </el-button>
            <el-button v-auth="'optimizationReport/index/batchExport'" plain class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <ElIconDownload />
              </el-icon>
              批量导出
            </el-button>
            <el-button v-auth="'optimizationReport/index/statisticalAnalysis'" plain class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <ElIconDataAnalysis />
              </el-icon>
              统计分析
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-row :gutter="20" class="">
              <el-col :span="6">
                <el-card shadow="hover" class="">
                  <!-- <template #header>
                    <div class="f-16 fw-600">基本信息</div>
                  </template> -->
                  <div class="flex justify-between">
                    <div>
                      <div class="text-sm text-gray-500">
                        报告总数
                      </div>
                      <div class="mt-1 text-2xl font-bold">
                        1,284
                      </div>
                      <div class="mt-1 flex items-center text-sm text-green-500">
                        <el-icon class="mr-1">
                          <el-icon-top />
                        </el-icon>12.5%
                      </div>
                    </div>
                    <div class="h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
                      <el-icon class="text-xl text-blue-500">
                        <ElIconDocument />
                      </el-icon>
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card shadow="hover" class="">
                  <div class="flex justify-between">
                    <div>
                      <div class="text-sm text-gray-500">
                        本月新增
                      </div>
                      <div class="mt-1 text-2xl font-bold">
                        86
                      </div>
                      <div class="mt-1 flex items-center text-sm text-green-500">
                        <el-icon class="mr-1">
                          <el-icon-top />
                        </el-icon>8.2%
                      </div>
                    </div>
                    <div class="h-12 w-12 flex items-center justify-center rounded-full bg-green-100">
                      <el-icon class="text-xl text-green-500">
                        <el-icon-trending-up />
                      </el-icon>
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card shadow="hover" class="">
                  <div class="flex justify-between">
                    <div>
                      <div class="text-sm text-gray-500">
                        平均阅读量
                      </div>
                      <div class="mt-1 text-2xl font-bold">
                        324
                      </div>
                      <div class="mt-1 flex items-center text-sm text-red-500">
                        <el-icon class="mr-1">
                          <el-icon-bottom />
                        </el-icon>3.1%
                      </div>
                    </div>
                    <div class="h-12 w-12 flex items-center justify-center rounded-full bg-purple-100">
                      <el-icon class="text-xl text-purple-500">
                        <ElIconView />
                      </el-icon>
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card shadow="hover" class="">
                  <div class="flex justify-between">
                    <div>
                      <div class="text-sm text-gray-500">
                        高引用率
                      </div>
                      <div class="mt-1 text-2xl font-bold">
                        68%
                      </div>
                      <div class="mt-1 flex items-center text-sm text-green-500">
                        <el-icon class="mr-1">
                          <el-icon-top />
                        </el-icon>5.7%
                      </div>
                    </div>
                    <div class="h-12 w-12 flex items-center justify-center rounded-full bg-orange-100">
                      <el-icon class="text-xl text-orange-500">
                        <ElIconStar />
                      </el-icon>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
            <el-card shadow="hover" class="mt-20">
              <!-- 快速筛选区 -->
              <div class="rounded-lg bg-white p-4 shadow">
                <div class="flex overflow-x-auto pb-2 space-x-2">
                  <button
                    v-auth="'optimizationReport/index/filterAll'"
                    class="!rounded-button whitespace-nowrap bg-blue-500 px-4 py-1.5 text-sm text-white"
                  >
                    全部
                  </button>
                  <button
                    v-auth="'optimizationReport/index/filterMonthly'"
                    class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-1.5 text-sm hover:bg-gray-50"
                  >
                    月度报告
                  </button>
                  <button
                    v-auth="'optimizationReport/index/filterQuarterly'"
                    class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-1.5 text-sm hover:bg-gray-50"
                  >
                    季度报告
                  </button>
                  <button
                    v-auth="'optimizationReport/index/filterYearly'"
                    class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-1.5 text-sm hover:bg-gray-50"
                  >
                    年度报告
                  </button>
                  <button
                    v-auth="'optimizationReport/index/filterSpecial'"
                    class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-1.5 text-sm hover:bg-gray-50"
                  >
                    专项报告
                  </button>
                  <button
                    v-auth="'optimizationReport/index/filterHighViews'"
                    class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-1.5 text-sm hover:bg-gray-50"
                  >
                    高阅读量
                  </button>
                </div>
              </div>
              <!-- 筛选区 -->
              <div class="rounded-lg bg-white p-4 shadow">
                <div class="grid grid-cols-6 gap-4">
                  <el-select placeholder="报告类型" size="small">
                    <el-option label="全部类型" value="" />
                    <el-option label="月度报告" value="monthly" />
                    <el-option label="季度报告" value="quarterly" />
                    <el-option label="年度报告" value="yearly" />
                    <el-option label="专项报告" value="special" />
                  </el-select>
                  <el-select placeholder="报告周期" size="small">
                    <el-option label="全部周期" value="" />
                    <el-option label="2023年" value="2023" />
                    <el-option label="2024年" value="2024" />
                    <el-option label="2024年Q1" value="2024Q1" />
                    <el-option label="2024年Q2" value="2024Q2" />
                  </el-select>
                  <el-select placeholder="状态" size="small">
                    <el-option label="全部状态" value="" />
                    <el-option label="草稿" value="draft" />
                    <el-option label="审核中" value="reviewing" />
                    <el-option label="已发布" value="published" />
                    <el-option label="已归档" value="archived" />
                  </el-select>
                  <el-date-picker
                    type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                    size="small"
                  />
                  <div class="relative">
                    <input
                      type="text" placeholder="关键字搜索"
                      class="w-full border border-gray-300 rounded-md py-1.5 pl-8 pr-4 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                    <el-icon class="absolute left-2 top-2 text-gray-400">
                      <ElIconSearch />
                    </el-icon>
                  </div>
                  <button
                    v-auth="'optimizationReport/index/advancedFilter'"
                    class="!rounded-button whitespace-nowrap border border-gray-300 text-sm text-gray-600 hover:bg-gray-50"
                  >
                    高级筛选
                  </button>
                </div>
              </div>
              <!-- 报告列表区 -->
              <div class="rounded-lg bg-white shadow">
                <div class="flex items-center justify-between border-b border-gray-200 p-4">
                  <div class="text-gray-700">
                    共 1,284 条报告
                  </div>
                  <div class="flex space-x-2">
                    <button
                      v-auth="'optimizationReport/index/tableView'"
                      class="!rounded-button whitespace-nowrap border border-gray-300 px-3 py-1 text-sm hover:bg-gray-50"
                    >
                      <el-icon class="mr-1">
                        <ElIconMenu />
                      </el-icon>表格视图
                    </button>
                    <button
                      v-auth="'optimizationReport/index/cardView'"
                      class="!rounded-button whitespace-nowrap border border-blue-500 px-3 py-1 text-sm text-blue-500 hover:bg-blue-50"
                    >
                      <el-icon class="mr-1">
                        <ElIconGrid />
                      </el-icon>卡片视图
                    </button>
                  </div>
                </div>
                <div class="p-4">
                  <el-table :data="reportData" style="width: 100%;">
                    <el-table-column type="selection" width="50" />
                    <el-table-column prop="id" label="报告编号" width="120" />
                    <el-table-column prop="title" label="报告标题" min-width="200" />
                    <el-table-column prop="type" label="报告类型" width="120">
                      <template #default="{ row }">
                        <span :class="getTypeClass(row.type)">{{ row.type }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="period" label="报告周期" width="120" />
                    <el-table-column prop="dateRange" label="覆盖时间" width="180" />
                    <el-table-column prop="author" label="编制人" width="120" />
                    <el-table-column prop="createDate" label="编制日期" width="120" />
                    <el-table-column prop="status" label="状态" width="120">
                      <template #default="{ row }">
                        <span :class="getStatusClass(row.status)">{{ row.status }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="views" label="阅读量" width="100" sortable />
                    <el-table-column prop="citations" label="引用次数" width="100" sortable />
                    <el-table-column label="操作" width="180">
                      <template #default="{ row }">
                        <button v-auth="'optimizationReport/index/view'" class="mr-2 text-sm text-blue-500 hover:text-blue-700">
                          查看
                        </button>
                        <button
                          v-if="row.status === '草稿'"
                          v-auth="'optimizationReport/index/edit'"
                          class="mr-2 text-sm text-blue-500 hover:text-blue-700"
                        >
                          编辑
                        </button>
                        <el-dropdown>
                          <span class="cursor-pointer text-sm text-blue-500 hover:text-blue-700">更多</span>
                          <template #dropdown>
                            <el-dropdown-menu>
                              <el-dropdown-item v-auth="'optimizationReport/index/export'">导出</el-dropdown-item>
                              <el-dropdown-item v-auth="'optimizationReport/index/share'">分享</el-dropdown-item>
                              <el-dropdown-item v-if="row.status !== '已归档'" v-auth="'optimizationReport/index/archive'">
                                归档
                              </el-dropdown-item>
                              <el-dropdown-item v-auth="'optimizationReport/index/delete'" class="text-red-500">
                                删除
                              </el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <div class="flex items-center justify-between border-t border-gray-200 p-4">
                  <div class="text-sm text-gray-500">
                    显示 1-10 条，共 1,284 条
                  </div>
                  <el-pagination
                    :total="1284" :page-size="10" :current-page="1" layout="prev, pager, next, jumper"
                    background
                  />
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  报告统计
                </div>
              </template>
              <div class="grid grid-cols-2 gap-4">
                <div class="h-80">
                  <div ref="trendChart" class="h-full w-full" />
                </div>
                <div class="h-80">
                  <div ref="pieChart" class="h-full w-full" />
                </div>
              </div>
              <div class="mt-6">
                <h3 class="text-md mb-3 font-medium">
                  阅读量 Top 5 报告
                </h3>
                <el-table :data="topReports" style="width: 100%;">
                  <el-table-column prop="rank" label="排名" width="80" />
                  <el-table-column prop="title" label="报告标题" min-width="200" />
                  <el-table-column prop="type" label="类型" width="120" />
                  <el-table-column prop="views" label="阅读量" width="120" sortable />
                  <el-table-column prop="createDate" label="编制日期" width="120" />
                  <el-table-column prop="author" label="编制人" width="120" />
                  <el-table-column label="操作" width="80">
                    <template #default>
                      <button v-auth="'optimizationReport/index/detail'" class="text-sm text-blue-500 hover:text-blue-700">
                        详情
                      </button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  生成报告
                </div>
              </template>
              <div class="mb-6 flex items-center justify-between">
                <div>
                  <p class="mb-2 text-gray-600">
                    使用向导快速生成标准化的合规报告
                  </p>
                  <p class="text-sm text-gray-500">
                    支持多种报告类型和自定义模板
                  </p>
                </div>
                <button v-auth="'optimizationReport/index/startGenerate'" class="!rounded-button whitespace-nowrap bg-blue-500 px-4 py-2 text-white hover:bg-blue-600">
                  开始生成
                </button>
              </div>
              <div class="grid grid-cols-4 gap-4">
                <div class="cursor-pointer border border-gray-200 rounded-lg p-4 hover:shadow-md">
                  <div class="mb-3 h-32 flex items-center justify-center rounded-md bg-blue-50">
                    <el-icon class="text-4xl text-blue-500">
                      <ElIconDocument />
                    </el-icon>
                  </div>
                  <h3 class="mb-1 font-medium">
                    月度总结模板
                  </h3>
                  <p class="text-sm text-gray-500">
                    适用于月度合规情况汇总
                  </p>
                  <div v-auth="'optimizationReport/index/useMonthlyTemplate'" class="mt-3 text-sm text-blue-500">
                    使用模板
                  </div>
                </div>
                <div class="cursor-pointer border border-gray-200 rounded-lg p-4 hover:shadow-md">
                  <div class="mb-3 h-32 flex items-center justify-center rounded-md bg-green-50">
                    <el-icon class="text-4xl text-green-500">
                      <ElIconDataAnalysis />
                    </el-icon>
                  </div>
                  <h3 class="mb-1 font-medium">
                    季度分析模板
                  </h3>
                  <p class="text-sm text-gray-500">
                    适用于季度合规数据分析
                  </p>
                  <div v-auth="'optimizationReport/index/useQuarterlyTemplate'" class="mt-3 text-sm text-blue-500">
                    使用模板
                  </div>
                </div>
                <div class="cursor-pointer border border-gray-200 rounded-lg p-4 hover:shadow-md">
                  <div class="mb-3 h-32 flex items-center justify-center rounded-md bg-purple-50">
                    <el-icon class="text-4xl text-purple-500">
                      <ElIconPieChart />
                    </el-icon>
                  </div>
                  <h3 class="mb-1 font-medium">
                    年度回顾模板
                  </h3>
                  <p class="text-sm text-gray-500">
                    适用于年度合规工作总结
                  </p>
                  <div v-auth="'optimizationReport/index/useYearlyTemplate'" class="mt-3 text-sm text-blue-500">
                    使用模板
                  </div>
                </div>
                <div class="cursor-pointer border border-gray-200 rounded-lg p-4 hover:shadow-md">
                  <div class="mb-3 h-32 flex items-center justify-center rounded-md bg-orange-50">
                    <el-icon class="text-4xl text-orange-500">
                      <ElIconFiles />
                    </el-icon>
                  </div>
                  <h3 class="mb-1 font-medium">
                    专项分析模板
                  </h3>
                  <p class="text-sm text-gray-500">
                    适用于特定合规问题分析
                  </p>
                  <div v-auth="'optimizationReport/index/useSpecialTemplate'" class="mt-3 text-sm text-blue-500">
                    使用模板
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  最近查看
                </div>
              </template>
              <div class="flex overflow-x-auto pb-4 space-x-4">
                <div
                  v-for="item in recentViewed" :key="item.id"
                  class="min-w-[240px] cursor-pointer border border-gray-200 rounded-lg p-4 hover:shadow-md"
                >
                  <h3 class="mb-1 truncate font-medium">
                    {{ item.title }}
                  </h3>
                  <div class="mb-2 flex justify-between text-sm text-gray-500">
                    <span>{{ item.type }}</span>
                    <span>{{ item.viewTime }}</span>
                  </div>
                  <div class="text-xs text-gray-400">
                    上次查看: {{ item.lastView }}
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  我的报告
                </div>
              </template>
              <el-tabs>
                <el-tab-pane label="我创建的">
                  <div class="space-y-3">
                    <div v-for="item in myReports.created" :key="item.id" class="border-b border-gray-100 pb-2">
                      <div class="truncate text-sm font-medium">
                        {{ item.title }}
                      </div>
                      <div class="mt-1 flex justify-between text-xs text-gray-500">
                        <span :class="getStatusClass(item.status)">{{ item.status }}</span>
                        <span>{{ item.date }}</span>
                      </div>
                    </div>
                  </div>
                  <div v-auth="'optimizationReport/index/viewAllCreated'" class="mt-3 cursor-pointer text-sm text-blue-500 hover:underline">
                    查看全部
                  </div>
                </el-tab-pane>
                <el-tab-pane label="待我审核">
                  <div class="space-y-3">
                    <div v-for="item in myReports.pending" :key="item.id" class="border-b border-gray-100 pb-2">
                      <div class="truncate text-sm font-medium">
                        {{ item.title }}
                      </div>
                      <div class="mt-1 flex justify-between text-xs text-gray-500">
                        <span class="text-blue-500">待审核</span>
                        <span>{{ item.date }}</span>
                      </div>
                    </div>
                  </div>
                  <div v-auth="'optimizationReport/index/viewAllPending'" class="mt-3 cursor-pointer text-sm text-blue-500 hover:underline">
                    查看全部
                  </div>
                </el-tab-pane>
                <el-tab-pane label="已发布的">
                  <div class="space-y-3">
                    <div v-for="item in myReports.published" :key="item.id" class="border-b border-gray-100 pb-2">
                      <div class="truncate text-sm font-medium">
                        {{ item.title }}
                      </div>
                      <div class="mt-1 flex justify-between text-xs text-gray-500">
                        <span class="text-green-500">已发布</span>
                        <span>{{ item.date }}</span>
                      </div>
                    </div>
                  </div>
                  <div v-auth="'optimizationReport/index/viewAllPublished'" class="mt-3 cursor-pointer text-sm text-blue-500 hover:underline">
                    查看全部
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-card>
            <el-card shadow="hover" class="">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-16 fw-600">
                    热门报告
                  </div>
                  <el-select v-model="hotReportPeriod" size="small" class="w-24">
                    <el-option label="本周" value="week" />
                    <el-option label="本月" value="month" />
                    <el-option label="本季" value="quarter" />
                  </el-select>
                </div>
              </template>
              <div class="space-y-3">
                <div v-for="item in hotReports" :key="item.id" class="border-b border-gray-100 pb-2">
                  <div class="truncate text-sm font-medium">
                    {{ item.title }}
                  </div>
                  <div class="mt-1 flex justify-between text-xs text-gray-500">
                    <span>阅读量: {{ item.views }}</span>
                    <span>{{ item.date }}</span>
                  </div>
                </div>
              </div>
              <div v-auth="'optimizationReport/index/viewAllHot'" class="mt-3 cursor-pointer text-sm text-blue-500 hover:underline">
                查看全部
              </div>
            </el-card>
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  报告日历
                </div>
              </template>
              <el-calendar>
                <template #date-cell="{ data }">
                  <div class="mx-auto h-8 w-8 flex items-center justify-center">
                    <span
                      :class="{ 'text-blue-500 font-medium': data.day.includes('15') }"
                    >{{ data.day.split('-')[2] }}</span>
                  </div>
                </template>
              </el-calendar>
              <div v-auth="'optimizationReport/index/viewCalendar'" class="mt-3 cursor-pointer text-sm text-blue-500 hover:underline">
                查看日历视图
              </div>
            </el-card>
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  快速操作
                </div>
              </template>
              <div class="space-y-2">
                <button
                  v-auth="'optimizationReport/index/newMonthlyReport'"
                  class="!rounded-button w-full whitespace-nowrap border border-gray-200 px-3 py-2 text-left text-sm hover:bg-gray-50"
                >
                  <el-icon class="mr-1">
                    <ElIconPlus />
                  </el-icon>新建月度报告
                </button>
                <button
                  v-auth="'optimizationReport/index/newQuarterlyReport'"
                  class="!rounded-button w-full whitespace-nowrap border border-gray-200 px-3 py-2 text-left text-sm hover:bg-gray-50"
                >
                  <el-icon class="mr-1">
                    <ElIconPlus />
                  </el-icon>新建季度报告
                </button>
                <button
                  v-auth="'optimizationReport/index/newSpecialReport'"
                  class="!rounded-button w-full whitespace-nowrap border border-gray-200 px-3 py-2 text-left text-sm hover:bg-gray-50"
                >
                  <el-icon class="mr-1">
                    <ElIconPlus />
                  </el-icon>新建专题报告
                </button>
                <button
                  v-auth="'optimizationReport/index/importExternalReport'"
                  class="!rounded-button w-full whitespace-nowrap border border-gray-200 px-3 py-2 text-left text-sm hover:bg-gray-50"
                >
                  <el-icon class="mr-1">
                    <ElIconUpload />
                  </el-icon>导入外部报告
                </button>
              </div>
            </el-card>
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  AI助手
                </div>
              </template>
              <div class="space-y-2">
                <button
                  v-auth="'optimizationReport/index/generateOutline'"
                  class="!rounded-button w-full whitespace-nowrap border border-gray-200 px-3 py-2 text-left text-sm hover:bg-gray-50"
                >
                  <el-icon class="mr-1">
                    <ElIconMagicStick />
                  </el-icon>生成报告大纲
                </button>
                <button
                  v-auth="'optimizationReport/index/summarizeResults'"
                  class="!rounded-button w-full whitespace-nowrap border border-gray-200 px-3 py-2 text-left text-sm hover:bg-gray-50"
                >
                  <el-icon class="mr-1">
                    <ElIconCollection />
                  </el-icon>汇总改进成果
                </button>
                <button
                  v-auth="'optimizationReport/index/analyzeTrends'"
                  class="!rounded-button w-full whitespace-nowrap border border-gray-200 px-3 py-2 text-left text-sm hover:bg-gray-50"
                >
                  <el-icon class="mr-1">
                    <ElIconTrendCharts />
                  </el-icon>分析报告趋势
                </button>
              </div>
              <div class="mt-3 rounded bg-blue-50 p-3 text-sm text-gray-700">
                <div class="mb-1 font-medium">
                  AI建议
                </div>
                <p>根据近期数据，建议重点关注数据隐私合规专项报告，阅读量增长显著。</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  :deep(.el-table .cell) {
    padding-right: 10px;
    padding-left: 10px;
  }

  :deep(.el-table th.el-table__cell) {
    background-color: #f8fafc;
  }

  :deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
    background-color: #1e88e5;
  }

  :deep(.el-pagination.is-background .el-pager li:not(.is-disabled):hover) {
    color: #1e88e5;
  }

  :deep(.el-select .el-input__inner) {
    height: 32px;
  }

  :deep(.el-date-editor .el-range-input) {
    height: 30px;
  }
</style>
