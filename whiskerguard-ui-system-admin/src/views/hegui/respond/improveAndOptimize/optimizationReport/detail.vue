<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import {
  Calendar,
  Document,
  Download,
  Edit,
  Message,
  More,
  Printer,
  Share,
  Star,
  View,
} from '@element-plus/icons-vue'

const activeTab = ref('data')
const rating = ref(4.5)

const tableData = ref([
  {
    name: '反洗钱系统升级',
    type: 'IT系统',
    department: 'IT部',
    status: '已完成',
    completion: '100%',
  },
  {
    name: '数据隐私保护培训',
    type: '培训',
    department: '人力资源部',
    status: '已完成',
    completion: '100%',
  },
  {
    name: '合规风险评估',
    type: '评估',
    department: '合规部',
    status: '进行中',
    completion: '65%',
  },
  {
    name: '员工行为规范更新',
    type: '制度',
    department: '合规部',
    status: '待审核',
    completion: '90%',
  },
])

const attachmentData = ref([
  {
    name: '2023Q4合规数据统计表',
    type: 'Excel',
    uploadTime: '2023-12-12',
    uploader: '李思远',
  },
  {
    name: '反洗钱系统升级报告',
    type: 'PDF',
    uploadTime: '2023-12-08',
    uploader: '王建国',
  },
  {
    name: '数据隐私保护培训材料',
    type: 'PPT',
    uploadTime: '2023-11-25',
    uploader: '陈美玲',
  },
])

function statusTagType(status: string) {
  switch (status) {
    case '已完成': return 'success'
    case '进行中': return 'warning'
    case '待审核': return ''
    default: return 'info'
  }
}

const improvementChart = ref<HTMLElement>()
const measuresChart = ref<HTMLElement>()
const departmentsChart = ref<HTMLElement>()

onMounted(() => {
  nextTick(() => {
    initCharts()
  })
})

function initCharts() {
  // 改进概况图表
  const improvementChartInstance = echarts.init(improvementChart.value)
  improvementChartInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'item',
    },
    legend: {
      top: '5%',
      left: 'center',
    },
    series: [
      {
        name: '改进措施分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 6, name: '反洗钱系统' },
          { value: 8, name: '数据隐私' },
          { value: 7, name: '员工行为' },
          { value: 4, name: '合规培训' },
          { value: 3, name: '风险评估' },
        ],
      },
    ],
  })

  // 改进措施状态图表
  const measuresChartInstance = echarts.init(measuresChart.value)
  measuresChartInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
    },
    yAxis: {
      type: 'category',
      data: ['已完成', '进行中', '待审核', '延期'],
    },
    series: [
      {
        name: '数量',
        type: 'bar',
        data: [18, 5, 3, 2],
        itemStyle: {
          color(params: any) {
            const colorList = ['#10B981', '#3B82F6', '#F59E0B', '#EF4444']
            return colorList[params.dataIndex]
          },
        },
      },
    ],
  })

  // 部门完成率图表
  const departmentsChartInstance = echarts.init(departmentsChart.value)
  departmentsChartInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: ['合规部', 'IT部', '人力资源部', '财务部', '市场部'],
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        formatter: '{value}%',
      },
    },
    series: [
      {
        name: '完成率',
        type: 'bar',
        data: [95, 85, 78, 65, 60],
        itemStyle: {
          color(params: any) {
            const value = params.data
            if (value >= 90) {
              return '#10B981'
            }
            else if (value >= 70) {
              return '#3B82F6'
            }
            else {
              return '#F59E0B'
            }
          },
        },
      },
    ],
  })

  window.addEventListener('resize', () => {
    improvementChartInstance.resize()
    measuresChartInstance.resize()
    departmentsChartInstance.resize()
  })
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              2023年第四季度合规优化报告
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-2">
            <el-button v-auth="'optimizationReport/detail/edit'" type="primary" class="rounded-button whitespace-nowrap">
              <el-icon>
                <Edit />
              </el-icon>
              <span>编辑</span>
            </el-button>

            <el-dropdown>
              <el-button type="primary" class="rounded-button whitespace-nowrap">
                <el-icon>
                  <Download />
                </el-icon>
                <span>导出</span>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-auth="'optimizationReport/detail/exportPDF'">PDF格式</el-dropdown-item>
                  <el-dropdown-item v-auth="'optimizationReport/detail/exportWord'">Word格式</el-dropdown-item>
                  <el-dropdown-item v-auth="'optimizationReport/detail/exportPPT'">PPT格式</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-button v-auth="'optimizationReport/detail/print'" type="primary" class="rounded-button whitespace-nowrap">
              <el-icon>
                <Printer />
              </el-icon>
              <span>打印</span>
            </el-button>

            <el-button v-auth="'optimizationReport/detail/share'" type="primary" class="rounded-button whitespace-nowrap">
              <el-icon>
                <Share />
              </el-icon>
              <span>分享</span>
            </el-button>

            <el-dropdown>
              <el-button class="rounded-button whitespace-nowrap bg-gray-200 text-gray-700 hover:bg-gray-300">
                <el-icon>
                  <More />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-auth="'optimizationReport/detail/archive'">归档报告</el-dropdown-item>
                  <el-dropdown-item v-auth="'optimizationReport/detail/delete'">删除报告</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <!-- 基本信息区 -->
            <el-card shadow="hover" class="">
              <template #header>
                <h2 class="text-lg text-gray-800 font-semibold">
                  报告基本信息
                </h2>
              </template>
              <div class="grid grid-cols-2 gap-4">
                <div class="flex">
                  <span class="w-24 text-gray-500">报告编号</span>
                  <span class="text-gray-800">REP-2023-Q4-001</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">报告标题</span>
                  <span class="text-gray-800">2023年第四季度合规优化报告</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">报告类型</span>
                  <span class="text-gray-800">季度报告</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">报告周期</span>
                  <span class="text-gray-800">2023年10月-12月</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">覆盖时间</span>
                  <span class="text-gray-800">2023-10-01 至 2023-12-31</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">编制人</span>
                  <span class="text-gray-800">张明远 (合规部)</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">编制日期</span>
                  <span class="text-gray-800">2023-12-10</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">状态</span>
                  <span class="text-green-600">已发布</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">阅读量</span>
                  <span class="text-gray-800">124</span>
                </div>
              </div>
            </el-card>

            <!-- 报告内容区 -->
            <el-card shadow="hover" class="mt-20">
              <h2 class="mb-4 text-xl text-gray-800 font-bold">
                优化报告摘要
              </h2>
              <div class="text-gray-700 space-y-4">
                <p>
                  <span
                    class="font-semibold"
                  >报告范围：</span>本报告涵盖了2023年第四季度公司在合规管理方面的改进措施、效果评估及未来规划，重点关注了反洗钱、数据隐私和员工行为规范三大领域。
                </p>
                <p><span class="font-semibold">主要成果：</span>本季度共实施改进措施28项，完成率89%，合规风险事件同比下降32%，员工合规培训覆盖率提升至98%。</p>
                <p><span class="font-semibold">关键发现：</span>数据隐私保护措施实施后，客户投诉减少45%；反洗钱系统升级显著提高了可疑交易识别率；新员工合规意识仍需加强。</p>
                <p><span class="font-semibold">总体评价：</span>本季度合规改进工作成效显著，但仍需在跨部门协作和长期效果跟踪方面加强。</p>
                <p><span class="font-semibold">后续建议：</span>建议下季度重点推进合规文化建设，完善合规风险评估机制，并加强IT系统对合规管理的支持。</p>
              </div>
            </el-card>

            <!-- 详细内容部分 -->
            <el-card shadow="hover" class="mt-20">
              <h2 class="mb-4 text-xl text-gray-800 font-bold">
                一、改进概况
              </h2>
              <div class="text-gray-700 space-y-4">
                <p>本季度共实施合规改进措施28项，其中已完成25项，3项因资源调配原因延期至下季度完成。改进措施主要分布在以下领域：</p>
                <ul class="list-disc pl-6 space-y-2">
                  <li>反洗钱系统升级（6项）</li>
                  <li>数据隐私保护（8项）</li>
                  <li>员工行为规范（7项）</li>
                  <li>合规培训体系（4项）</li>
                  <li>合规风险评估（3项）</li>
                </ul>
                <p>资源投入方面，本季度合规改进工作共投入人力320人天，预算执行率92%，主要集中在IT系统升级和员工培训两个领域。</p>
                <div ref="improvementChart" class="mt-6 h-80" />
              </div>
            </el-card>

            <!-- 标签页区 -->
            <el-card shadow="hover" class="mt-20">
              <el-tabs v-model="activeTab">
                <el-tab-pane label="改进数据" name="data">
                  <h3 class="mb-4 text-lg text-gray-800 font-semibold">
                    改进措施数据
                  </h3>
                  <el-table :data="tableData" style="width: 100%;">
                    <el-table-column prop="name" label="措施名称" />
                    <el-table-column prop="type" label="类型" />
                    <el-table-column prop="department" label="责任部门" />
                    <el-table-column prop="status" label="状态">
                      <template #default="{ row }">
                        <el-tag :type="statusTagType(row.status) || 'info'" effect="light">
                          {{ row.status }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="completion" label="完成率" />
                  </el-table>
                  <div class="grid grid-cols-2 mt-6 gap-6">
                    <div ref="measuresChart" class="h-64" />
                    <div ref="departmentsChart" class="h-64" />
                  </div>
                </el-tab-pane>
                <el-tab-pane label="部门分析" name="department">
                  部门分析
                </el-tab-pane>
                <el-tab-pane label="反馈意见" name="feedback">
                  反馈意见
                </el-tab-pane>
                <el-tab-pane label="历史对比" name="history">
                  历史对比
                </el-tab-pane>
              </el-tabs>
            </el-card>

            <!-- 相关资料区 -->
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <h2 class="text-lg text-gray-800 font-semibold">
                  相关资料
                </h2>
              </template>
              <el-table :data="attachmentData">
                <el-table-column prop="name" label="附件名称" />
                <el-table-column prop="type" label="类型" />
                <el-table-column prop="uploadTime" label="上传时间" />
                <el-table-column prop="uploader" label="上传人" />
                <el-table-column label="操作">
                  <template #default>
                    <el-link v-auth="'optimizationReport/detail/viewAttachment'" type="primary" :underline="false" class="mr-3">
                      查看
                    </el-link>
                    <el-link v-auth="'optimizationReport/detail/downloadAttachment'" type="primary" :underline="false">
                      下载
                    </el-link>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
          <el-col :span="6">
            <!-- 报告评价模块 -->
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  报告评价
                </div>
              </template>
              <div class="mb-2 flex items-center">
                <el-rate v-model="rating" disabled show-score text-color="#ff9900" score-template="{value} (12人评价)" />
              </div>
              <el-button v-auth="'optimizationReport/detail/rate'" type="primary" class="rounded-button w-full whitespace-nowrap">
                <el-icon>
                  <Star />
                </el-icon>
                <span>评价报告</span>
              </el-button>
            </el-card>

            <!-- 分享信息模块 -->
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  分享信息
                </div>
              </template>
              <div class="mb-2 flex items-center">
                <el-icon>
                  <Share />
                </el-icon>
                <span class="ml-2 text-sm text-gray-600">已分享 24 次</span>
              </div>
              <div class="mb-4 flex items-center">
                <el-icon>
                  <View />
                </el-icon>
                <span class="ml-2 text-sm text-gray-600">已查看 124 次</span>
              </div>
              <h3 class="mb-2 text-sm text-gray-700 font-medium">
                最近查看
              </h3>
              <el-avatar-group :max="4">
                <el-avatar src="https://randomuser.me/api/portraits/women/12.jpg" />
                <el-avatar src="https://randomuser.me/api/portraits/men/32.jpg" />
                <el-avatar src="https://randomuser.me/api/portraits/women/45.jpg" />
                <el-avatar src="https://randomuser.me/api/portraits/men/76.jpg" />
              </el-avatar-group>
            </el-card>

            <!-- 相关报告模块 -->
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关报告
                </div>
              </template>
              <div class="space-y-3">
                <el-link :underline="false" class="group block">
                  <div class="flex items-start justify-between">
                    <span class="group-hover:text-primary text-sm text-gray-700 font-medium">2023年第三季度合规优化报告</span>
                    <span class="text-xs text-gray-500">2023-09-30</span>
                  </div>
                  <el-tag size="small" class="mt-1">
                    季度报告
                  </el-tag>
                </el-link>
                <el-link :underline="false" class="group block">
                  <div class="flex items-start justify-between">
                    <span class="group-hover:text-primary text-sm text-gray-700 font-medium">2023年反洗钱专项报告</span>
                    <span class="text-xs text-gray-500">2023-11-15</span>
                  </div>
                  <el-tag size="small" class="mt-1">
                    专项报告
                  </el-tag>
                </el-link>
                <el-link :underline="false" class="group block">
                  <div class="flex items-start justify-between">
                    <span class="group-hover:text-primary text-sm text-gray-700 font-medium">数据隐私合规评估报告</span>
                    <span class="text-xs text-gray-500">2023-10-20</span>
                  </div>
                  <el-tag size="small" class="mt-1">
                    评估报告
                  </el-tag>
                </el-link>
              </div>
            </el-card>

            <!-- 快捷操作模块 -->
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  快捷操作
                </div>
              </template>
              <div class="space-y-2">
                <div>
                  <el-button v-auth="'optimizationReport/detail/exportPDF'" class="rounded-button w-full whitespace-nowrap text-left">
                    <el-icon color="#F56C6C">
                      <Document />
                    </el-icon>
                    <span>导出PDF</span>
                  </el-button>
                </div>
                <div>
                  <el-button v-auth="'optimizationReport/detail/generatePPT'" class="rounded-button w-full whitespace-nowrap text-left">
                    <el-icon color="#E6A23C">
                      <Document />
                    </el-icon>
                    <span>生成PPT</span>
                  </el-button>
                </div>
                <div>
                  <el-button v-auth="'optimizationReport/detail/sendEmail'" class="rounded-button w-full whitespace-nowrap text-left">
                    <el-icon color="#409EFF">
                      <Message />
                    </el-icon>
                    <span>发送邮件</span>
                  </el-button>
                </div>
                <div>
                  <el-button v-auth="'optimizationReport/detail/scheduleMeeting'" class="rounded-button w-full whitespace-nowrap text-left">
                    <el-icon color="#67C23A">
                      <Calendar />
                    </el-icon>
                    <span>召开会议</span>
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style scoped>
  .font-pacifico {
    font-family: Pacifico, serif;
  }

  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    margin: 0;
    appearance: none;
  }

  input[type="number"] {
    appearance: textfield;
  }
</style>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
