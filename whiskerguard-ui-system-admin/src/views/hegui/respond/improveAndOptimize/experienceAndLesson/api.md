---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 21-违规举报服务/持续改进经验教训管理

## POST 创建新的持续改进经验教训

POST /whiskerguardviolationservice/api/continuous/improvement/experiences

描述：创建新的持续改进经验教训。

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "title": "string",
  "experienceCode": "string",
  "experienceType": "PROCESS_ISSUE",
  "experienceSource": "AUDIT_FINDING",
  "level": "LOW",
  "sourceCode": "string",
  "dutyEmployeeOrgId": 0,
  "label": "string",
  "status": "DRAFT",
  "problemBackground": "string",
  "eventCourse": "string",
  "problemAnalysis": "string",
  "reasonCategory": "string",
  "experienceLearned": "string",
  "improvementSuggestion": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "attachmentList": [
    {
      "id": 0,
      "relatedId": 0,
      "relatedType": 0,
      "fileName": "string",
      "filePath": "string",
      "fileType": "string",
      "fileSize": "string",
      "fileDesc": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|[ContinuousImprovementExperienceDTO](#schemacontinuousimprovementexperiencedto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "title": "",
  "experienceCode": "",
  "experienceType": "",
  "experienceSource": "",
  "level": "",
  "sourceCode": "",
  "dutyEmployeeOrgId": 0,
  "label": "",
  "status": "",
  "problemBackground": "",
  "eventCourse": "",
  "problemAnalysis": "",
  "reasonCategory": "",
  "experienceLearned": "",
  "improvementSuggestion": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "attachmentList": [
    {
      "id": 0,
      "relatedId": 0,
      "relatedType": 0,
      "fileName": "",
      "filePath": "",
      "fileType": "",
      "fileSize": "",
      "fileDesc": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityContinuousImprovementExperienceDTO](#schemaresponseentitycontinuousimprovementexperiencedto)|

# 数据模型

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_ContinuousImprovementAttachmentDTO">ContinuousImprovementAttachmentDTO</h2>

<a id="schemacontinuousimprovementattachmentdto"></a>
<a id="schema_ContinuousImprovementAttachmentDTO"></a>
<a id="tocScontinuousimprovementattachmentdto"></a>
<a id="tocscontinuousimprovementattachmentdto"></a>

```json
{
  "id": 0,
  "relatedId": 0,
  "relatedType": 0,
  "fileName": "string",
  "filePath": "string",
  "fileType": "string",
  "fileSize": "string",
  "fileDesc": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|relatedId|integer(int64)|true|none||关联ID|
|relatedType|integer|true|none||关联类型：1、经验教训 2、改进措施 3、优化报告|
|fileName|string|true|none||附件名称|
|filePath|string|true|none||附件存储路径或URL|
|fileType|string|true|none||附件类型|
|fileSize|string|false|none||附件大小|
|fileDesc|string|false|none||附件描述|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

<h2 id="tocS_ResponseEntityContinuousImprovementExperienceDTO">ResponseEntityContinuousImprovementExperienceDTO</h2>

<a id="schemaresponseentitycontinuousimprovementexperiencedto"></a>
<a id="schema_ResponseEntityContinuousImprovementExperienceDTO"></a>
<a id="tocSresponseentitycontinuousimprovementexperiencedto"></a>
<a id="tocsresponseentitycontinuousimprovementexperiencedto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "title": "string",
  "experienceCode": "string",
  "experienceType": "PROCESS_ISSUE",
  "experienceSource": "AUDIT_FINDING",
  "level": "LOW",
  "sourceCode": "string",
  "dutyEmployeeOrgId": 0,
  "label": "string",
  "status": "DRAFT",
  "problemBackground": "string",
  "eventCourse": "string",
  "problemAnalysis": "string",
  "reasonCategory": "string",
  "experienceLearned": "string",
  "improvementSuggestion": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "attachmentList": [
    {
      "id": 0,
      "relatedId": 0,
      "relatedType": 0,
      "fileName": "string",
      "filePath": "string",
      "fileType": "string",
      "fileSize": "string",
      "fileDesc": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|title|string|false|none||标题|
|experienceCode|string|true|none||编号|
|experienceType|string|false|none||类型：流程问题、人员问题、制度问题、系统问题、其他|
|experienceSource|string|false|none||来源：审计发现、员工提交、处理结果|
|level|string|false|none||优先级：高、中、低|
|sourceCode|string|true|none||来源编号|
|dutyEmployeeOrgId|integer(int64)|true|none||责任部门id|
|label|string|false|none||标签，以逗号分隔|
|status|string|false|none||状态：草稿、已发布、已归档|
|problemBackground|string|false|none||问题背景|
|eventCourse|string|false|none||事件经过|
|problemAnalysis|string|false|none||问题分析|
|reasonCategory|string|false|none||原因分类，以逗号分隔|
|experienceLearned|string|false|none||经验教训|
|improvementSuggestion|string|false|none||改进建议|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|
|attachmentList|[[ContinuousImprovementAttachmentDTO](#schemacontinuousimprovementattachmentdto)]|false|none||附件列表|

#### 枚举值

|属性|值|
|---|---|
|experienceType|PROCESS_ISSUE|
|experienceType|PERSONNEL_ISSUE|
|experienceType|INSTITUTIONAL_ISSUE|
|experienceType|SYSTEM_ISSUE|
|experienceType|OTHER|
|experienceSource|AUDIT_FINDING|
|experienceSource|EMPLOYEE_SUBMISSION|
|experienceSource|PROCESSING_RESULT|
|level|LOW|
|level|MIDDLE|
|level|HIGH|
|status|DRAFT|
|status|PUBLISHED|
|status|SAVE|

<h2 id="tocS_ContinuousImprovementExperienceDTO">ContinuousImprovementExperienceDTO</h2>

<a id="schemacontinuousimprovementexperiencedto"></a>
<a id="schema_ContinuousImprovementExperienceDTO"></a>
<a id="tocScontinuousimprovementexperiencedto"></a>
<a id="tocscontinuousimprovementexperiencedto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "title": "string",
  "experienceCode": "string",
  "experienceType": "PROCESS_ISSUE",
  "experienceSource": "AUDIT_FINDING",
  "level": "LOW",
  "sourceCode": "string",
  "dutyEmployeeOrgId": 0,
  "label": "string",
  "status": "DRAFT",
  "problemBackground": "string",
  "eventCourse": "string",
  "problemAnalysis": "string",
  "reasonCategory": "string",
  "experienceLearned": "string",
  "improvementSuggestion": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "attachmentList": [
    {
      "id": 0,
      "relatedId": 0,
      "relatedType": 0,
      "fileName": "string",
      "filePath": "string",
      "fileType": "string",
      "fileSize": "string",
      "fileDesc": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|title|string|false|none||标题|
|experienceCode|string|true|none||编号|
|experienceType|string|false|none||类型：流程问题、人员问题、制度问题、系统问题、其他|
|experienceSource|string|false|none||来源：审计发现、员工提交、处理结果|
|level|string|false|none||优先级：高、中、低|
|sourceCode|string|true|none||来源编号|
|dutyEmployeeOrgId|integer(int64)|true|none||责任部门id|
|label|string|false|none||标签，以逗号分隔|
|status|string|false|none||状态：草稿、已发布、已归档|
|problemBackground|string|false|none||问题背景|
|eventCourse|string|false|none||事件经过|
|problemAnalysis|string|false|none||问题分析|
|reasonCategory|string|false|none||原因分类，以逗号分隔|
|experienceLearned|string|false|none||经验教训|
|improvementSuggestion|string|false|none||改进建议|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|
|attachmentList|[[ContinuousImprovementAttachmentDTO](#schemacontinuousimprovementattachmentdto)]|false|none||附件列表|

#### 枚举值

|属性|值|
|---|---|
|experienceType|PROCESS_ISSUE|
|experienceType|PERSONNEL_ISSUE|
|experienceType|INSTITUTIONAL_ISSUE|
|experienceType|SYSTEM_ISSUE|
|experienceType|OTHER|
|experienceSource|AUDIT_FINDING|
|experienceSource|EMPLOYEE_SUBMISSION|
|experienceSource|PROCESSING_RESULT|
|level|LOW|
|level|MIDDLE|
|level|HIGH|
|status|DRAFT|
|status|PUBLISHED|
|status|SAVE|

