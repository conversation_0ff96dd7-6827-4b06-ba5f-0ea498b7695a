---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 21-违规举报服务/问题调查报告管理

## POST 创建新的问题调查报告

POST /whiskerguardviolationservice/api/problem/investigate/reports

描述：创建新的问题调查报告。

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "title": "string",
  "reportCode": "string",
  "investigateId": 0,
  "reportType": "SPECIAL_INVESTIGE",
  "investigateSource": "MARKETING",
  "employeeId": 0,
  "orgId": 0,
  "establishDate": "string",
  "level": "LOW",
  "summary": "string",
  "status": "MODIFY",
  "investigateBackground": "string",
  "investigateMethod": "string",
  "investigateProcess": "string",
  "investigateFound": "string",
  "investigateConclusion": "string",
  "recommendMeasure": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "attachmentList": [
    {
      "id": 0,
      "relatedId": 0,
      "relatedType": 0,
      "fileName": "string",
      "filePath": "string",
      "fileType": "string",
      "fileSize": "string",
      "fileDesc": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|[ProblemInvestigateReportDTO](#schemaprobleminvestigatereportdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "title": "",
  "reportCode": "",
  "investigateId": 0,
  "reportType": "",
  "investigateSource": "",
  "employeeId": 0,
  "orgId": 0,
  "establishDate": "",
  "level": "",
  "summary": "",
  "status": "",
  "investigateBackground": "",
  "investigateMethod": "",
  "investigateProcess": "",
  "investigateFound": "",
  "investigateConclusion": "",
  "recommendMeasure": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "attachmentList": [
    {
      "id": 0,
      "relatedId": 0,
      "relatedType": 0,
      "fileName": "",
      "filePath": "",
      "fileType": "",
      "fileSize": "",
      "fileDesc": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityProblemInvestigateReportDTO](#schemaresponseentityprobleminvestigatereportdto)|

# 数据模型

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_ProblemInvestigateAttachmentDTO">ProblemInvestigateAttachmentDTO</h2>

<a id="schemaprobleminvestigateattachmentdto"></a>
<a id="schema_ProblemInvestigateAttachmentDTO"></a>
<a id="tocSprobleminvestigateattachmentdto"></a>
<a id="tocsprobleminvestigateattachmentdto"></a>

```json
{
  "id": 0,
  "relatedId": 0,
  "relatedType": 0,
  "fileName": "string",
  "filePath": "string",
  "fileType": "string",
  "fileSize": "string",
  "fileDesc": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|relatedId|integer(int64)|true|none||关联ID|
|relatedType|integer|true|none||关联类型：1、调查记录 2、调查报告|
|fileName|string|true|none||附件名称|
|filePath|string|true|none||附件存储路径或URL|
|fileType|string|true|none||附件类型|
|fileSize|string|false|none||附件大小|
|fileDesc|string|false|none||附件描述|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

<h2 id="tocS_ResponseEntityProblemInvestigateReportDTO">ResponseEntityProblemInvestigateReportDTO</h2>

<a id="schemaresponseentityprobleminvestigatereportdto"></a>
<a id="schema_ResponseEntityProblemInvestigateReportDTO"></a>
<a id="tocSresponseentityprobleminvestigatereportdto"></a>
<a id="tocsresponseentityprobleminvestigatereportdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "title": "string",
  "reportCode": "string",
  "investigateId": 0,
  "reportType": "SPECIAL_INVESTIGE",
  "investigateSource": "MARKETING",
  "employeeId": 0,
  "orgId": 0,
  "establishDate": "string",
  "level": "LOW",
  "summary": "string",
  "status": "MODIFY",
  "investigateBackground": "string",
  "investigateMethod": "string",
  "investigateProcess": "string",
  "investigateFound": "string",
  "investigateConclusion": "string",
  "recommendMeasure": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "attachmentList": [
    {
      "id": 0,
      "relatedId": 0,
      "relatedType": 0,
      "fileName": "string",
      "filePath": "string",
      "fileType": "string",
      "fileSize": "string",
      "fileDesc": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|title|string|false|none||报告标题|
|reportCode|string|true|none||调查编号|
|investigateId|integer(int64)|true|none||违规调查id|
|reportType|string|false|none||报告类型：专项调查报告|
|investigateSource|string|false|none||调查来源：市场部、采购部、人力资源部、财务部|
|employeeId|integer(int64)|true|none||编制人id|
|orgId|integer(int64)|true|none||编制部门id|
|establishDate|string|false|none||编制日期|
|level|string|false|none||优先级：高、中、低|
|summary|string|false|none||摘要|
|status|string|false|none||状态：需修改、待审查、发布、审核中、已撤回|
|investigateBackground|string|false|none||调查背景|
|investigateMethod|string|false|none||调查方法|
|investigateProcess|string|false|none||调查过程|
|investigateFound|string|false|none||调查发现|
|investigateConclusion|string|false|none||调查结论|
|recommendMeasure|string|false|none||建议措施|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|
|attachmentList|[[ProblemInvestigateAttachmentDTO](#schemaprobleminvestigateattachmentdto)]|false|none||附件列表|

#### 枚举值

|属性|值|
|---|---|
|reportType|SPECIAL_INVESTIGE|
|investigateSource|MARKETING|
|investigateSource|PROCUREMENT|
|investigateSource|HR|
|investigateSource|FINANCE|
|level|LOW|
|level|MIDDLE|
|level|HIGH|
|status|MODIFY|
|status|PENDING|
|status|PUBLISHED|
|status|REVIEWING|
|status|REVOKE|

<h2 id="tocS_ProblemInvestigateReportDTO">ProblemInvestigateReportDTO</h2>

<a id="schemaprobleminvestigatereportdto"></a>
<a id="schema_ProblemInvestigateReportDTO"></a>
<a id="tocSprobleminvestigatereportdto"></a>
<a id="tocsprobleminvestigatereportdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "title": "string",
  "reportCode": "string",
  "investigateId": 0,
  "reportType": "SPECIAL_INVESTIGE",
  "investigateSource": "MARKETING",
  "employeeId": 0,
  "orgId": 0,
  "establishDate": "string",
  "level": "LOW",
  "summary": "string",
  "status": "MODIFY",
  "investigateBackground": "string",
  "investigateMethod": "string",
  "investigateProcess": "string",
  "investigateFound": "string",
  "investigateConclusion": "string",
  "recommendMeasure": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "attachmentList": [
    {
      "id": 0,
      "relatedId": 0,
      "relatedType": 0,
      "fileName": "string",
      "filePath": "string",
      "fileType": "string",
      "fileSize": "string",
      "fileDesc": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|title|string|false|none||报告标题|
|reportCode|string|true|none||调查编号|
|investigateId|integer(int64)|true|none||违规调查id|
|reportType|string|false|none||报告类型：专项调查报告|
|investigateSource|string|false|none||调查来源：市场部、采购部、人力资源部、财务部|
|employeeId|integer(int64)|true|none||编制人id|
|orgId|integer(int64)|true|none||编制部门id|
|establishDate|string|false|none||编制日期|
|level|string|false|none||优先级：高、中、低|
|summary|string|false|none||摘要|
|status|string|false|none||状态：需修改、待审查、发布、审核中、已撤回|
|investigateBackground|string|false|none||调查背景|
|investigateMethod|string|false|none||调查方法|
|investigateProcess|string|false|none||调查过程|
|investigateFound|string|false|none||调查发现|
|investigateConclusion|string|false|none||调查结论|
|recommendMeasure|string|false|none||建议措施|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|
|attachmentList|[[ProblemInvestigateAttachmentDTO](#schemaprobleminvestigateattachmentdto)]|false|none||附件列表|

#### 枚举值

|属性|值|
|---|---|
|reportType|SPECIAL_INVESTIGE|
|investigateSource|MARKETING|
|investigateSource|PROCUREMENT|
|investigateSource|HR|
|investigateSource|FINANCE|
|level|LOW|
|level|MIDDLE|
|level|HIGH|
|status|MODIFY|
|status|PENDING|
|status|PUBLISHED|
|status|REVIEWING|
|status|REVOKE|

