<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { ref } from 'vue'

// 当前用户和日期
const currentUser = ref('张明远')
const currentDate = ref('2023-11-15')

// 基本信息
const confidentialLevel = ref('普通')

// 折叠面板
const activeCollapse = ref([])

// 文件上传
const fileList = ref([
  { name: '访谈记录.pdf', size: '2.4MB' },
  { name: '证据材料.zip', size: '15.6MB' },
])

function handleRemove(file, fileList) {
  console.log(file, fileList)
}

function handlePreview(file) {
  console.log(file)
}

// 报告格式设置
const showPageNumber = ref(true)
const showCover = ref(true)
const showSignature = ref(true)

// 审批流程设置
const approvalFlow = ref([
  { order: 1, role: '合规主管', person: '李伟', required: true },
  { order: 2, role: '法务负责人', person: '王芳', required: true },
  { order: 3, role: 'CEO', person: '张强', required: false },
])

function addApprovalStep() {
  approvalFlow.value.push({
    order: approvalFlow.value.length + 1,
    role: '',
    person: '',
    required: true,
  })
}

function removeApproval(index) {
  approvalFlow.value.splice(index, 1)
  // 重新排序
  approvalFlow.value.forEach((item, i) => {
    item.order = i + 1
  })
}

function moveUp(index) {
  if (index > 0) {
    const temp = approvalFlow.value[index]
    approvalFlow.value[index] = approvalFlow.value[index - 1]
    approvalFlow.value[index - 1] = temp
    // 重新排序
    approvalFlow.value.forEach((item, i) => {
      item.order = i + 1
    })
  }
}

function moveDown(index) {
  if (index < approvalFlow.value.length - 1) {
    const temp = approvalFlow.value[index]
    approvalFlow.value[index] = approvalFlow.value[index + 1]
    approvalFlow.value[index + 1] = temp
    // 重新排序
    approvalFlow.value.forEach((item, i) => {
      item.order = i + 1
    })
  }
}

const approvalTimeLimit = ref(3)
const enableReminder = ref(true)
const reminderInterval = ref(2)

// 通知与权限设置
const personOptions = [
  { value: '1', label: '张明远 (合规部)' },
  { value: '2', label: '李伟 (合规主管)' },
  { value: '3', label: '王芳 (法务部)' },
  { value: '4', label: '张强 (CEO)' },
  { value: '5', label: '刘洋 (财务部)' },
]

const notifyPersons = ref(['1', '2'])
const notifyMethods = ref(['系统消息', '电子邮件'])
const viewPermission = ref('仅审批人员')
const specifiedViewers = ref([])
const exportPermission = ref('需审批')
const commentPermission = ref('允许')
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              新增调查报告
            </h1>
            <!-- <el-tag type="warning" class="ml-4">进行中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="'reportManagement/addEdit/saveDraft'" type="primary" class="!rounded-button whitespace-nowrap">
              <i class="el-icon-document mr-1" />保存草稿
            </el-button>
            <el-button v-auth="'reportManagement/addEdit/submitReview'" class="!rounded-button whitespace-nowrap">
              <i class="el-icon-check mr-1" />提交审核
            </el-button>
            <el-button v-auth="'reportManagement/addEdit/cancel'" class="!rounded-button whitespace-nowrap">
              <i class="el-icon-close mr-1" />取消
            </el-button>
            <el-button v-auth="'reportManagement/addEdit/preview'" class="!rounded-button whitespace-nowrap">
              <i class="el-icon-view mr-1" />预览
            </el-button>
          </div>
        </div>
      </template>
    </page-header>

    <PageMain style="background-color: transparent;">
      <div>
        <!-- 基本信息编辑区 -->
        <el-card class="mb-6">
          <template #header>
            <div class="f-16 fw-600">
              基本信息
            </div>
          </template>
          <el-form label-position="right" label-width="120px">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="报告标题" required>
                  <el-input placeholder="请输入报告标题" class="h-8" />
                </el-form-item>
                <el-form-item label="报告编号">
                  <div class="flex items-center">
                    <el-input placeholder="系统自动生成" class="h-8 flex-1" />
                    <el-checkbox class="ml-2">
                      自动生成
                    </el-checkbox>
                  </div>
                </el-form-item>
                <el-form-item label="关联调查" required>
                  <el-select placeholder="请选择关联调查" class="h-8 w-full">
                    <el-option label="2023-Q3 财务合规调查" value="1" />
                    <el-option label="2023-Q2 数据安全调查" value="2" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="报告类型">
                  <el-select placeholder="请选择报告类型" class="h-8 w-full">
                    <el-option label="完整报告" value="1" />
                    <el-option label="简要报告" value="2" />
                    <el-option label="专项报告" value="3" />
                    <el-option label="阶段报告" value="4" />
                    <el-option label="其他" value="5" />
                  </el-select>
                </el-form-item>
                <el-form-item label="编制人">
                  <el-input :value="currentUser" disabled class="h-8" />
                </el-form-item>
                <el-form-item label="编制日期">
                  <el-input :value="currentDate" disabled class="h-8" />
                </el-form-item>
                <el-form-item label="保密级别">
                  <el-radio-group v-model="confidentialLevel">
                    <el-radio label="普通" />
                    <el-radio label="保密" />
                    <el-radio label="高度保密" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <!-- 报告内容编辑区 -->
        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              调查报告摘要
            </div>
          </template>
          <el-input type="textarea" :rows="8" placeholder="请输入调查报告摘要，建议300-500字" resize="none" />
        </el-card>

        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              一、调查背景
            </div>
          </template>
          <el-collapse v-model="activeCollapse">
            <el-collapse-item title="调查缘起" name="1">
              <el-input type="textarea" :rows="4" placeholder="请输入调查缘起" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="调查对象" name="2">
              <el-input type="textarea" :rows="4" placeholder="请输入调查对象" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="调查范围" name="3">
              <el-input type="textarea" :rows="4" placeholder="请输入调查范围" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="调查目标" name="4">
              <el-input type="textarea" :rows="4" placeholder="请输入调查目标" resize="none" />
            </el-collapse-item>
          </el-collapse>
        </el-card>
        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              二、调查方法
            </div>
          </template>
          <el-collapse v-model="activeCollapse">
            <el-collapse-item title="调查团队组成" name="5">
              <el-input type="textarea" :rows="4" placeholder="请输入调查团队组成" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="调查步骤" name="6">
              <el-input type="textarea" :rows="4" placeholder="请输入调查步骤" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="数据收集方法" name="7">
              <el-input type="textarea" :rows="4" placeholder="请输入数据收集方法" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="证据收集与保存方法" name="8">
              <el-input type="textarea" :rows="4" placeholder="请输入证据收集与保存方法" resize="none" />
            </el-collapse-item>
          </el-collapse>
        </el-card>
        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              三、调查过程
            </div>
          </template>
          <el-collapse v-model="activeCollapse">
            <el-collapse-item title="关键调查活动" name="9">
              <el-input type="textarea" :rows="4" placeholder="请输入关键调查活动" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="人员访谈概述" name="10">
              <el-input type="textarea" :rows="4" placeholder="请输入人员访谈概述" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="文件审阅概述" name="11">
              <el-input type="textarea" :rows="4" placeholder="请输入文件审阅概述" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="现场调查概述" name="12">
              <el-input type="textarea" :rows="4" placeholder="请输入现场调查概述" resize="none" />
            </el-collapse-item>
          </el-collapse>
          <div class="mt-4">
            <el-button type="primary" plain class="!rounded-button whitespace-nowrap">
              <i class="el-icon-upload mr-1" />自动导入活动记录
            </el-button>
          </div>
        </el-card>
        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              四、调查发现
            </div>
          </template>

          <el-collapse v-model="activeCollapse">
            <el-collapse-item title="事实发现" name="13">
              <el-input type="textarea" :rows="4" placeholder="请输入事实发现" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="证据分析" name="14">
              <el-input type="textarea" :rows="4" placeholder="请输入证据分析" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="违规行为认定" name="15">
              <el-input type="textarea" :rows="4" placeholder="请输入违规行为认定" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="原因分析" name="16">
              <el-input type="textarea" :rows="4" placeholder="请输入原因分析" resize="none" />
            </el-collapse-item>
          </el-collapse>
          <div class="mt-4">
            <el-button type="primary" plain class="!rounded-button whitespace-nowrap">
              <i class="el-icon-upload mr-1" />导入调查发现
            </el-button>
          </div>
        </el-card>
        <!-- 调查结论 -->
        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              五、调查结论
            </div>
          </template>
          <el-collapse v-model="activeCollapse">
            <el-collapse-item title="结论概述" name="17">
              <el-input type="textarea" :rows="4" placeholder="请输入结论概述" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="违规性质和严重程度" name="18">
              <el-input type="textarea" :rows="4" placeholder="请输入违规性质和严重程度" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="责任认定" name="19">
              <el-input type="textarea" :rows="4" placeholder="请输入责任认定" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="影响评估" name="20">
              <el-input type="textarea" :rows="4" placeholder="请输入影响评估" resize="none" />
            </el-collapse-item>
          </el-collapse>
          <div class="mt-4">
            <el-button type="primary" plain class="!rounded-button whitespace-nowrap">
              <i class="el-icon-upload mr-1" />导入调查结论
            </el-button>
          </div>
        </el-card>
        <!-- 建议措施 -->
        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              六、建议措施
            </div>
          </template>
          <el-collapse v-model="activeCollapse">
            <el-collapse-item title="具体措施建议" name="21">
              <el-input type="textarea" :rows="4" placeholder="请输入具体措施建议" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="优先级设置" name="22">
              <el-input type="textarea" :rows="4" placeholder="请输入优先级设置" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="责任部门推荐" name="23">
              <el-input type="textarea" :rows="4" placeholder="请输入责任部门推荐" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="实施时间建议" name="24">
              <el-input type="textarea" :rows="4" placeholder="请输入实施时间建议" resize="none" />
            </el-collapse-item>
            <el-collapse-item title="预期效果" name="25">
              <el-input type="textarea" :rows="4" placeholder="请输入预期效果" resize="none" />
            </el-collapse-item>
          </el-collapse>
          <div class="mt-4">
            <el-button type="primary" plain class="!rounded-button whitespace-nowrap">
              <i class="el-icon-plus mr-1" />添加措施
            </el-button>
          </div>
        </el-card>
        <!-- 附件 -->
        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              七、附件
            </div>
          </template>
          <el-upload
            class="upload-demo" action="https://jsonplaceholder.typicode.com/posts/"
            :on-preview="handlePreview" :on-remove="handleRemove" :file-list="fileList" multiple
          >
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <i class="el-icon-upload mr-1" />添加附件
            </el-button>
          </el-upload>
          <div class="mt-4">
            <el-button type="primary" plain class="!rounded-button whitespace-nowrap">
              <i class="el-icon-search mr-1" />从调查证据选择
            </el-button>
          </div>
        </el-card>
        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              报告格式
            </div>
          </template>
          <el-form label-position="right" label-width="120px">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="报告模板">
                  <el-select placeholder="请选择报告模板" class="h-8 w-full">
                    <el-option label="标准模板" value="1" />
                    <el-option label="简洁模板" value="2" />
                    <el-option label="详细模板" value="3" />
                    <el-option label="自定义模板" value="4" />
                  </el-select>
                </el-form-item>
                <el-form-item label="字体设置">
                  <el-select placeholder="请选择字体" class="h-8 w-full">
                    <el-option label="宋体" value="1" />
                    <el-option label="黑体" value="2" />
                    <el-option label="微软雅黑" value="3" />
                    <el-option label="Arial" value="4" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="字号设置">
                  <el-select placeholder="请选择字号" class="h-8 w-full">
                    <el-option label="小号" value="1" />
                    <el-option label="标准" value="2" />
                    <el-option label="大号" value="3" />
                  </el-select>
                </el-form-item>
                <el-form-item label="显示页码">
                  <el-switch v-model="showPageNumber" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="页眉内容">
              <el-input placeholder="请输入页眉内容" class="h-8" />
            </el-form-item>
            <el-form-item label="页脚内容">
              <el-input placeholder="请输入页脚内容" class="h-8" />
            </el-form-item>
            <el-form-item label="显示封面">
              <el-switch v-model="showCover" />
            </el-form-item>
            <el-form-item v-if="showCover" label="封面图片">
              <el-upload
                class="upload-demo" action="https://jsonplaceholder.typicode.com/posts/"
                :show-file-list="false"
              >
                <el-button type="primary" class="!rounded-button whitespace-nowrap">
                  <i class="el-icon-upload mr-1" />上传封面
                </el-button>
              </el-upload>
            </el-form-item>
            <el-form-item label="显示签名区域">
              <el-switch v-model="showSignature" />
            </el-form-item>
          </el-form>
        </el-card>
        <!-- 审批流程设置区 -->
        <el-card class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              审批流程
            </div>
          </template>
          <el-form label-position="right" label-width="120px">
            <el-form-item label="审批流程">
              <el-select placeholder="请选择审批流程" class="h-8 w-full">
                <el-option label="标准流程" value="1" />
                <el-option label="简化流程" value="2" />
                <el-option label="严格流程" value="3" />
                <el-option label="自定义流程" value="4" />
              </el-select>
            </el-form-item>
            <el-form-item label="审批人设置">
              <el-table :data="approvalFlow" border style="width: 100%;">
                <el-table-column prop="order" label="审批顺序" width="100" />
                <el-table-column prop="role" label="审批角色" />
                <el-table-column prop="person" label="审批人" />
                <el-table-column prop="required" label="必选" width="80">
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.required" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180">
                  <template #default="scope">
                    <el-button size="mini" @click="moveUp(scope.$index)">
                      <i class="el-icon-top" />
                    </el-button>
                    <el-button size="mini" @click="moveDown(scope.$index)">
                      <i class="el-icon-bottom" />
                    </el-button>
                    <el-button size="mini" type="danger" @click="removeApproval(scope.$index)">
                      <i class="el-icon-delete" />
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="mt-2">
                <el-button type="primary" plain class="!rounded-button whitespace-nowrap" @click="addApprovalStep">
                  <i class="el-icon-plus mr-1" />添加审批步骤
                </el-button>
              </div>
            </el-form-item>
            <el-form-item label="审批时限">
              <el-input-number v-model="approvalTimeLimit" :min="1" :max="30" label="天" />
            </el-form-item>
            <el-form-item label="催办设置">
              <el-switch v-model="enableReminder" />
              <span class="ml-2 text-sm text-gray-500">超时自动催办</span>
            </el-form-item>
            <el-form-item v-if="enableReminder" label="催办间隔">
              <el-input-number v-model="reminderInterval" :min="1" :max="7" label="天" />
            </el-form-item>
          </el-form>
        </el-card>
        <!-- 通知与权限设置区 -->
        <el-card class="mt-20">
          <template #header>
            <div class="text-base font-bold">
              通知与权限
            </div>
          </template>
          <el-form label-position="right" label-width="120px">
            <el-form-item label="通知对象">
              <el-select v-model="notifyPersons" multiple placeholder="请选择通知对象" class="w-full">
                <el-option
                  v-for="item in personOptions" :key="item.value" :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="通知方式">
              <el-checkbox-group v-model="notifyMethods">
                <el-checkbox label="系统消息" />
                <el-checkbox label="电子邮件" />
                <el-checkbox label="短信" />
                <el-checkbox label="其他" />
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="查看权限">
              <el-radio-group v-model="viewPermission">
                <el-radio label="仅审批人员" />
                <el-radio label="部门主管" />
                <el-radio label="全公司" />
                <el-radio label="指定人员" />
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="viewPermission === '指定人员'" label="指定查看人员">
              <el-select v-model="specifiedViewers" multiple placeholder="请选择指定查看人员" class="w-full">
                <el-option
                  v-for="item in personOptions" :key="item.value" :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="导出权限">
              <el-radio-group v-model="exportPermission">
                <el-radio label="允许" />
                <el-radio label="不允许" />
                <el-radio label="需审批" />
              </el-radio-group>
            </el-form-item>
            <el-form-item label="评论权限">
              <el-radio-group v-model="commentPermission">
                <el-radio label="允许" />
                <el-radio label="不允许" />
                <el-radio label="需审批" />
              </el-radio-group>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-form-item {
    margin-bottom: 24px;
  }

  .el-collapse-item__header {
    font-weight: 500;
  }

  .el-table {
    margin-top: 10px;
  }

  .upload-demo {
    margin-top: 10px;
  }
</style>
