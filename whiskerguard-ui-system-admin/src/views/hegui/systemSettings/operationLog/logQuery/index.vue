<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import {
  // Notebook2 as ElIconNotebook2,
  Search as ElIconSearch,
  Download as ElIconDownload,
  Refresh as ElIconRefresh,
} from '@element-plus/icons-vue'

// 筛选条件
const filter = ref({
  type: '',
  timeRange: '',
  users: [],
  modules: [],
  result: '',
  keyword: '',
})

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(248)

// 加载状态
const loading = ref(false)

// 图表引用
const chart1 = ref<HTMLElement>()

// 日志数据
const logData = ref([
  {
    time: '2023-06-15 09:23:45',
    user: '张伟',
    type: 'login',
    module: '系统登录',
    content: '用户登录系统',
    ip: '*************',
    result: 'success',
  },
  {
    time: '2023-06-15 10:12:33',
    user: '李娜',
    type: 'add',
    module: '用户管理',
    content: '新增用户: 王芳',
    ip: '*************',
    result: 'success',
  },
  {
    time: '2023-06-15 11:45:21',
    user: '王芳',
    type: 'edit',
    module: '权限管理',
    content: '修改角色权限: 管理员',
    ip: '*************',
    result: 'success',
  },
  {
    time: '2023-06-15 14:30:15',
    user: '刘强',
    type: 'delete',
    module: '数据管理',
    content: '删除数据记录: ID=10023',
    ip: '*************',
    result: 'fail',
  },
  {
    time: '2023-06-15 15:22:08',
    user: '陈明',
    type: 'export',
    module: '日志管理',
    content: '导出操作日志',
    ip: '*************',
    result: 'success',
  },
  {
    time: '2023-06-15 16:10:45',
    user: '张伟',
    type: 'query',
    module: '系统设置',
    content: '查询系统参数',
    ip: '*************',
    result: 'success',
  },
  {
    time: '2023-06-15 17:35:22',
    user: '李娜',
    type: 'logout',
    module: '系统登录',
    content: '用户退出系统',
    ip: '*************',
    result: 'success',
  },
])

// 异常操作数据
const errorLogs = ref([
  {
    time: '09:23:45',
    user: '张伟',
    action: '删除数据记录',
    reason: '权限不足',
  },
  {
    time: '10:12:33',
    user: '李娜',
    action: '修改系统参数',
    reason: '参数格式错误',
  },
  {
    time: '11:45:21',
    user: '王芳',
    action: '导出敏感数据',
    reason: '超出每日限额',
  },
])

// 当前激活菜单
const activeMenu = ref('6')

// 重置筛选条件
function resetFilter() {
  filter.value = {
    type: '',
    timeRange: '',
    users: [],
    modules: [],
    result: '',
    keyword: '',
  }
}

// 执行筛选
function search() {
  loading.value = true
  // 模拟API请求
  setTimeout(() => {
    loading.value = false
  }, 800)
}

// 初始化图表
function initChart() {
  nextTick(() => {
    if (chart1.value) {
      const myChart = echarts.init(chart1.value)
      const option = {
        animation: false,
        tooltip: {
          trigger: 'item',
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
        },
        series: [
          {
            name: '操作类型',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 320, name: '登录' },
              { value: 240, name: '查询' },
              { value: 149, name: '新增' },
              { value: 100, name: '修改' },
              { value: 59, name: '删除' },
              { value: 80, name: '导出' },
            ],
          },
        ],
      }
      myChart.setOption(option)

      // 响应式调整
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  })
}

onMounted(() => {
  initChart()
})
</script>

<template>
  <div class="absolute-container" style="">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              操作日志
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="space-x-3">
            <el-button class="!rounded-button whitespace-nowrap" type="primary" plain>
              <el-icon class="mr-1">
                <ElIconDownload />
              </el-icon>
              导出日志
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" type="primary" plain>
              <el-icon class="mr-1">
                <ElIconRefresh />
              </el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <!-- 筛选条件 -->
              <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
                <div class="grid grid-cols-3 gap-4">
                  <div>
                    <div class="mb-1 text-sm text-gray-500">
                      操作类型
                    </div>
                    <el-select v-model="filter.type" placeholder="请选择操作类型" class="w-full" clearable>
                      <el-option label="登录" value="login" />
                      <el-option label="退出" value="logout" />
                      <el-option label="查询" value="query" />
                      <el-option label="新增" value="add" />
                      <el-option label="修改" value="edit" />
                      <el-option label="删除" value="delete" />
                      <el-option label="导出" value="export" />
                    </el-select>
                  </div>
                  <div>
                    <div class="mb-1 text-sm text-gray-500">
                      时间范围
                    </div>
                    <el-select v-model="filter.timeRange" placeholder="请选择时间范围" class="w-full" clearable>
                      <el-option label="今天" value="today" />
                      <el-option label="昨天" value="yesterday" />
                      <el-option label="最近7天" value="7days" />
                      <el-option label="最近30天" value="30days" />
                      <el-option label="自定义" value="custom" />
                    </el-select>
                  </div>
                  <div>
                    <div class="mb-1 text-sm text-gray-500">
                      操作人员
                    </div>
                    <el-select v-model="filter.users" placeholder="请选择操作人员" class="w-full" multiple clearable>
                      <el-option label="张伟" value="zhangwei" />
                      <el-option label="李娜" value="lina" />
                      <el-option label="王芳" value="wangfang" />
                      <el-option label="刘强" value="liuqiang" />
                      <el-option label="陈明" value="chenming" />
                    </el-select>
                  </div>
                  <div>
                    <div class="mb-1 text-sm text-gray-500">
                      操作模块
                    </div>
                    <el-select v-model="filter.modules" placeholder="请选择操作模块" class="w-full" multiple clearable>
                      <el-option label="用户管理" value="user" />
                      <el-option label="权限管理" value="permission" />
                      <el-option label="系统设置" value="system" />
                      <el-option label="数据管理" value="data" />
                      <el-option label="日志管理" value="log" />
                    </el-select>
                  </div>
                  <div>
                    <div class="mb-1 text-sm text-gray-500">
                      操作结果
                    </div>
                    <el-select v-model="filter.result" placeholder="请选择操作结果" class="w-full" clearable>
                      <el-option label="成功" value="success" />
                      <el-option label="失败" value="fail" />
                    </el-select>
                  </div>
                  <div>
                    <div class="mb-1 text-sm text-gray-500">
                      搜索内容
                    </div>
                    <el-input v-model="filter.keyword" placeholder="搜索操作内容、用户..." clearable>
                      <template #prefix>
                        <el-icon><ElIconSearch /></el-icon>
                      </template>
                    </el-input>
                  </div>
                </div>
                <div class="mt-4 flex justify-end space-x-3">
                  <el-button class="!rounded-button whitespace-nowrap" @click="resetFilter">
                    重置
                  </el-button>
                  <el-button class="!rounded-button whitespace-nowrap" type="primary" @click="search">
                    筛选
                  </el-button>
                </div>
              </div>

              <!-- 日志表格 -->
              <div class="overflow-hidden rounded-lg bg-white shadow-sm">
                <el-table v-loading="loading" :data="logData" style="width: 100%;" stripe border>
                  <el-table-column prop="time" label="操作时间" width="180" sortable />
                  <el-table-column prop="user" label="操作用户" width="120" />
                  <el-table-column prop="type" label="操作类型" width="120">
                    <template #default="{ row }">
                      <el-tag
                        :type="{
                          login: 'success',
                          logout: 'info',
                          query: '',
                          add: 'warning',
                          edit: 'warning',
                          delete: 'danger',
                          export: 'primary',
                        }[row.type]" size="small"
                      >
                        {{ {
                          login: '登录',
                          logout: '退出',
                          query: '查询',
                          add: '新增',
                          edit: '修改',
                          delete: '删除',
                          export: '导出',
                        }[row.type] }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="module" label="操作模块" width="120" />
                  <el-table-column prop="content" label="操作内容摘要" min-width="200" />
                  <el-table-column prop="ip" label="操作IP" width="150" />
                  <el-table-column prop="result" label="操作结果" width="100">
                    <template #default="{ row }">
                      <el-tag :type="row.result === 'success' ? 'success' : 'danger'" size="small">
                        {{ row.result === 'success' ? '成功' : '失败' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="80" fixed="right">
                    <template #default>
                      <el-button type="text" size="small">
                        详情
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="flex items-center justify-between p-4">
                  <div class="text-sm text-gray-500">
                    共 {{ total }} 条记录
                  </div>
                  <el-pagination
                    v-model:current-page="currentPage" v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total"
                  />
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  日志统计
                </div>
              </template>
              <div class="grid grid-cols-2 gap-4">
                <div class="rounded-lg bg-blue-50 p-4">
                  <div class="text-sm text-gray-500">
                    总日志数
                  </div>
                  <div class="mt-1 text-2xl font-bold">
                    1,248
                  </div>
                </div>
                <div class="rounded-lg bg-green-50 p-4">
                  <div class="text-sm text-gray-500">
                    今日日志
                  </div>
                  <div class="mt-1 text-2xl font-bold">
                    86
                  </div>
                </div>
                <div class="rounded-lg bg-purple-50 p-4">
                  <div class="text-sm text-gray-500">
                    成功操作
                  </div>
                  <div class="mt-1 text-2xl font-bold">
                    1,120
                  </div>
                </div>
                <div class="rounded-lg bg-red-50 p-4">
                  <div class="text-sm text-gray-500">
                    失败操作
                  </div>
                  <div class="mt-1 text-2xl font-bold">
                    128
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  操作类型分布
                </div>
              </template>
              <div ref="chart1" class="h-64" />
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  异常操作
                </div>
              </template>
              <div class="space-y-4">
                <div v-for="(item, index) in errorLogs" :key="index" class="border-b pb-3 last:border-b-0">
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-500">{{ item.time }}</span>
                    <el-tag type="danger" size="small">
                      {{ item.user }}
                    </el-tag>
                  </div>
                  <div class="mt-1 text-sm">
                    {{ item.action }}
                  </div>
                  <div class="mt-1 text-xs text-red-500">
                    {{ item.reason }}
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-menu {
    border-right: none;
  }

  .el-table {
    --el-table-border-color: #f0f0f0;
  }

  .el-table :deep(.el-table__header-wrapper) th {
    background-color: #f5f7fa;
  }

  .el-pagination {
    justify-content: flex-end;
  }

  .el-select,
  .el-input {
    width: 100%;
  }
</style>
