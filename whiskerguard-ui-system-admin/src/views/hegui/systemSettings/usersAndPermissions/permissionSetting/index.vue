<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { computed, nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import { ElMessage, ElMessageBox, ElTable, ElTag, ElTree } from 'element-plus'
import {
  Download as ElIconDownload,
  Plus as ElIconPlus,
  Refresh as ElIconRefresh,
  Search as ElIconSearch,
} from '@element-plus/icons-vue'

// 菜单状态
const activeMenu = ref('1-2-3')
const activeTab = ref('function')

// 角色选择
const selectedRole = ref('admin')

// 模块树
const moduleTree = ref<InstanceType<typeof ElTree>>()
const modules = ref([
  {
    id: '1',
    label: '系统管理',
    description: '系统基础功能管理',
    children: [
      {
        id: '1-1',
        label: '用户管理',
        description: '系统用户管理功能',
      },
      {
        id: '1-2',
        label: '角色管理',
        description: '系统角色管理功能',
      },
      {
        id: '1-3',
        label: '权限设置',
        description: '系统权限配置功能',
      },
    ],
  },
  {
    id: '2',
    label: '业务管理',
    description: '业务相关功能模块',
    children: [
      {
        id: '2-1',
        label: '订单管理',
        description: '订单处理功能',
      },
      {
        id: '2-2',
        label: '客户管理',
        description: '客户信息管理',
      },
    ],
  },
])
const defaultProps = {
  children: 'children',
  label: 'label',
}

// 当前选中的模块
const currentModule = ref<any>(null)
const permissions = ref<any[]>([])
const selectedPermissions = ref<any[]>([])

// 数据权限
const dataScope = ref('全部数据')
const orgTree = ref([
  {
    id: '1',
    label: '总公司',
    children: [
      {
        id: '1-1',
        label: '技术部',
        children: [
          { id: '1-1-1', label: '前端组' },
          { id: '1-1-2', label: '后端组' },
        ],
      },
      {
        id: '1-2',
        label: '市场部',
        children: [
          { id: '1-2-1', label: '品牌组' },
          { id: '1-2-2', label: '运营组' },
        ],
      },
    ],
  },
])
const selectedOrgs = ref<any[]>([])
const functionScopes = ref([
  { module: '用户管理', scope: 'all' },
  { module: '角色管理', scope: 'department' },
  { module: '权限设置', scope: 'custom' },
])
const fieldPermissions = ref([
  { table: '用户表', field: '手机号', permission: 'visible' },
  { table: '用户表', field: '邮箱', permission: 'editable' },
  { table: '用户表', field: '密码', permission: 'hidden' },
])

// 权限模板
const templates = ref([
  {
    id: '1',
    name: '管理员模板',
    description: '系统管理员权限模板',
    createdAt: '2023-05-10',
    permissions: [
      { name: '用户管理', type: '菜单' },
      { name: '角色管理', type: '菜单' },
      { name: '权限设置', type: '菜单' },
    ],
  },
  {
    id: '2',
    name: '普通员工模板',
    description: '普通员工权限模板',
    createdAt: '2023-05-12',
    permissions: [
      { name: '客户管理', type: '菜单' },
      { name: '订单管理', type: '菜单' },
    ],
  },
])
const showTemplateDialog = ref(false)
const newTemplate = ref({
  name: '',
  description: '',
  selectedPermissions: [],
})
const allPermissions = computed(() => {
  return permissions.value.map(p => ({
    key: p.code,
    label: `${p.name} (${p.type})`,
  }))
})
const showApplyDialog = ref(false)
const selectedTemplate = ref<any>(null)

// 变更记录
const changeLogs = ref([
  {
    operator: '张明',
    time: '2023-06-15 14:30',
    action: '更新了"系统管理员"角色的权限设置',
  },
  {
    operator: '李华',
    time: '2023-06-14 10:15',
    action: '创建了"财务人员"权限模板',
  },
  {
    operator: '王伟',
    time: '2023-06-12 16:45',
    action: '调整了"市场部"的数据权限范围',
  },
])

// 图表
const pieChart = ref<HTMLElement>()
let pieChartInstance: echarts.ECharts | null = null

// 方法
function handleNodeClick(data: any) {
  currentModule.value = data
  // 模拟加载权限数据
  permissions.value = [
    { code: `${data.id}-view`, name: '查看', type: '菜单', description: '查看该模块内容' },
    { code: `${data.id}-add`, name: '新增', type: '按钮', description: '新增内容' },
    { code: `${data.id}-edit`, name: '编辑', type: '按钮', description: '编辑内容' },
    { code: `${data.id}-delete`, name: '删除', type: '按钮', description: '删除内容' },
    { code: `${data.id}-export`, name: '导出', type: 'API', description: '导出数据' },
  ]
}

function getTagType(type: string) {
  const map: Record<string, string> = {
    菜单: '',
    按钮: 'success',
    API: 'warning',
    其他: 'info',
  }
  return map[type] || ''
}

function handleSelectionChange(val: any[]) {
  selectedPermissions.value = val
}

function selectAll() {
  permissions.value.forEach((row) => {
    moduleTree.value?.setChecked(row.code, true)
  })
}

function unselectAll() {
  permissions.value.forEach((row) => {
    moduleTree.value?.setChecked(row.code, false)
  })
}

function reverseSelect() {
  permissions.value.forEach((row) => {
    const isChecked = moduleTree.value?.getCheckedNodes().some((n: any) => n.code === row.code)
    moduleTree.value?.setChecked(row.code, !isChecked)
  })
}

function handleOrgCheckChange(data: any, checked: boolean) {
  if (checked && !selectedOrgs.value.some(o => o.id === data.id)) {
    selectedOrgs.value.push(data)
  }
  else if (!checked) {
    selectedOrgs.value = selectedOrgs.value.filter(o => o.id !== data.id)
  }
}

function removeOrg(org: any) {
  selectedOrgs.value = selectedOrgs.value.filter(o => o.id !== org.id)
  moduleTree.value?.setChecked(org.id, false)
}

function setFunctionScope(row: any) {
  ElMessage.info(`设置 ${row.module} 的数据范围`)
}

function applyTemplate(template: any) {
  selectedTemplate.value = template
  showApplyDialog.value = true
}

function editTemplate(template: any) {
  newTemplate.value = {
    name: template.name,
    description: template.description,
    selectedPermissions: template.permissions.map((p: any) => p.code),
  }
  showTemplateDialog.value = true
}

function deleteTemplate(template: any) {
  ElMessageBox.confirm(`确定删除模板 "${template.name}" 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    templates.value = templates.value.filter(t => t.id !== template.id)
    ElMessage.success('删除成功')
  })
}

function createTemplate() {
  if (!newTemplate.value.name) {
    ElMessage.warning('请输入模板名称')
    return
  }

  templates.value.push({
    id: `t-${Date.now()}`,
    name: newTemplate.value.name,
    description: newTemplate.value.description,
    createdAt: new Date().toISOString().split('T')[0],
    permissions: permissions.value.filter(p => newTemplate.value.selectedPermissions.includes(p.code)),
  })

  showTemplateDialog.value = false
  newTemplate.value = { name: '', description: '', selectedPermissions: [] }
  ElMessage.success('创建成功')
}

function confirmApply() {
  showApplyDialog.value = false
  ElMessage.success('模板应用成功')
}

// 初始化饼图
function initPieChart() {
  if (pieChart.value) {
    pieChartInstance = echarts.init(pieChart.value)
    const option = {
      animation: false,
      tooltip: {
        trigger: 'item',
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
      },
      series: [
        {
          name: '权限分布',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: [
            { value: 48, name: '菜单权限' },
            { value: 128, name: '按钮权限' },
            { value: 72, name: 'API权限' },
          ],
        },
      ],
    }
    pieChartInstance.setOption(option)
  }
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    initPieChart()
  })
})

window.addEventListener('resize', () => {
  pieChartInstance?.resize()
})
</script>

<template>
  <div class="absolute-container" style="">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              权限设置
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button class="!rounded-button whitespace-nowrap" plain type="primary" size="small">
              <el-icon class="mr-1">
                <ElIconRefresh />
              </el-icon>
              权限刷新
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" plain type="primary" size="small">
              <el-icon class="mr-1">
                <ElIconDownload />
              </el-icon>
              导出
            </el-button>
            <el-select v-model="selectedRole" placeholder="选择角色" size="small" class="w-48">
              <el-option label="系统管理员" value="admin" />
              <el-option label="IT管理员" value="it" />
              <el-option label="财务人员" value="finance" />
              <el-option label="普通员工" value="staff" />
            </el-select>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="never" class="!border">
              <el-tabs v-model="activeTab" type="card">
                <el-tab-pane label="功能权限" name="function">
                  <div class="h-[600px] flex">
                    <!-- 左侧模块树 -->
                    <div class="w-64 overflow-auto border-r pr-4">
                      <ElTree
                        ref="moduleTree" :data="modules" node-key="id" :props="defaultProps"
                        :expand-on-click-node="false" highlight-current class="text-sm" @node-click="handleNodeClick"
                      />
                    </div>
                    <!-- 右侧权限详情 -->
                    <div class="flex-1 overflow-auto pl-4">
                      <div v-if="currentModule" class="mb-4">
                        <h3 class="text-lg font-medium">
                          {{ currentModule.label }}
                        </h3>
                        <p class="text-sm text-gray-500">
                          {{ currentModule.description }}
                        </p>
                      </div>
                      <div v-else class="py-20 text-center text-gray-400">
                        请从左侧选择功能模块
                      </div>

                      <div v-if="currentModule" class="space-y-4">
                        <div class="flex items-center justify-between">
                          <div class="text-sm text-gray-500">
                            共 {{ permissions.length }} 项权限
                          </div>
                          <div class="flex space-x-2">
                            <el-button size="small" @click="selectAll">
                              全选
                            </el-button>
                            <el-button size="small" @click="unselectAll">
                              全不选
                            </el-button>
                            <el-button size="small" @click="reverseSelect">
                              反选
                            </el-button>
                          </div>
                        </div>

                        <ElTable
                          :data="permissions" border style="width: 100%;" height="400"
                          @selection-change="handleSelectionChange"
                        >
                          <el-table-column type="selection" width="50" />
                          <el-table-column prop="code" label="权限编码" width="180" />
                          <el-table-column prop="name" label="权限名称" width="180" />
                          <el-table-column prop="type" label="权限类型">
                            <template #default="{ row }">
                              <ElTag :type="getTagType(row.type)" size="small">
                                {{ row.type }}
                              </ElTag>
                            </template>
                          </el-table-column>
                          <el-table-column prop="description" label="权限描述" />
                        </ElTable>

                        <div class="flex justify-end pt-4 space-x-3">
                          <el-button class="!rounded-button whitespace-nowrap">
                            取消
                          </el-button>
                          <el-button class="!rounded-button whitespace-nowrap" type="primary">
                            保存
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="数据权限" name="data">
                  <div class="space-y-6">
                    <div>
                      <h3 class="mb-3 text-base font-medium">
                        数据范围
                      </h3>
                      <el-radio-group v-model="dataScope">
                        <el-radio-button label="全部数据" />
                        <el-radio-button label="本部门数据" />
                        <el-radio-button label="本部门及下级部门数据" />
                        <el-radio-button label="仅本人数据" />
                        <el-radio-button label="自定义" />
                      </el-radio-group>
                    </div>

                    <div v-if="dataScope === '自定义'" class="space-y-6">
                      <div>
                        <h3 class="mb-3 text-base font-medium">
                          组织数据范围设置
                        </h3>
                        <div class="h-64 flex border rounded">
                          <div class="w-1/2 overflow-auto border-r p-2">
                            <ElTree
                              :data="orgTree" node-key="id" :props="defaultProps" show-checkbox
                              @check-change="handleOrgCheckChange"
                            />
                          </div>
                          <div class="w-1/2 overflow-auto p-2">
                            <div class="mb-2 text-sm text-gray-500">
                              已选部门 ({{ selectedOrgs.length }})
                            </div>
                            <ElTag
                              v-for="org in selectedOrgs" :key="org.id" closable class="m-1"
                              @close="removeOrg(org)"
                            >
                              {{ org.label }}
                            </ElTag>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h3 class="mb-3 text-base font-medium">
                          功能数据范围设置
                        </h3>
                        <ElTable :data="functionScopes" border style="width: 100%;">
                          <el-table-column prop="module" label="功能模块" width="180" />
                          <el-table-column prop="scope" label="数据范围">
                            <template #default="{ row }">
                              <el-select v-model="row.scope" size="small" class="w-full">
                                <el-option label="全部数据" value="all" />
                                <el-option label="本部门数据" value="department" />
                                <el-option label="自定义" value="custom" />
                              </el-select>
                            </template>
                          </el-table-column>
                          <el-table-column label="操作" width="80">
                            <template #default="{ row }">
                              <el-button type="text" size="small" @click="setFunctionScope(row)">
                                设置
                              </el-button>
                            </template>
                          </el-table-column>
                        </ElTable>
                      </div>

                      <div>
                        <h3 class="mb-3 text-base font-medium">
                          字段数据权限设置
                        </h3>
                        <ElTable :data="fieldPermissions" border style="width: 100%;">
                          <el-table-column prop="table" label="数据表" width="180" />
                          <el-table-column prop="field" label="字段名称" width="180" />
                          <el-table-column prop="permission" label="权限类型">
                            <template #default="{ row }">
                              <el-select v-model="row.permission" size="small" class="w-full">
                                <el-option label="可见" value="visible" />
                                <el-option label="可编辑" value="editable" />
                                <el-option label="不可见" value="hidden" />
                              </el-select>
                            </template>
                          </el-table-column>
                        </ElTable>
                      </div>
                    </div>

                    <div class="flex justify-end pt-4 space-x-3">
                      <el-button class="!rounded-button whitespace-nowrap">
                        取消
                      </el-button>
                      <el-button class="!rounded-button whitespace-nowrap" type="primary">
                        保存
                      </el-button>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="权限模板" name="template">
                  <div class="space-y-4">
                    <div class="flex justify-between">
                      <el-button
                        class="!rounded-button whitespace-nowrap" type="primary" size="small"
                        @click="showTemplateDialog = true"
                      >
                        <el-icon class="mr-1">
                          <ElIconPlus />
                        </el-icon>
                        新建模板
                      </el-button>
                      <el-input placeholder="搜索模板..." size="small" style="width: 200px;" clearable>
                        <template #prefix>
                          <el-icon><ElIconSearch /></el-icon>
                        </template>
                      </el-input>
                    </div>

                    <ElTable :data="templates" border style="width: 100%;">
                      <el-table-column prop="name" label="模板名称" width="180" />
                      <el-table-column prop="description" label="模板描述" />
                      <el-table-column prop="createdAt" label="创建时间" width="180" />
                      <el-table-column label="操作" width="180">
                        <template #default="{ row }">
                          <el-button type="text" size="small" @click="applyTemplate(row)">
                            应用
                          </el-button>
                          <el-button type="text" size="small" @click="editTemplate(row)">
                            编辑
                          </el-button>
                          <el-button
                            type="text" size="small" class="!text-red-500"
                            @click="deleteTemplate(row)"
                          >
                            删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </ElTable>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  权限概览
                </div>
              </template>
              <div class="text-sm space-y-2">
                <div class="flex justify-between">
                  <span class="text-gray-500">总权限数</span>
                  <span class="font-medium">248 项</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">已授权</span>
                  <span class="font-medium">156 项</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">菜单权限</span>
                  <span class="font-medium">48 项</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">按钮权限</span>
                  <span class="font-medium">128 项</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">API权限</span>
                  <span class="font-medium">72 项</span>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  权限分布
                </div>
              </template>
              <div ref="pieChart" class="h-48" />
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  最近更改记录
                </div>
              </template>
              <div class="space-y-3">
                <div v-for="(log, index) in changeLogs" :key="index" class="text-sm">
                  <div class="flex justify-between">
                    <span class="font-medium">{{ log.operator }}</span>
                    <span class="text-gray-500">{{ log.time }}</span>
                  </div>
                  <div class="mt-1 text-gray-500">
                    {{ log.action }}
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 新建模板对话框 -->
        <el-dialog v-model="showTemplateDialog" title="新建权限模板" width="600px">
          <el-form :model="newTemplate" label-width="100px">
            <el-form-item label="模板名称" required>
              <el-input v-model="newTemplate.name" placeholder="请输入模板名称" />
            </el-form-item>
            <el-form-item label="模板描述">
              <el-input v-model="newTemplate.description" type="textarea" :rows="3" placeholder="请输入模板描述" />
            </el-form-item>
            <el-form-item label="权限设置">
              <el-transfer
                v-model="newTemplate.selectedPermissions" :data="allPermissions"
                :titles="['所有权限', '已选权限']"
              />
            </el-form-item>
          </el-form>
          <template #footer>
            <el-button @click="showTemplateDialog = false">
              取消
            </el-button>
            <el-button type="primary" @click="createTemplate">
              确定
            </el-button>
          </template>
        </el-dialog>

        <!-- 应用模板对话框 -->
        <el-dialog v-model="showApplyDialog" title="应用权限模板" width="600px">
          <div v-if="selectedTemplate" class="space-y-4">
            <div>
              <h4 class="text-sm text-gray-500 font-medium">
                模板名称
              </h4>
              <p class="mt-1">
                {{ selectedTemplate.name }}
              </p>
            </div>
            <div>
              <h4 class="text-sm text-gray-500 font-medium">
                模板描述
              </h4>
              <p class="mt-1">
                {{ selectedTemplate.description }}
              </p>
            </div>
            <div>
              <h4 class="text-sm text-gray-500 font-medium">
                包含权限
              </h4>
              <ElTable :data="selectedTemplate.permissions" height="200" border>
                <el-table-column prop="name" label="权限名称" />
                <el-table-column prop="type" label="类型" width="100" />
              </ElTable>
            </div>
          </div>
          <template #footer>
            <el-button @click="showApplyDialog = false">
              取消
            </el-button>
            <el-button type="primary" @click="confirmApply">
              确认应用
            </el-button>
          </template>
        </el-dialog>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-menu {
    border-right: none;
  }

  .el-tree {
    background: transparent;
  }

  .el-transfer {
    display: flex;
    justify-content: center;
  }

  :deep(.el-transfer-panel) {
    width: 200px;
  }

  :deep(.el-transfer__buttons) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 10px;
  }

  :deep(.el-transfer__button) {
    margin: 0 0 10px;
  }

  :deep(.el-transfer__button:last-child) {
    margin-bottom: 0;
  }
</style>
