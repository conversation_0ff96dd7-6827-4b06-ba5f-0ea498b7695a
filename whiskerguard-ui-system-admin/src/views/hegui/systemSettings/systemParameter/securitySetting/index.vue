<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'

// 数据
const activeTab = ref('todo')
const viewMode = ref('table')
const filter = ref({
  type: '',
  priority: '',
  dateRange: [],
  keyword: '',
})
const pagination = ref({
  current: 1,
  size: 10,
})
const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts

// 任务列表数据
const taskList = ref([
  {
    id: '********',
    title: '季度财务报告审批',
    type: 'approval',
    creator: '张财务',
    createTime: '2023-05-10 09:30',
    deadline: '2023-05-15 18:00',
    priority: 'urgent',
    status: 'pending',
  },
  {
    id: '********',
    title: '新员工入职培训',
    type: 'training',
    creator: '李HR',
    createTime: '2023-05-08 14:00',
    deadline: '2023-05-20 17:00',
    priority: 'normal',
    status: 'processing',
  },
  {
    id: '********',
    title: '数据安全合规检查',
    type: 'compliance',
    creator: '王安全',
    createTime: '2023-05-05 10:15',
    deadline: '2023-05-12 12:00',
    priority: 'urgent',
    status: 'overdue',
  },
  {
    id: '********',
    title: '项目立项评审',
    type: 'approval',
    creator: '赵经理',
    createTime: '2023-05-09 16:30',
    deadline: '2023-05-16 12:00',
    priority: 'normal',
    status: 'pending',
  },
  {
    id: '********',
    title: '系统权限申请',
    type: 'other',
    creator: '钱技术',
    createTime: '2023-05-11 11:20',
    deadline: '2023-05-18 18:00',
    priority: 'normal',
    status: 'pending',
  },
  {
    id: '********',
    title: '年度预算调整审批',
    type: 'approval',
    creator: '孙财务',
    createTime: '2023-05-07 15:45',
    deadline: '2023-05-14 12:00',
    priority: 'urgent',
    status: 'processing',
  },
  {
    id: '********',
    title: '部门团建方案确认',
    type: 'other',
    creator: '周行政',
    createTime: '2023-05-06 13:10',
    deadline: '2023-05-13 18:00',
    priority: 'normal',
    status: 'overdue',
  },
  {
    id: '********',
    title: '产品需求评审',
    type: 'approval',
    creator: '吴产品',
    createTime: '2023-05-12 10:00',
    deadline: '2023-05-19 14:00',
    priority: 'normal',
    status: 'pending',
  },
])

// 待办提醒数据
const reminders = ref([
  {
    id: 'R001',
    title: '季度财务报告审批',
    remaining: '2天3小时',
    status: 'urgent',
  },
  {
    id: 'R002',
    title: '数据安全合规检查',
    remaining: '已超时',
    status: 'overdue',
  },
  {
    id: 'R003',
    title: '年度预算调整审批',
    remaining: '1天5小时',
    status: 'urgent',
  },
  {
    id: 'R004',
    title: '部门团建方案确认',
    remaining: '已超时',
    status: 'overdue',
  },
])

// 辅助函数
function getTypeName(type: string) {
  const map: Record<string, string> = {
    approval: '审批',
    compliance: '合规检查',
    training: '培训任务',
    other: '其他',
  }
  return map[type] || type
}

function getTypeTag(type: string) {
  const map: Record<string, string> = {
    approval: '',
    compliance: 'warning',
    training: 'success',
    other: 'info',
  }
  return map[type] || ''
}

function getStatusName(status: string) {
  const map: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    overdue: '已超时',
  }
  return map[status] || status
}

function getStatusTag(status: string) {
  const map: Record<string, string> = {
    pending: 'warning',
    processing: '',
    overdue: 'danger',
  }
  return map[status] || ''
}

function isOverdue(deadline: string) {
  const now = new Date()
  const deadlineDate = new Date(deadline)
  return now > deadlineDate
}

// 初始化图表
function initChart() {
  if (!chartRef.value) { return }

  chart = echarts.init(chartRef.value)
  const option = {
    animation: false,
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
    },
    series: [
      {
        name: '任务类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 4, name: '审批' },
          { value: 1, name: '合规检查' },
          { value: 1, name: '培训任务' },
          { value: 2, name: '其他' },
        ],
        color: ['#409EFF', '#E6A23C', '#67C23A', '#909399'],
      },
    ],
  }
  chart.setOption(option)
}

// 响应式调整图表大小
function resizeChart() {
  chart?.resize()
}

onMounted(() => {
  nextTick(() => {
    initChart()
    window.addEventListener('resize', resizeChart)
  })
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              ****
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <!-- <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <i class="el-icon-check mr-1"></i>编辑资料
            </el-button> -->
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <!-- <template #header> -->
              <!-- <div class="f-16 fw-600">个人资料</div> -->
              <!-- </template> -->
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  任务统计
                </div>
              </template>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .min-h-screen {
    min-height: 1024px;
  }

  :deep(.el-tabs__item) {
    padding: 0 20px;
  }

  :deep(.el-tabs__active-bar) {
    height: 3px;
  }

  :deep(.el-table .cell) {
    padding-right: 8px;
    padding-left: 8px;
  }

  :deep(.el-table th.el-table__cell) {
    background-color: #f8f8f9;
  }

  :deep(.el-table tr:hover td.el-table__cell) {
    background-color: #f5f7fa !important;
  }

  :deep(.el-pagination) {
    justify-content: flex-end;
  }
</style>
