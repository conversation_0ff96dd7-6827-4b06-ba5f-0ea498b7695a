<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Check,
  Close,
  Delete,
  Download,
  Edit,
  FolderAdd,
  OfficeBuilding,
  Plus,
  Search,
  Upload,
  User,
  UserFilled,
} from '@element-plus/icons-vue'
import organizationalApi from '@/api/organizational/index'
import ImportComponent from '@/components/import/index.vue'

// 组织架构树
const treeRef = ref()
const treeFilterText = ref('')
const treeProps = {
  children: 'children',
  label: 'name',
}
const treeData = ref([])

// 部门表格
const loading = ref(false)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])

// 批量选择
const multipleSelection = ref([])

// 弹窗相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const formRef = ref()

// 导入相关
const importDialogVisible = ref(false)

// 表单数据
const formData = ref({
  id: null,
  tenantId: 1,
  name: '',
  code: '',
  type: 'DEPARTMENT',
  level: 1,
  status: 1,
  sortOrder: 0,
  description: '',
  metadata: '',
  parentId: null,
  version: 0,
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入组织单位名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  code: [
    { required: true, message: '请输入唯一编码', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
  ],
  type: [
    { required: true, message: '请选择组织单元类型', trigger: 'change' },
  ],
}

// 组织单元类型选项
const typeOptions = [
  { label: '公司', value: 'COMPANY' },
  { label: '子公司', value: 'SUBSIDIARY' },
  { label: '事业群', value: 'BUSINESS_GROUP' },
  { label: '部门', value: 'DEPARTMENT' },
  { label: '团队', value: 'TEAM' },
]

// 根据层级过滤可用的类型选项
const availableTypeOptions = computed(() => {
  // 第一级只能创建公司
  if (formData.value.level === 1) {
    return typeOptions.filter(option => option.value === 'COMPANY')
  }
  // 其他层级可以选择除公司外的其他类型
  return typeOptions.filter(option => option.value !== 'COMPANY')
})

// 监听树搜索
watch(treeFilterText, (val) => {
  treeRef.value?.filter(val)
})

// 方法
function filterNode(value, data) {
  if (!value) { return true }
  return data.name.includes(value) || data.code.includes(value)
}

// 递归查找树形数据中的节点
function findNodeById(nodes, id) {
  if (!nodes || !id) { return null }

  for (const node of nodes) {
    if (node.id === id) {
      return node
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeById(node.children, id)
      if (found) { return found }
    }
  }
  return null
}

// 获取组织架构树形数据
async function fetchTreeData() {
  try {
    loading.value = true
    const response = await organizationalApi.organizationalUnitTreeApi()
    treeData.value = response || []
  }
  catch (error) {
    console.error('获取组织架构失败:', error)
    ElMessage.error('获取组织架构失败')
  }
  finally {
    loading.value = false
  }
}

// 获取组织单元列表（树形结构）
async function fetchData() {
  try {
    loading.value = true
    const response = await organizationalApi.organizationalUnitApi(
      { page: currentPage.value - 1, size: pageSize.value },
      {},
      null,
    )

    // 处理树形结构数据
    if (response.content && Array.isArray(response.content)) {
      tableData.value = response.content
      total.value = response.totalElements
    }
    else {
      tableData.value = []
      total.value = 0
    }
  }
  catch (error) {
    console.error('获取组织单元列表失败:', error)
    ElMessage.error('获取组织单元列表失败')
    tableData.value = []
    total.value = 0
  }
  finally {
    loading.value = false
  }
}

function handleAll() {
  searchFun()
}

// 查询部门
async function handleSearch() {
  try {
    loading.value = true
    const params = {
      page: currentPage.value - 1,
      size: pageSize.value,
    }

    // 如果有搜索关键词，添加到查询参数中
    const queryParams = {}
    if (searchQuery.value.trim()) {
      queryParams.keyword = searchQuery.value.trim()
    }

    const response = await organizationalApi.organizationalUnitApi(
      params,
      queryParams,
      null,
    )

    // 处理查询结果
    if (response.content && Array.isArray(response.content)) {
      tableData.value = response.content
      total.value = response.totalElements
    }
    else {
      tableData.value = []
      total.value = 0
    }

    ElMessage.success('查询完成')
  }
  catch (error) {
    console.error('查询部门失败:', error)
    ElMessage.error('查询部门失败')
    tableData.value = []
    total.value = 0
  }
  finally {
    loading.value = false
  }
}

// 重置搜索
function handleReset() {
  searchQuery.value = ''
  currentPage.value = 1
  fetchData()
}

function handleNodeClick(data) {
  console.log('点击节点:', data)
  // 根据点击的节点过滤表格数据
  if (data.id) {
    // 可以根据选中的节点筛选对应的部门数据
    fetchData()
  }
}

function handleSelectionChange(val) {
  multipleSelection.value = val
}

function cancelSelection() {
  const table = document.querySelector('.el-table')
  if (table) {
    table.clearSelection()
  }
}

function handleSizeChange(val) {
  pageSize.value = val
  fetchData()
}

function handleCurrentChange(val) {
  currentPage.value = val
  fetchData()
}

// 重置表单
function resetForm() {
  formData.value = {
    id: null,
    tenantId: 1,
    name: '',
    code: '',
    type: 'DEPARTMENT',
    level: 1,
    status: 1,
    sortOrder: 0,
    description: '',
    metadata: '',
    parentId: null,
    version: 0,
  }
  formRef.value?.clearValidate()
}

// 新增部门
function handleAdd() {
  resetForm()
  // 第一级新增默认为公司类型
  if (formData.value.level === 1) {
    formData.value.type = 'COMPANY'
    dialogTitle.value = '新增公司'
  }
  else {
    dialogTitle.value = '新增部门'
  }
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑部门
function handleEdit(row: any) {
  resetForm()
  formData.value = {
    tenantId: 1,
    id: row.id,
    name: row.name,
    code: row.code,
    type: row.type,
    level: row.level,
    status: row.status,
    sortOrder: row.sortOrder || 0,
    description: row.description || '',
    metadata: row.metadata || '',
    parentId: row.parentId,
    version: row.version,
  }
  dialogTitle.value = '编辑部门'
  isEdit.value = true
  dialogVisible.value = true
}

// 保存部门
async function saveOrgUnit() {
  if (!formRef.value) { return }

  try {
    await formRef.value.validate()

    // 检查是否与父节点类型相同（除了根节点）
    if (!isEdit.value && formData.value.level > 1 && formData.value.parentId) {
      const parentNode = findNodeById(treeData.value, formData.value.parentId)
      if (parentNode && parentNode.type === formData.value.type) {
        await ElMessageBox.confirm(
          `检测到您选择的类型"${getTypeName(formData.value.type)}"与父节点类型相同，这可能会导致组织架构层次不清晰。是否确认继续创建？`,
          '类型重复提醒',
          {
            confirmButtonText: '确认创建',
            cancelButtonText: '重新选择',
            type: 'warning',
          },
        )
      }
    }

    loading.value = true

    if (isEdit.value) {
      // 更新组织单元
      await organizationalApi.organizationalUnitApi(
        { id: formData.value.id },
        formData.value,
        'update',
      )
      ElMessage.success('更新部门成功')
    }
    else {
      // 创建组织单元
      await organizationalApi.organizationalUnitApi(
        {},
        formData.value,
        'create',
      )
      ElMessage.success('创建部门成功')
    }

    dialogVisible.value = false
    await fetchData()
    await fetchTreeData()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('保存部门失败:', error)
      ElMessage.error('保存部门失败')
    }
  }
  finally {
    loading.value = false
  }
}

function handleView(row) {
  console.log('查看:', row)
}

function handleMember(row) {
  console.log('成员管理:', row)
}

function handleAddChild(row) {
  resetForm()
  formData.value.parentId = row.id
  formData.value.level = row.level + 1
  dialogTitle.value = '添加子部门'
  isEdit.value = false
  dialogVisible.value = true
}

function handleToggleStatus(row) {
  console.log('切换状态:', row)
  row.status = row.status === 1 ? 0 : 1
}

// 删除部门
async function handleDelete(row) {
  try {
    await ElMessageBox.confirm('确定要删除这个部门吗？删除后不可恢复！', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await organizationalApi.organizationalUnitApi(
      null,
      { id: row.id },
      'delete',
    )
    ElMessage.success('删除成功')
    await fetchData()
    await fetchTreeData()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除部门失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

function batchEnable() {
  multipleSelection.value.forEach((item) => {
    item.status = 1
  })
}

function batchDisable() {
  multipleSelection.value.forEach((item) => {
    item.status = 0
  })
}

function batchDelete() {
  console.log('批量删除:', multipleSelection.value)
}

// 获取类型名称
function getTypeName(type: string) {
  const option = typeOptions.find(item => item.value === type)
  return option?.label || type
}

// 获取状态标签
function getStatusTag(status: number) {
  return status === 1 ? 'success' : 'danger'
}

// 获取状态名称
function getStatusName(status: number) {
  return status === 1 ? '启用' : '禁用'
}

// 格式化时间
function formatTime(timeObj: any) {
  if (!timeObj || !timeObj.seconds) { return '-' }
  const date = new Date(timeObj.seconds * 1000)
  return date.toLocaleDateString('zh-CN')
}

// 显示导入弹窗
function showImportDialog() {
  importDialogVisible.value = true
}

// 导入成功回调
function handleImportSuccess(result: any) {
  ElMessage.success('导入部门成功')
  // 刷新数据
  fetchData()
  fetchTreeData()
}

// 导入失败回调
function handleImportError(error: any) {
  console.error('导入部门失败:', error)
}

function searchFun() {
  fetchData()
  fetchTreeData()
}
// 初始化
onMounted(() => {
  searchFun()
})
</script>

<template>
  <div class="absolute-container" style="">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              部门管理
            </h1>
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="'departmentManagement/index/search'" type="primary" class="!rounded-button whitespace-nowrap" @click="handleAll">
              <el-icon class="mr-1">
                <Plus />
              </el-icon>
              查询
            </el-button>
            <el-button v-auth="'departmentManagement/index/add'" type="primary" class="!rounded-button whitespace-nowrap" @click="handleAdd">
              <el-icon class="mr-1">
                <Plus />
              </el-icon>
              新增部门
            </el-button>
            <el-button v-auth="'departmentManagement/index/import'" class="!rounded-button whitespace-nowrap" @click="showImportDialog">
              <el-icon class="mr-1">
                <Upload />
              </el-icon>
              导入部门
            </el-button>
            <!-- <el-button v-auth="'departmentManagement/index/export'" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <Download />
              </el-icon>
              导出
            </el-button> -->
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="4">
            <el-card shadow="hover" class="">
              <el-input v-model="treeFilterText" placeholder="搜索组织架构" class="!rounded-button mb-4" size="small">
                <template #prefix>
                  <el-icon>
                    <Search />
                  </el-icon>
                </template>
              </el-input>
              <el-tree
                ref="treeRef" :data="treeData" :props="treeProps" :filter-node-method="filterNode" node-key="id"
                default-expand-all highlight-current class="custom-tree" @node-click="handleNodeClick"
              >
                <template #default="{ node, data }">
                  <span class="flex items-center">
                    <el-icon v-if="data.type === 'COMPANY'" class="mr-1"><OfficeBuilding /></el-icon>
                    <el-icon v-else class="mr-1"><UserFilled /></el-icon>
                    <span>{{ node.label }}</span>
                  </span>
                </template>
              </el-tree>
            </el-card>
          </el-col>
          <el-col :span="20">
            <el-card shadow="hover" class="">
              <div class="mb-4 flex items-center space-x-3">
                <el-input v-model="searchQuery" placeholder="搜索部门名称、编码..." class="!rounded-button w-60" size="small" @keyup.enter="handleSearch">
                  <template #prefix>
                    <el-icon>
                      <Search />
                    </el-icon>
                  </template>
                </el-input>
                <!-- <el-button type="primary" size="small" class="!rounded-button" @click="handleSearch">
                  <el-icon class="mr-1">
                    <Search />
                  </el-icon>
                  查询
                </el-button> -->
                <el-button v-auth="'departmentManagement/index/reset'" size="small" class="!rounded-button" @click="handleReset">
                  重置
                </el-button>
              </div>

              <el-table
                v-loading="loading"
                :data="tableData"
                style="width: 100%;"
                row-key="id"
                :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="50" />
                <el-table-column prop="code" label="部门编码" width="120" />
                <el-table-column prop="name" label="部门名称" width="180" />
                <el-table-column prop="type" label="部门类型" width="120">
                  <template #default="{ row }">
                    <el-tag :type="row.type === 'DEPARTMENT' ? 'primary' : 'info'" size="small">
                      {{ getTypeName(row.type) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="level" label="层级" width="80" align="center" />
                <el-table-column prop="sortOrder" label="排序" width="80" align="center" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getStatusTag(row.status)" size="small">
                      {{ getStatusName(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="createdAt" label="创建时间" width="120">
                  <template #default="{ row }">
                    {{ formatTime(row.createdAt) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="240" fixed="right">
                  <template #default="{ row }">
                    <el-button v-auth="'departmentManagement/index/edit'" size="small" @click="handleEdit(row)">
                      编辑
                    </el-button>
                    <el-button v-auth="'departmentManagement/index/addChild'" size="small" @click="handleAddChild(row)">
                      新增
                    </el-button>
                    <el-button v-auth="'departmentManagement/index/delete'" size="small" @click="handleDelete(row)">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分页 -->
              <div class="mt-4 flex items-center justify-between px-2">
                <div>
                  <el-button v-if="multipleSelection.length > 0" v-auth="'departmentManagement/index/cancelSelection'" size="small" @click="cancelSelection">
                    取消选择
                  </el-button>
                  <span v-if="multipleSelection.length > 0" class="ml-2 text-sm text-gray-500">
                    已选择 {{ multipleSelection.length }} 项
                  </span>
                </div>
                <el-pagination
                  v-model:current-page="currentPage" v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]" :small="true" layout="total, sizes, prev, pager, next, jumper"
                  :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>

    <!-- 新增/编辑部门弹窗 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" :close-on-click-modal="false">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="组织单位名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入组织单位名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="唯一编码" prop="code">
              <el-input v-model="formData.code" placeholder="请输入唯一编码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="组织单元类型" prop="type">
              <el-select v-model="formData.type" placeholder="请选择组织单元类型" style="width: 100%">
                <el-option
                  v-for="option in availableTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="formData.status">
                <el-radio :label="1">
                  启用
                </el-radio>
                <el-radio :label="0">
                  禁用
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="层级深度">
              <el-input-number v-model="formData.level" :min="1" :max="10" style="width: 100%" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序序号">
              <el-input-number v-model="formData.sortOrder" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述信息">
          <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入描述信息" />
        </el-form-item>

        <el-form-item label="扩展元数据">
          <el-input v-model="formData.metadata" type="textarea" :rows="2" placeholder="请输入扩展元数据（JSON格式）" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" :loading="loading" @click="saveOrgUnit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入弹窗 -->
    <ImportComponent
      v-model:visible="importDialogVisible"
      title="导入部门"
      :download-template-api="organizationalApi.downloadDepartmentTemplate"
      :import-data-api="organizationalApi.importDepartments"
      template-file-name="部门信息模板.xlsx"
      accept-file-types=".xlsx,.xls"
      :max-file-size="10"
      :show-download-template="true"
      @success="handleImportSuccess"
      @error="handleImportError"
    />
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .custom-tree {
    :deep(.el-tree-node__content) {
      height: 36px;
    }

    :deep(.el-tree-node.is-current > .el-tree-node__content) {
      background-color: #f0f7ff;
    }

    :deep(.el-tree-node__content:hover) {
      background-color: #f5f7fa;
    }
  }

  .el-menu {
    border-right: none;
  }

  .el-menu-item.is-active {
    background-color: var(--el-color-primary-light-9) !important;
  }

  .el-menu-item:hover {
    background-color: var(--el-color-primary-light-8) !important;
  }

  .el-sub-menu__title:hover {
    background-color: var(--el-color-primary-light-8) !important;
  }
</style>
