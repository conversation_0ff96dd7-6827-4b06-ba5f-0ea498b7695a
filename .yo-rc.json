{"generator-jhipster": {"applicationType": "microservice", "authenticationType": "jwt", "baseName": "Test1Application", "buildTool": "maven", "cacheProvider": null, "clientFramework": "no", "clientTestFrameworks": null, "clientTheme": null, "creationTimestamp": 1744349343004, "databaseType": "no", "devDatabaseType": null, "enableHibernateCache": null, "enableTranslation": true, "entities": [], "feignClient": null, "jhipsterVersion": "8.10.0", "jwtSecretKey": "NzI0MjZmYTgyMjhlOGJmNTc0ZWFiOThlZmM4MjgxYjZmNmJiYmEzZTVhOWFjNGMzZGVkNzM2MDU2Y2FlMTAyZjU0ZjFhY2ViZWE4MjU1MTU5MjlmZDYyNTk1OWRlZTAwYWIyYTc0MWRlOTM3ZTZkYzg2ZTVlMzU1NWZmMDY2ZWE=", "languages": ["en"], "microfrontend": null, "microfrontends": [], "nativeLanguage": "en", "packageName": "com.mycompany.myapp", "prodDatabaseType": null, "reactive": true, "serverPort": 8081, "serviceDiscoveryType": "consul", "skipClient": true, "skipUserManagement": true, "syncUserWithIdp": null, "testFrameworks": [], "withAdminUi": null}}