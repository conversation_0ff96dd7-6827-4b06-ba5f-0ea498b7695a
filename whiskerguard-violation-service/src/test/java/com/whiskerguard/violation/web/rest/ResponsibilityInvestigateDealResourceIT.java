package com.whiskerguard.violation.web.rest;

import static com.whiskerguard.violation.domain.ResponsibilityInvestigateDealAsserts.*;
import static com.whiskerguard.violation.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.violation.IntegrationTest;
import com.whiskerguard.violation.domain.ResponsibilityInvestigateDeal;
import com.whiskerguard.violation.domain.enumeration.DealType;
import com.whiskerguard.violation.domain.enumeration.PriorityLevel;
import com.whiskerguard.violation.domain.enumeration.Status;
import com.whiskerguard.violation.repository.ResponsibilityInvestigateDealRepository;
import com.whiskerguard.violation.service.dto.ResponsibilityInvestigateDealDTO;
import com.whiskerguard.violation.service.mapper.ResponsibilityInvestigateDealMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ResponsibilityInvestigateDealResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ResponsibilityInvestigateDealResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_TITLE = "AAAAAAAAAA";
    private static final String UPDATED_TITLE = "BBBBBBBBBB";

    private static final String DEFAULT_DEAL_CODE = "AAAAAAAAAA";
    private static final String UPDATED_DEAL_CODE = "BBBBBBBBBB";

    private static final DealType DEFAULT_DEAL_TYPE = DealType.FINANCIAL_VIOLATION;
    private static final DealType UPDATED_DEAL_TYPE = DealType.INFORMATION_SECURITY;

    private static final PriorityLevel DEFAULT_LEVEL = PriorityLevel.LOW;
    private static final PriorityLevel UPDATED_LEVEL = PriorityLevel.MIDDLE;

    private static final Long DEFAULT_INVESTIGATE_ID = 1L;
    private static final Long UPDATED_INVESTIGATE_ID = 2L;

    private static final Long DEFAULT_DUTY_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_DUTY_EMPLOYEE_ID = 2L;

    private static final Long DEFAULT_DUTY_EMPLOYEE_ORG_ID = 1L;
    private static final Long UPDATED_DUTY_EMPLOYEE_ORG_ID = 2L;

    private static final Long DEFAULT_DEAL_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_DEAL_EMPLOYEE_ID = 2L;

    private static final LocalDate DEFAULT_START_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_START_DATE = LocalDate.now(ZoneId.systemDefault());

    private static final LocalDate DEFAULT_FINISH_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_FINISH_DATE = LocalDate.now(ZoneId.systemDefault());

    private static final Status DEFAULT_STATUS = Status.NO_START;
    private static final Status UPDATED_STATUS = Status.PROGRESSING;

    private static final String DEFAULT_VIOLATION_DESC = "AAAAAAAAAA";
    private static final String UPDATED_VIOLATION_DESC = "BBBBBBBBBB";

    private static final String DEFAULT_VIOLATION_IMPACT = "AAAAAAAAAA";
    private static final String UPDATED_VIOLATION_IMPACT = "BBBBBBBBBB";

    private static final String DEFAULT_RESPONSIBILITY = "AAAAAAAAAA";
    private static final String UPDATED_RESPONSIBILITY = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/responsibility-investigate-deals";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ResponsibilityInvestigateDealRepository responsibilityInvestigateDealRepository;

    @Autowired
    private ResponsibilityInvestigateDealMapper responsibilityInvestigateDealMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restResponsibilityInvestigateDealMockMvc;

    private ResponsibilityInvestigateDeal responsibilityInvestigateDeal;

    private ResponsibilityInvestigateDeal insertedResponsibilityInvestigateDeal;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ResponsibilityInvestigateDeal createEntity() {
        return new ResponsibilityInvestigateDeal()
            .tenantId(DEFAULT_TENANT_ID)
            .title(DEFAULT_TITLE)
            .dealCode(DEFAULT_DEAL_CODE)
            .dealType(DEFAULT_DEAL_TYPE)
            .level(DEFAULT_LEVEL)
            .investigateId(DEFAULT_INVESTIGATE_ID)
            .dutyEmployeeId(DEFAULT_DUTY_EMPLOYEE_ID)
            .dutyEmployeeOrgId(DEFAULT_DUTY_EMPLOYEE_ORG_ID)
            .dealEmployeeId(DEFAULT_DEAL_EMPLOYEE_ID)
            .startDate(DEFAULT_START_DATE)
            .finishDate(DEFAULT_FINISH_DATE)
            .status(DEFAULT_STATUS)
            .violationDesc(DEFAULT_VIOLATION_DESC)
            .violationImpact(DEFAULT_VIOLATION_IMPACT)
            .responsibility(DEFAULT_RESPONSIBILITY)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ResponsibilityInvestigateDeal createUpdatedEntity() {
        return new ResponsibilityInvestigateDeal()
            .tenantId(UPDATED_TENANT_ID)
            .title(UPDATED_TITLE)
            .dealCode(UPDATED_DEAL_CODE)
            .dealType(UPDATED_DEAL_TYPE)
            .level(UPDATED_LEVEL)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .dutyEmployeeId(UPDATED_DUTY_EMPLOYEE_ID)
            .dutyEmployeeOrgId(UPDATED_DUTY_EMPLOYEE_ORG_ID)
            .dealEmployeeId(UPDATED_DEAL_EMPLOYEE_ID)
            .startDate(UPDATED_START_DATE)
            .finishDate(UPDATED_FINISH_DATE)
            .status(UPDATED_STATUS)
            .violationDesc(UPDATED_VIOLATION_DESC)
            .violationImpact(UPDATED_VIOLATION_IMPACT)
            .responsibility(UPDATED_RESPONSIBILITY)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        responsibilityInvestigateDeal = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedResponsibilityInvestigateDeal != null) {
            responsibilityInvestigateDealRepository.delete(insertedResponsibilityInvestigateDeal);
            insertedResponsibilityInvestigateDeal = null;
        }
    }

    @Test
    @Transactional
    void createResponsibilityInvestigateDeal() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ResponsibilityInvestigateDeal
        ResponsibilityInvestigateDealDTO responsibilityInvestigateDealDTO = responsibilityInvestigateDealMapper.toDto(
            responsibilityInvestigateDeal
        );
        var returnedResponsibilityInvestigateDealDTO = om.readValue(
            restResponsibilityInvestigateDealMockMvc
                .perform(
                    post(ENTITY_API_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(om.writeValueAsBytes(responsibilityInvestigateDealDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ResponsibilityInvestigateDealDTO.class
        );

        // Validate the ResponsibilityInvestigateDeal in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedResponsibilityInvestigateDeal = responsibilityInvestigateDealMapper.toEntity(returnedResponsibilityInvestigateDealDTO);
        assertResponsibilityInvestigateDealUpdatableFieldsEquals(
            returnedResponsibilityInvestigateDeal,
            getPersistedResponsibilityInvestigateDeal(returnedResponsibilityInvestigateDeal)
        );

        insertedResponsibilityInvestigateDeal = returnedResponsibilityInvestigateDeal;
    }

    @Test
    @Transactional
    void createResponsibilityInvestigateDealWithExistingId() throws Exception {
        // Create the ResponsibilityInvestigateDeal with an existing ID
        responsibilityInvestigateDeal.setId(1L);
        ResponsibilityInvestigateDealDTO responsibilityInvestigateDealDTO = responsibilityInvestigateDealMapper.toDto(
            responsibilityInvestigateDeal
        );

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restResponsibilityInvestigateDealMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(responsibilityInvestigateDealDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ResponsibilityInvestigateDeal in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateDeal.setTenantId(null);

        // Create the ResponsibilityInvestigateDeal, which fails.
        ResponsibilityInvestigateDealDTO responsibilityInvestigateDealDTO = responsibilityInvestigateDealMapper.toDto(
            responsibilityInvestigateDeal
        );

        restResponsibilityInvestigateDealMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(responsibilityInvestigateDealDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkDealCodeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateDeal.setDealCode(null);

        // Create the ResponsibilityInvestigateDeal, which fails.
        ResponsibilityInvestigateDealDTO responsibilityInvestigateDealDTO = responsibilityInvestigateDealMapper.toDto(
            responsibilityInvestigateDeal
        );

        restResponsibilityInvestigateDealMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(responsibilityInvestigateDealDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkInvestigateIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateDeal.setInvestigateId(null);

        // Create the ResponsibilityInvestigateDeal, which fails.
        ResponsibilityInvestigateDealDTO responsibilityInvestigateDealDTO = responsibilityInvestigateDealMapper.toDto(
            responsibilityInvestigateDeal
        );

        restResponsibilityInvestigateDealMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(responsibilityInvestigateDealDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkDutyEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateDeal.setDutyEmployeeId(null);

        // Create the ResponsibilityInvestigateDeal, which fails.
        ResponsibilityInvestigateDealDTO responsibilityInvestigateDealDTO = responsibilityInvestigateDealMapper.toDto(
            responsibilityInvestigateDeal
        );

        restResponsibilityInvestigateDealMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(responsibilityInvestigateDealDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkDutyEmployeeOrgIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateDeal.setDutyEmployeeOrgId(null);

        // Create the ResponsibilityInvestigateDeal, which fails.
        ResponsibilityInvestigateDealDTO responsibilityInvestigateDealDTO = responsibilityInvestigateDealMapper.toDto(
            responsibilityInvestigateDeal
        );

        restResponsibilityInvestigateDealMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(responsibilityInvestigateDealDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkDealEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateDeal.setDealEmployeeId(null);

        // Create the ResponsibilityInvestigateDeal, which fails.
        ResponsibilityInvestigateDealDTO responsibilityInvestigateDealDTO = responsibilityInvestigateDealMapper.toDto(
            responsibilityInvestigateDeal
        );

        restResponsibilityInvestigateDealMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(responsibilityInvestigateDealDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateDeal.setCreatedBy(null);

        // Create the ResponsibilityInvestigateDeal, which fails.
        ResponsibilityInvestigateDealDTO responsibilityInvestigateDealDTO = responsibilityInvestigateDealMapper.toDto(
            responsibilityInvestigateDeal
        );

        restResponsibilityInvestigateDealMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(responsibilityInvestigateDealDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllResponsibilityInvestigateDeals() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateDeal = responsibilityInvestigateDealRepository.saveAndFlush(responsibilityInvestigateDeal);

        // Get all the responsibilityInvestigateDealList
        restResponsibilityInvestigateDealMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(responsibilityInvestigateDeal.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].title").value(hasItem(DEFAULT_TITLE)))
            .andExpect(jsonPath("$.[*].dealCode").value(hasItem(DEFAULT_DEAL_CODE)))
            .andExpect(jsonPath("$.[*].dealType").value(hasItem(DEFAULT_DEAL_TYPE.toString())))
            .andExpect(jsonPath("$.[*].level").value(hasItem(DEFAULT_LEVEL.toString())))
            .andExpect(jsonPath("$.[*].investigateId").value(hasItem(DEFAULT_INVESTIGATE_ID.intValue())))
            .andExpect(jsonPath("$.[*].dutyEmployeeId").value(hasItem(DEFAULT_DUTY_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].dutyEmployeeOrgId").value(hasItem(DEFAULT_DUTY_EMPLOYEE_ORG_ID.intValue())))
            .andExpect(jsonPath("$.[*].dealEmployeeId").value(hasItem(DEFAULT_DEAL_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].startDate").value(hasItem(DEFAULT_START_DATE.toString())))
            .andExpect(jsonPath("$.[*].finishDate").value(hasItem(DEFAULT_FINISH_DATE.toString())))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].violationDesc").value(hasItem(DEFAULT_VIOLATION_DESC)))
            .andExpect(jsonPath("$.[*].violationImpact").value(hasItem(DEFAULT_VIOLATION_IMPACT)))
            .andExpect(jsonPath("$.[*].responsibility").value(hasItem(DEFAULT_RESPONSIBILITY)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getResponsibilityInvestigateDeal() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateDeal = responsibilityInvestigateDealRepository.saveAndFlush(responsibilityInvestigateDeal);

        // Get the responsibilityInvestigateDeal
        restResponsibilityInvestigateDealMockMvc
            .perform(get(ENTITY_API_URL_ID, responsibilityInvestigateDeal.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(responsibilityInvestigateDeal.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.title").value(DEFAULT_TITLE))
            .andExpect(jsonPath("$.dealCode").value(DEFAULT_DEAL_CODE))
            .andExpect(jsonPath("$.dealType").value(DEFAULT_DEAL_TYPE.toString()))
            .andExpect(jsonPath("$.level").value(DEFAULT_LEVEL.toString()))
            .andExpect(jsonPath("$.investigateId").value(DEFAULT_INVESTIGATE_ID.intValue()))
            .andExpect(jsonPath("$.dutyEmployeeId").value(DEFAULT_DUTY_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.dutyEmployeeOrgId").value(DEFAULT_DUTY_EMPLOYEE_ORG_ID.intValue()))
            .andExpect(jsonPath("$.dealEmployeeId").value(DEFAULT_DEAL_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.startDate").value(DEFAULT_START_DATE.toString()))
            .andExpect(jsonPath("$.finishDate").value(DEFAULT_FINISH_DATE.toString()))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.violationDesc").value(DEFAULT_VIOLATION_DESC))
            .andExpect(jsonPath("$.violationImpact").value(DEFAULT_VIOLATION_IMPACT))
            .andExpect(jsonPath("$.responsibility").value(DEFAULT_RESPONSIBILITY))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingResponsibilityInvestigateDeal() throws Exception {
        // Get the responsibilityInvestigateDeal
        restResponsibilityInvestigateDealMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingResponsibilityInvestigateDeal() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateDeal = responsibilityInvestigateDealRepository.saveAndFlush(responsibilityInvestigateDeal);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the responsibilityInvestigateDeal
        ResponsibilityInvestigateDeal updatedResponsibilityInvestigateDeal = responsibilityInvestigateDealRepository
            .findById(responsibilityInvestigateDeal.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedResponsibilityInvestigateDeal are not directly saved in db
        em.detach(updatedResponsibilityInvestigateDeal);
        updatedResponsibilityInvestigateDeal
            .tenantId(UPDATED_TENANT_ID)
            .title(UPDATED_TITLE)
            .dealCode(UPDATED_DEAL_CODE)
            .dealType(UPDATED_DEAL_TYPE)
            .level(UPDATED_LEVEL)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .dutyEmployeeId(UPDATED_DUTY_EMPLOYEE_ID)
            .dutyEmployeeOrgId(UPDATED_DUTY_EMPLOYEE_ORG_ID)
            .dealEmployeeId(UPDATED_DEAL_EMPLOYEE_ID)
            .startDate(UPDATED_START_DATE)
            .finishDate(UPDATED_FINISH_DATE)
            .status(UPDATED_STATUS)
            .violationDesc(UPDATED_VIOLATION_DESC)
            .violationImpact(UPDATED_VIOLATION_IMPACT)
            .responsibility(UPDATED_RESPONSIBILITY)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ResponsibilityInvestigateDealDTO responsibilityInvestigateDealDTO = responsibilityInvestigateDealMapper.toDto(
            updatedResponsibilityInvestigateDeal
        );

        restResponsibilityInvestigateDealMockMvc
            .perform(
                put(ENTITY_API_URL_ID, responsibilityInvestigateDealDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateDealDTO))
            )
            .andExpect(status().isOk());

        // Validate the ResponsibilityInvestigateDeal in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedResponsibilityInvestigateDealToMatchAllProperties(updatedResponsibilityInvestigateDeal);
    }

    @Test
    @Transactional
    void putNonExistingResponsibilityInvestigateDeal() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateDeal.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateDeal
        ResponsibilityInvestigateDealDTO responsibilityInvestigateDealDTO = responsibilityInvestigateDealMapper.toDto(
            responsibilityInvestigateDeal
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateDealMockMvc
            .perform(
                put(ENTITY_API_URL_ID, responsibilityInvestigateDealDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateDealDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ResponsibilityInvestigateDeal in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchResponsibilityInvestigateDeal() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateDeal.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateDeal
        ResponsibilityInvestigateDealDTO responsibilityInvestigateDealDTO = responsibilityInvestigateDealMapper.toDto(
            responsibilityInvestigateDeal
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateDealMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateDealDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ResponsibilityInvestigateDeal in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamResponsibilityInvestigateDeal() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateDeal.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateDeal
        ResponsibilityInvestigateDealDTO responsibilityInvestigateDealDTO = responsibilityInvestigateDealMapper.toDto(
            responsibilityInvestigateDeal
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateDealMockMvc
            .perform(
                put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(responsibilityInvestigateDealDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ResponsibilityInvestigateDeal in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateResponsibilityInvestigateDealWithPatch() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateDeal = responsibilityInvestigateDealRepository.saveAndFlush(responsibilityInvestigateDeal);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the responsibilityInvestigateDeal using partial update
        ResponsibilityInvestigateDeal partialUpdatedResponsibilityInvestigateDeal = new ResponsibilityInvestigateDeal();
        partialUpdatedResponsibilityInvestigateDeal.setId(responsibilityInvestigateDeal.getId());

        partialUpdatedResponsibilityInvestigateDeal
            .title(UPDATED_TITLE)
            .dealType(UPDATED_DEAL_TYPE)
            .level(UPDATED_LEVEL)
            .dutyEmployeeId(UPDATED_DUTY_EMPLOYEE_ID)
            .dealEmployeeId(UPDATED_DEAL_EMPLOYEE_ID)
            .violationImpact(UPDATED_VIOLATION_IMPACT)
            .metadata(UPDATED_METADATA)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .isDeleted(UPDATED_IS_DELETED);

        restResponsibilityInvestigateDealMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedResponsibilityInvestigateDeal.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedResponsibilityInvestigateDeal))
            )
            .andExpect(status().isOk());

        // Validate the ResponsibilityInvestigateDeal in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertResponsibilityInvestigateDealUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedResponsibilityInvestigateDeal, responsibilityInvestigateDeal),
            getPersistedResponsibilityInvestigateDeal(responsibilityInvestigateDeal)
        );
    }

    @Test
    @Transactional
    void fullUpdateResponsibilityInvestigateDealWithPatch() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateDeal = responsibilityInvestigateDealRepository.saveAndFlush(responsibilityInvestigateDeal);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the responsibilityInvestigateDeal using partial update
        ResponsibilityInvestigateDeal partialUpdatedResponsibilityInvestigateDeal = new ResponsibilityInvestigateDeal();
        partialUpdatedResponsibilityInvestigateDeal.setId(responsibilityInvestigateDeal.getId());

        partialUpdatedResponsibilityInvestigateDeal
            .tenantId(UPDATED_TENANT_ID)
            .title(UPDATED_TITLE)
            .dealCode(UPDATED_DEAL_CODE)
            .dealType(UPDATED_DEAL_TYPE)
            .level(UPDATED_LEVEL)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .dutyEmployeeId(UPDATED_DUTY_EMPLOYEE_ID)
            .dutyEmployeeOrgId(UPDATED_DUTY_EMPLOYEE_ORG_ID)
            .dealEmployeeId(UPDATED_DEAL_EMPLOYEE_ID)
            .startDate(UPDATED_START_DATE)
            .finishDate(UPDATED_FINISH_DATE)
            .status(UPDATED_STATUS)
            .violationDesc(UPDATED_VIOLATION_DESC)
            .violationImpact(UPDATED_VIOLATION_IMPACT)
            .responsibility(UPDATED_RESPONSIBILITY)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restResponsibilityInvestigateDealMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedResponsibilityInvestigateDeal.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedResponsibilityInvestigateDeal))
            )
            .andExpect(status().isOk());

        // Validate the ResponsibilityInvestigateDeal in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertResponsibilityInvestigateDealUpdatableFieldsEquals(
            partialUpdatedResponsibilityInvestigateDeal,
            getPersistedResponsibilityInvestigateDeal(partialUpdatedResponsibilityInvestigateDeal)
        );
    }

    @Test
    @Transactional
    void patchNonExistingResponsibilityInvestigateDeal() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateDeal.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateDeal
        ResponsibilityInvestigateDealDTO responsibilityInvestigateDealDTO = responsibilityInvestigateDealMapper.toDto(
            responsibilityInvestigateDeal
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateDealMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, responsibilityInvestigateDealDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(responsibilityInvestigateDealDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ResponsibilityInvestigateDeal in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchResponsibilityInvestigateDeal() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateDeal.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateDeal
        ResponsibilityInvestigateDealDTO responsibilityInvestigateDealDTO = responsibilityInvestigateDealMapper.toDto(
            responsibilityInvestigateDeal
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateDealMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(responsibilityInvestigateDealDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ResponsibilityInvestigateDeal in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamResponsibilityInvestigateDeal() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateDeal.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateDeal
        ResponsibilityInvestigateDealDTO responsibilityInvestigateDealDTO = responsibilityInvestigateDealMapper.toDto(
            responsibilityInvestigateDeal
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateDealMockMvc
            .perform(
                patch(ENTITY_API_URL)
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(responsibilityInvestigateDealDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ResponsibilityInvestigateDeal in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteResponsibilityInvestigateDeal() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateDeal = responsibilityInvestigateDealRepository.saveAndFlush(responsibilityInvestigateDeal);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the responsibilityInvestigateDeal
        restResponsibilityInvestigateDealMockMvc
            .perform(delete(ENTITY_API_URL_ID, responsibilityInvestigateDeal.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return responsibilityInvestigateDealRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ResponsibilityInvestigateDeal getPersistedResponsibilityInvestigateDeal(
        ResponsibilityInvestigateDeal responsibilityInvestigateDeal
    ) {
        return responsibilityInvestigateDealRepository.findById(responsibilityInvestigateDeal.getId()).orElseThrow();
    }

    protected void assertPersistedResponsibilityInvestigateDealToMatchAllProperties(
        ResponsibilityInvestigateDeal expectedResponsibilityInvestigateDeal
    ) {
        assertResponsibilityInvestigateDealAllPropertiesEquals(
            expectedResponsibilityInvestigateDeal,
            getPersistedResponsibilityInvestigateDeal(expectedResponsibilityInvestigateDeal)
        );
    }

    protected void assertPersistedResponsibilityInvestigateDealToMatchUpdatableProperties(
        ResponsibilityInvestigateDeal expectedResponsibilityInvestigateDeal
    ) {
        assertResponsibilityInvestigateDealAllUpdatablePropertiesEquals(
            expectedResponsibilityInvestigateDeal,
            getPersistedResponsibilityInvestigateDeal(expectedResponsibilityInvestigateDeal)
        );
    }
}
