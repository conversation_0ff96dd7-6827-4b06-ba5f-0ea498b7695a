package com.whiskerguard.violation.web.rest;

import static com.whiskerguard.violation.domain.ProblemInvestigateRecordAsserts.*;
import static com.whiskerguard.violation.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.violation.IntegrationTest;
import com.whiskerguard.violation.domain.ProblemInvestigateRecord;
import com.whiskerguard.violation.domain.enumeration.RecordType;
import com.whiskerguard.violation.repository.ProblemInvestigateRecordRepository;
import com.whiskerguard.violation.service.dto.ProblemInvestigateRecordDTO;
import com.whiskerguard.violation.service.mapper.ProblemInvestigateRecordMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ProblemInvestigateRecordResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ProblemInvestigateRecordResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_INVESTIGATE_ID = 1L;
    private static final Long UPDATED_INVESTIGATE_ID = 2L;

    private static final String DEFAULT_RECORD_CODE = "AAAAAAAAAA";
    private static final String UPDATED_RECORD_CODE = "BBBBBBBBBB";

    private static final String DEFAULT_LOCATION = "AAAAAAAAAA";
    private static final String UPDATED_LOCATION = "BBBBBBBBBB";

    private static final RecordType DEFAULT_RECORD_TYPE = RecordType.PERSONNEL_INTERVIEW;
    private static final RecordType UPDATED_RECORD_TYPE = RecordType.PERSONNEL_INTERVIEW;

    private static final String DEFAULT_CONTENT = "AAAAAAAAAA";
    private static final String UPDATED_CONTENT = "BBBBBBBBBB";

    private static final String DEFAULT_DISCOVER = "AAAAAAAAAA";
    private static final String UPDATED_DISCOVER = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/problem-investigate-records";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ProblemInvestigateRecordRepository problemInvestigateRecordRepository;

    @Autowired
    private ProblemInvestigateRecordMapper problemInvestigateRecordMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restProblemInvestigateRecordMockMvc;

    private ProblemInvestigateRecord problemInvestigateRecord;

    private ProblemInvestigateRecord insertedProblemInvestigateRecord;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ProblemInvestigateRecord createEntity() {
        return new ProblemInvestigateRecord()
            .tenantId(DEFAULT_TENANT_ID)
            .investigateId(DEFAULT_INVESTIGATE_ID)
            .recordCode(DEFAULT_RECORD_CODE)
            .location(DEFAULT_LOCATION)
            .recordType(DEFAULT_RECORD_TYPE)
            .content(DEFAULT_CONTENT)
            .discover(DEFAULT_DISCOVER)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ProblemInvestigateRecord createUpdatedEntity() {
        return new ProblemInvestigateRecord()
            .tenantId(UPDATED_TENANT_ID)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .recordCode(UPDATED_RECORD_CODE)
            .location(UPDATED_LOCATION)
            .recordType(UPDATED_RECORD_TYPE)
            .content(UPDATED_CONTENT)
            .discover(UPDATED_DISCOVER)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        problemInvestigateRecord = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedProblemInvestigateRecord != null) {
            problemInvestigateRecordRepository.delete(insertedProblemInvestigateRecord);
            insertedProblemInvestigateRecord = null;
        }
    }

    @Test
    @Transactional
    void createProblemInvestigateRecord() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ProblemInvestigateRecord
        ProblemInvestigateRecordDTO problemInvestigateRecordDTO = problemInvestigateRecordMapper.toDto(problemInvestigateRecord);
        var returnedProblemInvestigateRecordDTO = om.readValue(
            restProblemInvestigateRecordMockMvc
                .perform(
                    post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateRecordDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ProblemInvestigateRecordDTO.class
        );

        // Validate the ProblemInvestigateRecord in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedProblemInvestigateRecord = problemInvestigateRecordMapper.toEntity(returnedProblemInvestigateRecordDTO);
        assertProblemInvestigateRecordUpdatableFieldsEquals(
            returnedProblemInvestigateRecord,
            getPersistedProblemInvestigateRecord(returnedProblemInvestigateRecord)
        );

        insertedProblemInvestigateRecord = returnedProblemInvestigateRecord;
    }

    @Test
    @Transactional
    void createProblemInvestigateRecordWithExistingId() throws Exception {
        // Create the ProblemInvestigateRecord with an existing ID
        problemInvestigateRecord.setId(1L);
        ProblemInvestigateRecordDTO problemInvestigateRecordDTO = problemInvestigateRecordMapper.toDto(problemInvestigateRecord);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restProblemInvestigateRecordMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateRecordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateRecord.setTenantId(null);

        // Create the ProblemInvestigateRecord, which fails.
        ProblemInvestigateRecordDTO problemInvestigateRecordDTO = problemInvestigateRecordMapper.toDto(problemInvestigateRecord);

        restProblemInvestigateRecordMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateRecordDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkInvestigateIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateRecord.setInvestigateId(null);

        // Create the ProblemInvestigateRecord, which fails.
        ProblemInvestigateRecordDTO problemInvestigateRecordDTO = problemInvestigateRecordMapper.toDto(problemInvestigateRecord);

        restProblemInvestigateRecordMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateRecordDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkRecordCodeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateRecord.setRecordCode(null);

        // Create the ProblemInvestigateRecord, which fails.
        ProblemInvestigateRecordDTO problemInvestigateRecordDTO = problemInvestigateRecordMapper.toDto(problemInvestigateRecord);

        restProblemInvestigateRecordMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateRecordDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkLocationIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateRecord.setLocation(null);

        // Create the ProblemInvestigateRecord, which fails.
        ProblemInvestigateRecordDTO problemInvestigateRecordDTO = problemInvestigateRecordMapper.toDto(problemInvestigateRecord);

        restProblemInvestigateRecordMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateRecordDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateRecord.setCreatedBy(null);

        // Create the ProblemInvestigateRecord, which fails.
        ProblemInvestigateRecordDTO problemInvestigateRecordDTO = problemInvestigateRecordMapper.toDto(problemInvestigateRecord);

        restProblemInvestigateRecordMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateRecordDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllProblemInvestigateRecords() throws Exception {
        // Initialize the database
        insertedProblemInvestigateRecord = problemInvestigateRecordRepository.saveAndFlush(problemInvestigateRecord);

        // Get all the problemInvestigateRecordList
        restProblemInvestigateRecordMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(problemInvestigateRecord.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].investigateId").value(hasItem(DEFAULT_INVESTIGATE_ID.intValue())))
            .andExpect(jsonPath("$.[*].recordCode").value(hasItem(DEFAULT_RECORD_CODE)))
            .andExpect(jsonPath("$.[*].location").value(hasItem(DEFAULT_LOCATION)))
            .andExpect(jsonPath("$.[*].recordType").value(hasItem(DEFAULT_RECORD_TYPE.toString())))
            .andExpect(jsonPath("$.[*].content").value(hasItem(DEFAULT_CONTENT)))
            .andExpect(jsonPath("$.[*].discover").value(hasItem(DEFAULT_DISCOVER)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getProblemInvestigateRecord() throws Exception {
        // Initialize the database
        insertedProblemInvestigateRecord = problemInvestigateRecordRepository.saveAndFlush(problemInvestigateRecord);

        // Get the problemInvestigateRecord
        restProblemInvestigateRecordMockMvc
            .perform(get(ENTITY_API_URL_ID, problemInvestigateRecord.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(problemInvestigateRecord.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.investigateId").value(DEFAULT_INVESTIGATE_ID.intValue()))
            .andExpect(jsonPath("$.recordCode").value(DEFAULT_RECORD_CODE))
            .andExpect(jsonPath("$.location").value(DEFAULT_LOCATION))
            .andExpect(jsonPath("$.recordType").value(DEFAULT_RECORD_TYPE.toString()))
            .andExpect(jsonPath("$.content").value(DEFAULT_CONTENT))
            .andExpect(jsonPath("$.discover").value(DEFAULT_DISCOVER))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingProblemInvestigateRecord() throws Exception {
        // Get the problemInvestigateRecord
        restProblemInvestigateRecordMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingProblemInvestigateRecord() throws Exception {
        // Initialize the database
        insertedProblemInvestigateRecord = problemInvestigateRecordRepository.saveAndFlush(problemInvestigateRecord);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateRecord
        ProblemInvestigateRecord updatedProblemInvestigateRecord = problemInvestigateRecordRepository
            .findById(problemInvestigateRecord.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedProblemInvestigateRecord are not directly saved in db
        em.detach(updatedProblemInvestigateRecord);
        updatedProblemInvestigateRecord
            .tenantId(UPDATED_TENANT_ID)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .recordCode(UPDATED_RECORD_CODE)
            .location(UPDATED_LOCATION)
            .recordType(UPDATED_RECORD_TYPE)
            .content(UPDATED_CONTENT)
            .discover(UPDATED_DISCOVER)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ProblemInvestigateRecordDTO problemInvestigateRecordDTO = problemInvestigateRecordMapper.toDto(updatedProblemInvestigateRecord);

        restProblemInvestigateRecordMockMvc
            .perform(
                put(ENTITY_API_URL_ID, problemInvestigateRecordDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateRecordDTO))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedProblemInvestigateRecordToMatchAllProperties(updatedProblemInvestigateRecord);
    }

    @Test
    @Transactional
    void putNonExistingProblemInvestigateRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateRecord.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateRecord
        ProblemInvestigateRecordDTO problemInvestigateRecordDTO = problemInvestigateRecordMapper.toDto(problemInvestigateRecord);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restProblemInvestigateRecordMockMvc
            .perform(
                put(ENTITY_API_URL_ID, problemInvestigateRecordDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateRecordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchProblemInvestigateRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateRecord.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateRecord
        ProblemInvestigateRecordDTO problemInvestigateRecordDTO = problemInvestigateRecordMapper.toDto(problemInvestigateRecord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateRecordMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateRecordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamProblemInvestigateRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateRecord.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateRecord
        ProblemInvestigateRecordDTO problemInvestigateRecordDTO = problemInvestigateRecordMapper.toDto(problemInvestigateRecord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateRecordMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateRecordDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the ProblemInvestigateRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateProblemInvestigateRecordWithPatch() throws Exception {
        // Initialize the database
        insertedProblemInvestigateRecord = problemInvestigateRecordRepository.saveAndFlush(problemInvestigateRecord);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateRecord using partial update
        ProblemInvestigateRecord partialUpdatedProblemInvestigateRecord = new ProblemInvestigateRecord();
        partialUpdatedProblemInvestigateRecord.setId(problemInvestigateRecord.getId());

        partialUpdatedProblemInvestigateRecord
            .recordCode(UPDATED_RECORD_CODE)
            .location(UPDATED_LOCATION)
            .content(UPDATED_CONTENT)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT);

        restProblemInvestigateRecordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedProblemInvestigateRecord.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedProblemInvestigateRecord))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateRecord in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertProblemInvestigateRecordUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedProblemInvestigateRecord, problemInvestigateRecord),
            getPersistedProblemInvestigateRecord(problemInvestigateRecord)
        );
    }

    @Test
    @Transactional
    void fullUpdateProblemInvestigateRecordWithPatch() throws Exception {
        // Initialize the database
        insertedProblemInvestigateRecord = problemInvestigateRecordRepository.saveAndFlush(problemInvestigateRecord);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateRecord using partial update
        ProblemInvestigateRecord partialUpdatedProblemInvestigateRecord = new ProblemInvestigateRecord();
        partialUpdatedProblemInvestigateRecord.setId(problemInvestigateRecord.getId());

        partialUpdatedProblemInvestigateRecord
            .tenantId(UPDATED_TENANT_ID)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .recordCode(UPDATED_RECORD_CODE)
            .location(UPDATED_LOCATION)
            .recordType(UPDATED_RECORD_TYPE)
            .content(UPDATED_CONTENT)
            .discover(UPDATED_DISCOVER)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restProblemInvestigateRecordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedProblemInvestigateRecord.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedProblemInvestigateRecord))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateRecord in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertProblemInvestigateRecordUpdatableFieldsEquals(
            partialUpdatedProblemInvestigateRecord,
            getPersistedProblemInvestigateRecord(partialUpdatedProblemInvestigateRecord)
        );
    }

    @Test
    @Transactional
    void patchNonExistingProblemInvestigateRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateRecord.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateRecord
        ProblemInvestigateRecordDTO problemInvestigateRecordDTO = problemInvestigateRecordMapper.toDto(problemInvestigateRecord);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restProblemInvestigateRecordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, problemInvestigateRecordDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(problemInvestigateRecordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchProblemInvestigateRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateRecord.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateRecord
        ProblemInvestigateRecordDTO problemInvestigateRecordDTO = problemInvestigateRecordMapper.toDto(problemInvestigateRecord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateRecordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(problemInvestigateRecordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamProblemInvestigateRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateRecord.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateRecord
        ProblemInvestigateRecordDTO problemInvestigateRecordDTO = problemInvestigateRecordMapper.toDto(problemInvestigateRecord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateRecordMockMvc
            .perform(
                patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(problemInvestigateRecordDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ProblemInvestigateRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteProblemInvestigateRecord() throws Exception {
        // Initialize the database
        insertedProblemInvestigateRecord = problemInvestigateRecordRepository.saveAndFlush(problemInvestigateRecord);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the problemInvestigateRecord
        restProblemInvestigateRecordMockMvc
            .perform(delete(ENTITY_API_URL_ID, problemInvestigateRecord.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return problemInvestigateRecordRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ProblemInvestigateRecord getPersistedProblemInvestigateRecord(ProblemInvestigateRecord problemInvestigateRecord) {
        return problemInvestigateRecordRepository.findById(problemInvestigateRecord.getId()).orElseThrow();
    }

    protected void assertPersistedProblemInvestigateRecordToMatchAllProperties(ProblemInvestigateRecord expectedProblemInvestigateRecord) {
        assertProblemInvestigateRecordAllPropertiesEquals(
            expectedProblemInvestigateRecord,
            getPersistedProblemInvestigateRecord(expectedProblemInvestigateRecord)
        );
    }

    protected void assertPersistedProblemInvestigateRecordToMatchUpdatableProperties(
        ProblemInvestigateRecord expectedProblemInvestigateRecord
    ) {
        assertProblemInvestigateRecordAllUpdatablePropertiesEquals(
            expectedProblemInvestigateRecord,
            getPersistedProblemInvestigateRecord(expectedProblemInvestigateRecord)
        );
    }
}
