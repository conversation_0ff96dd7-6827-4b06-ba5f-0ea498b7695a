package com.whiskerguard.violation.web.rest;

import static com.whiskerguard.violation.domain.ProblemInvestigateInvolveAsserts.*;
import static com.whiskerguard.violation.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.violation.IntegrationTest;
import com.whiskerguard.violation.domain.ProblemInvestigateInvolve;
import com.whiskerguard.violation.repository.ProblemInvestigateInvolveRepository;
import com.whiskerguard.violation.service.dto.ProblemInvestigateInvolveDTO;
import com.whiskerguard.violation.service.mapper.ProblemInvestigateInvolveMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ProblemInvestigateInvolveResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ProblemInvestigateInvolveResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_INVESTIGATE_ID = 1L;
    private static final Long UPDATED_INVESTIGATE_ID = 2L;

    private static final Integer DEFAULT_INVOLVE = 1;
    private static final Integer UPDATED_INVOLVE = 2;

    private static final Long DEFAULT_INVOLVE_ID = 1L;
    private static final Long UPDATED_INVOLVE_ID = 2L;

    private static final String DEFAULT_REMARK = "AAAAAAAAAA";
    private static final String UPDATED_REMARK = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/problem-investigate-involves";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ProblemInvestigateInvolveRepository problemInvestigateInvolveRepository;

    @Autowired
    private ProblemInvestigateInvolveMapper problemInvestigateInvolveMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restProblemInvestigateInvolveMockMvc;

    private ProblemInvestigateInvolve problemInvestigateInvolve;

    private ProblemInvestigateInvolve insertedProblemInvestigateInvolve;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ProblemInvestigateInvolve createEntity() {
        return new ProblemInvestigateInvolve()
            .tenantId(DEFAULT_TENANT_ID)
            .investigateId(DEFAULT_INVESTIGATE_ID)
            .involve(DEFAULT_INVOLVE)
            .involveId(DEFAULT_INVOLVE_ID)
            .remark(DEFAULT_REMARK)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ProblemInvestigateInvolve createUpdatedEntity() {
        return new ProblemInvestigateInvolve()
            .tenantId(UPDATED_TENANT_ID)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .involve(UPDATED_INVOLVE)
            .involveId(UPDATED_INVOLVE_ID)
            .remark(UPDATED_REMARK)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        problemInvestigateInvolve = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedProblemInvestigateInvolve != null) {
            problemInvestigateInvolveRepository.delete(insertedProblemInvestigateInvolve);
            insertedProblemInvestigateInvolve = null;
        }
    }

    @Test
    @Transactional
    void createProblemInvestigateInvolve() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ProblemInvestigateInvolve
        ProblemInvestigateInvolveDTO problemInvestigateInvolveDTO = problemInvestigateInvolveMapper.toDto(problemInvestigateInvolve);
        var returnedProblemInvestigateInvolveDTO = om.readValue(
            restProblemInvestigateInvolveMockMvc
                .perform(
                    post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateInvolveDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ProblemInvestigateInvolveDTO.class
        );

        // Validate the ProblemInvestigateInvolve in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedProblemInvestigateInvolve = problemInvestigateInvolveMapper.toEntity(returnedProblemInvestigateInvolveDTO);
        assertProblemInvestigateInvolveUpdatableFieldsEquals(
            returnedProblemInvestigateInvolve,
            getPersistedProblemInvestigateInvolve(returnedProblemInvestigateInvolve)
        );

        insertedProblemInvestigateInvolve = returnedProblemInvestigateInvolve;
    }

    @Test
    @Transactional
    void createProblemInvestigateInvolveWithExistingId() throws Exception {
        // Create the ProblemInvestigateInvolve with an existing ID
        problemInvestigateInvolve.setId(1L);
        ProblemInvestigateInvolveDTO problemInvestigateInvolveDTO = problemInvestigateInvolveMapper.toDto(problemInvestigateInvolve);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restProblemInvestigateInvolveMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateInvolveDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateInvolve in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateInvolve.setTenantId(null);

        // Create the ProblemInvestigateInvolve, which fails.
        ProblemInvestigateInvolveDTO problemInvestigateInvolveDTO = problemInvestigateInvolveMapper.toDto(problemInvestigateInvolve);

        restProblemInvestigateInvolveMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateInvolveDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkInvestigateIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateInvolve.setInvestigateId(null);

        // Create the ProblemInvestigateInvolve, which fails.
        ProblemInvestigateInvolveDTO problemInvestigateInvolveDTO = problemInvestigateInvolveMapper.toDto(problemInvestigateInvolve);

        restProblemInvestigateInvolveMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateInvolveDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkInvolveIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateInvolve.setInvolve(null);

        // Create the ProblemInvestigateInvolve, which fails.
        ProblemInvestigateInvolveDTO problemInvestigateInvolveDTO = problemInvestigateInvolveMapper.toDto(problemInvestigateInvolve);

        restProblemInvestigateInvolveMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateInvolveDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkInvolveIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateInvolve.setInvolveId(null);

        // Create the ProblemInvestigateInvolve, which fails.
        ProblemInvestigateInvolveDTO problemInvestigateInvolveDTO = problemInvestigateInvolveMapper.toDto(problemInvestigateInvolve);

        restProblemInvestigateInvolveMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateInvolveDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateInvolve.setCreatedBy(null);

        // Create the ProblemInvestigateInvolve, which fails.
        ProblemInvestigateInvolveDTO problemInvestigateInvolveDTO = problemInvestigateInvolveMapper.toDto(problemInvestigateInvolve);

        restProblemInvestigateInvolveMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateInvolveDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllProblemInvestigateInvolves() throws Exception {
        // Initialize the database
        insertedProblemInvestigateInvolve = problemInvestigateInvolveRepository.saveAndFlush(problemInvestigateInvolve);

        // Get all the problemInvestigateInvolveList
        restProblemInvestigateInvolveMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(problemInvestigateInvolve.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].investigateId").value(hasItem(DEFAULT_INVESTIGATE_ID.intValue())))
            .andExpect(jsonPath("$.[*].involve").value(hasItem(DEFAULT_INVOLVE)))
            .andExpect(jsonPath("$.[*].involveId").value(hasItem(DEFAULT_INVOLVE_ID.intValue())))
            .andExpect(jsonPath("$.[*].remark").value(hasItem(DEFAULT_REMARK)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getProblemInvestigateInvolve() throws Exception {
        // Initialize the database
        insertedProblemInvestigateInvolve = problemInvestigateInvolveRepository.saveAndFlush(problemInvestigateInvolve);

        // Get the problemInvestigateInvolve
        restProblemInvestigateInvolveMockMvc
            .perform(get(ENTITY_API_URL_ID, problemInvestigateInvolve.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(problemInvestigateInvolve.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.investigateId").value(DEFAULT_INVESTIGATE_ID.intValue()))
            .andExpect(jsonPath("$.involve").value(DEFAULT_INVOLVE))
            .andExpect(jsonPath("$.involveId").value(DEFAULT_INVOLVE_ID.intValue()))
            .andExpect(jsonPath("$.remark").value(DEFAULT_REMARK))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingProblemInvestigateInvolve() throws Exception {
        // Get the problemInvestigateInvolve
        restProblemInvestigateInvolveMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingProblemInvestigateInvolve() throws Exception {
        // Initialize the database
        insertedProblemInvestigateInvolve = problemInvestigateInvolveRepository.saveAndFlush(problemInvestigateInvolve);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateInvolve
        ProblemInvestigateInvolve updatedProblemInvestigateInvolve = problemInvestigateInvolveRepository
            .findById(problemInvestigateInvolve.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedProblemInvestigateInvolve are not directly saved in db
        em.detach(updatedProblemInvestigateInvolve);
        updatedProblemInvestigateInvolve
            .tenantId(UPDATED_TENANT_ID)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .involve(UPDATED_INVOLVE)
            .involveId(UPDATED_INVOLVE_ID)
            .remark(UPDATED_REMARK)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ProblemInvestigateInvolveDTO problemInvestigateInvolveDTO = problemInvestigateInvolveMapper.toDto(updatedProblemInvestigateInvolve);

        restProblemInvestigateInvolveMockMvc
            .perform(
                put(ENTITY_API_URL_ID, problemInvestigateInvolveDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateInvolveDTO))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateInvolve in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedProblemInvestigateInvolveToMatchAllProperties(updatedProblemInvestigateInvolve);
    }

    @Test
    @Transactional
    void putNonExistingProblemInvestigateInvolve() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateInvolve.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateInvolve
        ProblemInvestigateInvolveDTO problemInvestigateInvolveDTO = problemInvestigateInvolveMapper.toDto(problemInvestigateInvolve);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restProblemInvestigateInvolveMockMvc
            .perform(
                put(ENTITY_API_URL_ID, problemInvestigateInvolveDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateInvolveDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateInvolve in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchProblemInvestigateInvolve() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateInvolve.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateInvolve
        ProblemInvestigateInvolveDTO problemInvestigateInvolveDTO = problemInvestigateInvolveMapper.toDto(problemInvestigateInvolve);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateInvolveMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateInvolveDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateInvolve in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamProblemInvestigateInvolve() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateInvolve.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateInvolve
        ProblemInvestigateInvolveDTO problemInvestigateInvolveDTO = problemInvestigateInvolveMapper.toDto(problemInvestigateInvolve);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateInvolveMockMvc
            .perform(
                put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateInvolveDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ProblemInvestigateInvolve in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateProblemInvestigateInvolveWithPatch() throws Exception {
        // Initialize the database
        insertedProblemInvestigateInvolve = problemInvestigateInvolveRepository.saveAndFlush(problemInvestigateInvolve);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateInvolve using partial update
        ProblemInvestigateInvolve partialUpdatedProblemInvestigateInvolve = new ProblemInvestigateInvolve();
        partialUpdatedProblemInvestigateInvolve.setId(problemInvestigateInvolve.getId());

        partialUpdatedProblemInvestigateInvolve
            .tenantId(UPDATED_TENANT_ID)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .involveId(UPDATED_INVOLVE_ID)
            .version(UPDATED_VERSION)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .isDeleted(UPDATED_IS_DELETED);

        restProblemInvestigateInvolveMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedProblemInvestigateInvolve.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedProblemInvestigateInvolve))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateInvolve in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertProblemInvestigateInvolveUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedProblemInvestigateInvolve, problemInvestigateInvolve),
            getPersistedProblemInvestigateInvolve(problemInvestigateInvolve)
        );
    }

    @Test
    @Transactional
    void fullUpdateProblemInvestigateInvolveWithPatch() throws Exception {
        // Initialize the database
        insertedProblemInvestigateInvolve = problemInvestigateInvolveRepository.saveAndFlush(problemInvestigateInvolve);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateInvolve using partial update
        ProblemInvestigateInvolve partialUpdatedProblemInvestigateInvolve = new ProblemInvestigateInvolve();
        partialUpdatedProblemInvestigateInvolve.setId(problemInvestigateInvolve.getId());

        partialUpdatedProblemInvestigateInvolve
            .tenantId(UPDATED_TENANT_ID)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .involve(UPDATED_INVOLVE)
            .involveId(UPDATED_INVOLVE_ID)
            .remark(UPDATED_REMARK)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restProblemInvestigateInvolveMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedProblemInvestigateInvolve.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedProblemInvestigateInvolve))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateInvolve in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertProblemInvestigateInvolveUpdatableFieldsEquals(
            partialUpdatedProblemInvestigateInvolve,
            getPersistedProblemInvestigateInvolve(partialUpdatedProblemInvestigateInvolve)
        );
    }

    @Test
    @Transactional
    void patchNonExistingProblemInvestigateInvolve() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateInvolve.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateInvolve
        ProblemInvestigateInvolveDTO problemInvestigateInvolveDTO = problemInvestigateInvolveMapper.toDto(problemInvestigateInvolve);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restProblemInvestigateInvolveMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, problemInvestigateInvolveDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(problemInvestigateInvolveDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateInvolve in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchProblemInvestigateInvolve() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateInvolve.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateInvolve
        ProblemInvestigateInvolveDTO problemInvestigateInvolveDTO = problemInvestigateInvolveMapper.toDto(problemInvestigateInvolve);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateInvolveMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(problemInvestigateInvolveDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateInvolve in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamProblemInvestigateInvolve() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateInvolve.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateInvolve
        ProblemInvestigateInvolveDTO problemInvestigateInvolveDTO = problemInvestigateInvolveMapper.toDto(problemInvestigateInvolve);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateInvolveMockMvc
            .perform(
                patch(ENTITY_API_URL)
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(problemInvestigateInvolveDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ProblemInvestigateInvolve in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteProblemInvestigateInvolve() throws Exception {
        // Initialize the database
        insertedProblemInvestigateInvolve = problemInvestigateInvolveRepository.saveAndFlush(problemInvestigateInvolve);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the problemInvestigateInvolve
        restProblemInvestigateInvolveMockMvc
            .perform(delete(ENTITY_API_URL_ID, problemInvestigateInvolve.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return problemInvestigateInvolveRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ProblemInvestigateInvolve getPersistedProblemInvestigateInvolve(ProblemInvestigateInvolve problemInvestigateInvolve) {
        return problemInvestigateInvolveRepository.findById(problemInvestigateInvolve.getId()).orElseThrow();
    }

    protected void assertPersistedProblemInvestigateInvolveToMatchAllProperties(
        ProblemInvestigateInvolve expectedProblemInvestigateInvolve
    ) {
        assertProblemInvestigateInvolveAllPropertiesEquals(
            expectedProblemInvestigateInvolve,
            getPersistedProblemInvestigateInvolve(expectedProblemInvestigateInvolve)
        );
    }

    protected void assertPersistedProblemInvestigateInvolveToMatchUpdatableProperties(
        ProblemInvestigateInvolve expectedProblemInvestigateInvolve
    ) {
        assertProblemInvestigateInvolveAllUpdatablePropertiesEquals(
            expectedProblemInvestigateInvolve,
            getPersistedProblemInvestigateInvolve(expectedProblemInvestigateInvolve)
        );
    }
}
