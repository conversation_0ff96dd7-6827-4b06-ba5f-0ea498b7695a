package com.whiskerguard.violation.web.rest;

import static com.whiskerguard.violation.domain.ProblemInvestigateTaskAsserts.*;
import static com.whiskerguard.violation.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.violation.IntegrationTest;
import com.whiskerguard.violation.domain.ProblemInvestigateTask;
import com.whiskerguard.violation.domain.enumeration.InvestigateSource;
import com.whiskerguard.violation.domain.enumeration.InvestigateType;
import com.whiskerguard.violation.domain.enumeration.PriorityLevel;
import com.whiskerguard.violation.domain.enumeration.Status;
import com.whiskerguard.violation.repository.ProblemInvestigateTaskRepository;
import com.whiskerguard.violation.service.dto.ProblemInvestigateTaskDTO;
import com.whiskerguard.violation.service.mapper.ProblemInvestigateTaskMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ProblemInvestigateTaskResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ProblemInvestigateTaskResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_TITLE = "AAAAAAAAAA";
    private static final String UPDATED_TITLE = "BBBBBBBBBB";

    private static final String DEFAULT_INVESTIGATE_CODE = "AAAAAAAAAA";
    private static final String UPDATED_INVESTIGATE_CODE = "BBBBBBBBBB";

    private static final InvestigateType DEFAULT_INVESTIGATE_TYPE = InvestigateType.ADVERTISING_COMPLIANCE;
    private static final InvestigateType UPDATED_INVESTIGATE_TYPE = InvestigateType.SUPPLIER_MANAGEMENT;

    private static final InvestigateSource DEFAULT_INVESTIGATE_SOURCE = InvestigateSource.MARKETING;
    private static final InvestigateSource UPDATED_INVESTIGATE_SOURCE = InvestigateSource.PROCUREMENT;

    private static final PriorityLevel DEFAULT_LEVEL = PriorityLevel.LOW;
    private static final PriorityLevel UPDATED_LEVEL = PriorityLevel.MIDDLE;

    private static final LocalDate DEFAULT_START_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_START_DATE = LocalDate.now(ZoneId.systemDefault());

    private static final LocalDate DEFAULT_FINISH_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_FINISH_DATE = LocalDate.now(ZoneId.systemDefault());

    private static final Long DEFAULT_DUTY_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_DUTY_EMPLOYEE_ID = 2L;

    private static final Long DEFAULT_COORDINATE_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_COORDINATE_EMPLOYEE_ID = 2L;

    private static final Status DEFAULT_STATUS = Status.NO_START;
    private static final Status UPDATED_STATUS = Status.PROGRESSING;

    private static final String DEFAULT_INVESTIGATE_BACKGROUND = "AAAAAAAAAA";
    private static final String UPDATED_INVESTIGATE_BACKGROUND = "BBBBBBBBBB";

    private static final String DEFAULT_INVESTIGATE_TARGET = "AAAAAAAAAA";
    private static final String UPDATED_INVESTIGATE_TARGET = "BBBBBBBBBB";

    private static final String DEFAULT_INVESTIGATE_RANGE = "AAAAAAAAAA";
    private static final String UPDATED_INVESTIGATE_RANGE = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/problem-investigate-tasks";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ProblemInvestigateTaskRepository problemInvestigateTaskRepository;

    @Autowired
    private ProblemInvestigateTaskMapper problemInvestigateTaskMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restProblemInvestigateTaskMockMvc;

    private ProblemInvestigateTask problemInvestigateTask;

    private ProblemInvestigateTask insertedProblemInvestigateTask;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ProblemInvestigateTask createEntity() {
        return new ProblemInvestigateTask()
            .tenantId(DEFAULT_TENANT_ID)
            .title(DEFAULT_TITLE)
            .investigateCode(DEFAULT_INVESTIGATE_CODE)
            .investigateType(DEFAULT_INVESTIGATE_TYPE)
            .investigateSource(DEFAULT_INVESTIGATE_SOURCE)
            .level(DEFAULT_LEVEL)
            .startDate(DEFAULT_START_DATE)
            .finishDate(DEFAULT_FINISH_DATE)
            .dutyEmployeeId(DEFAULT_DUTY_EMPLOYEE_ID)
            .coordinateEmployeeId(DEFAULT_COORDINATE_EMPLOYEE_ID)
            .status(DEFAULT_STATUS)
            .investigateBackground(DEFAULT_INVESTIGATE_BACKGROUND)
            .investigateTarget(DEFAULT_INVESTIGATE_TARGET)
            .investigateRange(DEFAULT_INVESTIGATE_RANGE)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ProblemInvestigateTask createUpdatedEntity() {
        return new ProblemInvestigateTask()
            .tenantId(UPDATED_TENANT_ID)
            .title(UPDATED_TITLE)
            .investigateCode(UPDATED_INVESTIGATE_CODE)
            .investigateType(UPDATED_INVESTIGATE_TYPE)
            .investigateSource(UPDATED_INVESTIGATE_SOURCE)
            .level(UPDATED_LEVEL)
            .startDate(UPDATED_START_DATE)
            .finishDate(UPDATED_FINISH_DATE)
            .dutyEmployeeId(UPDATED_DUTY_EMPLOYEE_ID)
            .coordinateEmployeeId(UPDATED_COORDINATE_EMPLOYEE_ID)
            .status(UPDATED_STATUS)
            .investigateBackground(UPDATED_INVESTIGATE_BACKGROUND)
            .investigateTarget(UPDATED_INVESTIGATE_TARGET)
            .investigateRange(UPDATED_INVESTIGATE_RANGE)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        problemInvestigateTask = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedProblemInvestigateTask != null) {
            problemInvestigateTaskRepository.delete(insertedProblemInvestigateTask);
            insertedProblemInvestigateTask = null;
        }
    }

    @Test
    @Transactional
    void createProblemInvestigateTask() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ProblemInvestigateTask
        ProblemInvestigateTaskDTO problemInvestigateTaskDTO = problemInvestigateTaskMapper.toDto(problemInvestigateTask);
        var returnedProblemInvestigateTaskDTO = om.readValue(
            restProblemInvestigateTaskMockMvc
                .perform(
                    post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateTaskDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ProblemInvestigateTaskDTO.class
        );

        // Validate the ProblemInvestigateTask in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedProblemInvestigateTask = problemInvestigateTaskMapper.toEntity(returnedProblemInvestigateTaskDTO);
        assertProblemInvestigateTaskUpdatableFieldsEquals(
            returnedProblemInvestigateTask,
            getPersistedProblemInvestigateTask(returnedProblemInvestigateTask)
        );

        insertedProblemInvestigateTask = returnedProblemInvestigateTask;
    }

    @Test
    @Transactional
    void createProblemInvestigateTaskWithExistingId() throws Exception {
        // Create the ProblemInvestigateTask with an existing ID
        problemInvestigateTask.setId(1L);
        ProblemInvestigateTaskDTO problemInvestigateTaskDTO = problemInvestigateTaskMapper.toDto(problemInvestigateTask);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restProblemInvestigateTaskMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateTaskDTO)))
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateTask in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateTask.setTenantId(null);

        // Create the ProblemInvestigateTask, which fails.
        ProblemInvestigateTaskDTO problemInvestigateTaskDTO = problemInvestigateTaskMapper.toDto(problemInvestigateTask);

        restProblemInvestigateTaskMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateTaskDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkInvestigateCodeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateTask.setInvestigateCode(null);

        // Create the ProblemInvestigateTask, which fails.
        ProblemInvestigateTaskDTO problemInvestigateTaskDTO = problemInvestigateTaskMapper.toDto(problemInvestigateTask);

        restProblemInvestigateTaskMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateTaskDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkDutyEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateTask.setDutyEmployeeId(null);

        // Create the ProblemInvestigateTask, which fails.
        ProblemInvestigateTaskDTO problemInvestigateTaskDTO = problemInvestigateTaskMapper.toDto(problemInvestigateTask);

        restProblemInvestigateTaskMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateTaskDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCoordinateEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateTask.setCoordinateEmployeeId(null);

        // Create the ProblemInvestigateTask, which fails.
        ProblemInvestigateTaskDTO problemInvestigateTaskDTO = problemInvestigateTaskMapper.toDto(problemInvestigateTask);

        restProblemInvestigateTaskMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateTaskDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateTask.setCreatedBy(null);

        // Create the ProblemInvestigateTask, which fails.
        ProblemInvestigateTaskDTO problemInvestigateTaskDTO = problemInvestigateTaskMapper.toDto(problemInvestigateTask);

        restProblemInvestigateTaskMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateTaskDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllProblemInvestigateTasks() throws Exception {
        // Initialize the database
        insertedProblemInvestigateTask = problemInvestigateTaskRepository.saveAndFlush(problemInvestigateTask);

        // Get all the problemInvestigateTaskList
        restProblemInvestigateTaskMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(problemInvestigateTask.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].title").value(hasItem(DEFAULT_TITLE)))
            .andExpect(jsonPath("$.[*].investigateCode").value(hasItem(DEFAULT_INVESTIGATE_CODE)))
            .andExpect(jsonPath("$.[*].investigateType").value(hasItem(DEFAULT_INVESTIGATE_TYPE.toString())))
            .andExpect(jsonPath("$.[*].investigateSource").value(hasItem(DEFAULT_INVESTIGATE_SOURCE.toString())))
            .andExpect(jsonPath("$.[*].level").value(hasItem(DEFAULT_LEVEL.toString())))
            .andExpect(jsonPath("$.[*].startDate").value(hasItem(DEFAULT_START_DATE.toString())))
            .andExpect(jsonPath("$.[*].finishDate").value(hasItem(DEFAULT_FINISH_DATE.toString())))
            .andExpect(jsonPath("$.[*].dutyEmployeeId").value(hasItem(DEFAULT_DUTY_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].coordinateEmployeeId").value(hasItem(DEFAULT_COORDINATE_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].investigateBackground").value(hasItem(DEFAULT_INVESTIGATE_BACKGROUND)))
            .andExpect(jsonPath("$.[*].investigateTarget").value(hasItem(DEFAULT_INVESTIGATE_TARGET)))
            .andExpect(jsonPath("$.[*].investigateRange").value(hasItem(DEFAULT_INVESTIGATE_RANGE)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getProblemInvestigateTask() throws Exception {
        // Initialize the database
        insertedProblemInvestigateTask = problemInvestigateTaskRepository.saveAndFlush(problemInvestigateTask);

        // Get the problemInvestigateTask
        restProblemInvestigateTaskMockMvc
            .perform(get(ENTITY_API_URL_ID, problemInvestigateTask.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(problemInvestigateTask.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.title").value(DEFAULT_TITLE))
            .andExpect(jsonPath("$.investigateCode").value(DEFAULT_INVESTIGATE_CODE))
            .andExpect(jsonPath("$.investigateType").value(DEFAULT_INVESTIGATE_TYPE.toString()))
            .andExpect(jsonPath("$.investigateSource").value(DEFAULT_INVESTIGATE_SOURCE.toString()))
            .andExpect(jsonPath("$.level").value(DEFAULT_LEVEL.toString()))
            .andExpect(jsonPath("$.startDate").value(DEFAULT_START_DATE.toString()))
            .andExpect(jsonPath("$.finishDate").value(DEFAULT_FINISH_DATE.toString()))
            .andExpect(jsonPath("$.dutyEmployeeId").value(DEFAULT_DUTY_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.coordinateEmployeeId").value(DEFAULT_COORDINATE_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.investigateBackground").value(DEFAULT_INVESTIGATE_BACKGROUND))
            .andExpect(jsonPath("$.investigateTarget").value(DEFAULT_INVESTIGATE_TARGET))
            .andExpect(jsonPath("$.investigateRange").value(DEFAULT_INVESTIGATE_RANGE))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingProblemInvestigateTask() throws Exception {
        // Get the problemInvestigateTask
        restProblemInvestigateTaskMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingProblemInvestigateTask() throws Exception {
        // Initialize the database
        insertedProblemInvestigateTask = problemInvestigateTaskRepository.saveAndFlush(problemInvestigateTask);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateTask
        ProblemInvestigateTask updatedProblemInvestigateTask = problemInvestigateTaskRepository
            .findById(problemInvestigateTask.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedProblemInvestigateTask are not directly saved in db
        em.detach(updatedProblemInvestigateTask);
        updatedProblemInvestigateTask
            .tenantId(UPDATED_TENANT_ID)
            .title(UPDATED_TITLE)
            .investigateCode(UPDATED_INVESTIGATE_CODE)
            .investigateType(UPDATED_INVESTIGATE_TYPE)
            .investigateSource(UPDATED_INVESTIGATE_SOURCE)
            .level(UPDATED_LEVEL)
            .startDate(UPDATED_START_DATE)
            .finishDate(UPDATED_FINISH_DATE)
            .dutyEmployeeId(UPDATED_DUTY_EMPLOYEE_ID)
            .coordinateEmployeeId(UPDATED_COORDINATE_EMPLOYEE_ID)
            .status(UPDATED_STATUS)
            .investigateBackground(UPDATED_INVESTIGATE_BACKGROUND)
            .investigateTarget(UPDATED_INVESTIGATE_TARGET)
            .investigateRange(UPDATED_INVESTIGATE_RANGE)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ProblemInvestigateTaskDTO problemInvestigateTaskDTO = problemInvestigateTaskMapper.toDto(updatedProblemInvestigateTask);

        restProblemInvestigateTaskMockMvc
            .perform(
                put(ENTITY_API_URL_ID, problemInvestigateTaskDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateTaskDTO))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateTask in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedProblemInvestigateTaskToMatchAllProperties(updatedProblemInvestigateTask);
    }

    @Test
    @Transactional
    void putNonExistingProblemInvestigateTask() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateTask.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateTask
        ProblemInvestigateTaskDTO problemInvestigateTaskDTO = problemInvestigateTaskMapper.toDto(problemInvestigateTask);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restProblemInvestigateTaskMockMvc
            .perform(
                put(ENTITY_API_URL_ID, problemInvestigateTaskDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateTaskDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateTask in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchProblemInvestigateTask() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateTask.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateTask
        ProblemInvestigateTaskDTO problemInvestigateTaskDTO = problemInvestigateTaskMapper.toDto(problemInvestigateTask);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateTaskMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateTaskDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateTask in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamProblemInvestigateTask() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateTask.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateTask
        ProblemInvestigateTaskDTO problemInvestigateTaskDTO = problemInvestigateTaskMapper.toDto(problemInvestigateTask);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateTaskMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateTaskDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the ProblemInvestigateTask in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateProblemInvestigateTaskWithPatch() throws Exception {
        // Initialize the database
        insertedProblemInvestigateTask = problemInvestigateTaskRepository.saveAndFlush(problemInvestigateTask);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateTask using partial update
        ProblemInvestigateTask partialUpdatedProblemInvestigateTask = new ProblemInvestigateTask();
        partialUpdatedProblemInvestigateTask.setId(problemInvestigateTask.getId());

        partialUpdatedProblemInvestigateTask
            .tenantId(UPDATED_TENANT_ID)
            .title(UPDATED_TITLE)
            .investigateType(UPDATED_INVESTIGATE_TYPE)
            .finishDate(UPDATED_FINISH_DATE)
            .investigateRange(UPDATED_INVESTIGATE_RANGE)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT);

        restProblemInvestigateTaskMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedProblemInvestigateTask.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedProblemInvestigateTask))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateTask in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertProblemInvestigateTaskUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedProblemInvestigateTask, problemInvestigateTask),
            getPersistedProblemInvestigateTask(problemInvestigateTask)
        );
    }

    @Test
    @Transactional
    void fullUpdateProblemInvestigateTaskWithPatch() throws Exception {
        // Initialize the database
        insertedProblemInvestigateTask = problemInvestigateTaskRepository.saveAndFlush(problemInvestigateTask);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateTask using partial update
        ProblemInvestigateTask partialUpdatedProblemInvestigateTask = new ProblemInvestigateTask();
        partialUpdatedProblemInvestigateTask.setId(problemInvestigateTask.getId());

        partialUpdatedProblemInvestigateTask
            .tenantId(UPDATED_TENANT_ID)
            .title(UPDATED_TITLE)
            .investigateCode(UPDATED_INVESTIGATE_CODE)
            .investigateType(UPDATED_INVESTIGATE_TYPE)
            .investigateSource(UPDATED_INVESTIGATE_SOURCE)
            .level(UPDATED_LEVEL)
            .startDate(UPDATED_START_DATE)
            .finishDate(UPDATED_FINISH_DATE)
            .dutyEmployeeId(UPDATED_DUTY_EMPLOYEE_ID)
            .coordinateEmployeeId(UPDATED_COORDINATE_EMPLOYEE_ID)
            .status(UPDATED_STATUS)
            .investigateBackground(UPDATED_INVESTIGATE_BACKGROUND)
            .investigateTarget(UPDATED_INVESTIGATE_TARGET)
            .investigateRange(UPDATED_INVESTIGATE_RANGE)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restProblemInvestigateTaskMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedProblemInvestigateTask.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedProblemInvestigateTask))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateTask in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertProblemInvestigateTaskUpdatableFieldsEquals(
            partialUpdatedProblemInvestigateTask,
            getPersistedProblemInvestigateTask(partialUpdatedProblemInvestigateTask)
        );
    }

    @Test
    @Transactional
    void patchNonExistingProblemInvestigateTask() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateTask.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateTask
        ProblemInvestigateTaskDTO problemInvestigateTaskDTO = problemInvestigateTaskMapper.toDto(problemInvestigateTask);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restProblemInvestigateTaskMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, problemInvestigateTaskDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(problemInvestigateTaskDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateTask in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchProblemInvestigateTask() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateTask.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateTask
        ProblemInvestigateTaskDTO problemInvestigateTaskDTO = problemInvestigateTaskMapper.toDto(problemInvestigateTask);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateTaskMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(problemInvestigateTaskDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateTask in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamProblemInvestigateTask() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateTask.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateTask
        ProblemInvestigateTaskDTO problemInvestigateTaskDTO = problemInvestigateTaskMapper.toDto(problemInvestigateTask);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateTaskMockMvc
            .perform(
                patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(problemInvestigateTaskDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ProblemInvestigateTask in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteProblemInvestigateTask() throws Exception {
        // Initialize the database
        insertedProblemInvestigateTask = problemInvestigateTaskRepository.saveAndFlush(problemInvestigateTask);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the problemInvestigateTask
        restProblemInvestigateTaskMockMvc
            .perform(delete(ENTITY_API_URL_ID, problemInvestigateTask.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return problemInvestigateTaskRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ProblemInvestigateTask getPersistedProblemInvestigateTask(ProblemInvestigateTask problemInvestigateTask) {
        return problemInvestigateTaskRepository.findById(problemInvestigateTask.getId()).orElseThrow();
    }

    protected void assertPersistedProblemInvestigateTaskToMatchAllProperties(ProblemInvestigateTask expectedProblemInvestigateTask) {
        assertProblemInvestigateTaskAllPropertiesEquals(
            expectedProblemInvestigateTask,
            getPersistedProblemInvestigateTask(expectedProblemInvestigateTask)
        );
    }

    protected void assertPersistedProblemInvestigateTaskToMatchUpdatableProperties(ProblemInvestigateTask expectedProblemInvestigateTask) {
        assertProblemInvestigateTaskAllUpdatablePropertiesEquals(
            expectedProblemInvestigateTask,
            getPersistedProblemInvestigateTask(expectedProblemInvestigateTask)
        );
    }
}
