package com.whiskerguard.violation.web.rest;

import static com.whiskerguard.violation.domain.ViolationChannelAnonymousDesensitizeAsserts.*;
import static com.whiskerguard.violation.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.violation.IntegrationTest;
import com.whiskerguard.violation.domain.ViolationChannelAnonymousDesensitize;
import com.whiskerguard.violation.domain.enumeration.DesensitizeWay;
import com.whiskerguard.violation.domain.enumeration.MsgType;
import com.whiskerguard.violation.repository.ViolationChannelAnonymousDesensitizeRepository;
import com.whiskerguard.violation.service.dto.ViolationChannelAnonymousDesensitizeDTO;
import com.whiskerguard.violation.service.mapper.ViolationChannelAnonymousDesensitizeMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ViolationChannelAnonymousDesensitizeResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ViolationChannelAnonymousDesensitizeResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_ANONYMOUS_ID = 1L;
    private static final Long UPDATED_ANONYMOUS_ID = 2L;

    private static final MsgType DEFAULT_MSG_TYPE = MsgType.ID_CARD;
    private static final MsgType UPDATED_MSG_TYPE = MsgType.PHONE;

    private static final String DEFAULT_MATCH_RULE = "AAAAAAAAAA";
    private static final String UPDATED_MATCH_RULE = "BBBBBBBBBB";

    private static final DesensitizeWay DEFAULT_DESENSITIZE_WAY = DesensitizeWay.ALL;
    private static final DesensitizeWay UPDATED_DESENSITIZE_WAY = DesensitizeWay.PART;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/violation-channel-anonymous-desensitizes";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ViolationChannelAnonymousDesensitizeRepository violationChannelAnonymousDesensitizeRepository;

    @Autowired
    private ViolationChannelAnonymousDesensitizeMapper violationChannelAnonymousDesensitizeMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restViolationChannelAnonymousDesensitizeMockMvc;

    private ViolationChannelAnonymousDesensitize violationChannelAnonymousDesensitize;

    private ViolationChannelAnonymousDesensitize insertedViolationChannelAnonymousDesensitize;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ViolationChannelAnonymousDesensitize createEntity() {
        return new ViolationChannelAnonymousDesensitize()
            .tenantId(DEFAULT_TENANT_ID)
            .anonymousId(DEFAULT_ANONYMOUS_ID)
            .msgType(DEFAULT_MSG_TYPE)
            .matchRule(DEFAULT_MATCH_RULE)
            .desensitizeWay(DEFAULT_DESENSITIZE_WAY)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ViolationChannelAnonymousDesensitize createUpdatedEntity() {
        return new ViolationChannelAnonymousDesensitize()
            .tenantId(UPDATED_TENANT_ID)
            .anonymousId(UPDATED_ANONYMOUS_ID)
            .msgType(UPDATED_MSG_TYPE)
            .matchRule(UPDATED_MATCH_RULE)
            .desensitizeWay(UPDATED_DESENSITIZE_WAY)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        violationChannelAnonymousDesensitize = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedViolationChannelAnonymousDesensitize != null) {
            violationChannelAnonymousDesensitizeRepository.delete(insertedViolationChannelAnonymousDesensitize);
            insertedViolationChannelAnonymousDesensitize = null;
        }
    }

    @Test
    @Transactional
    void createViolationChannelAnonymousDesensitize() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ViolationChannelAnonymousDesensitize
        ViolationChannelAnonymousDesensitizeDTO violationChannelAnonymousDesensitizeDTO = violationChannelAnonymousDesensitizeMapper.toDto(
            violationChannelAnonymousDesensitize
        );
        var returnedViolationChannelAnonymousDesensitizeDTO = om.readValue(
            restViolationChannelAnonymousDesensitizeMockMvc
                .perform(
                    post(ENTITY_API_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(om.writeValueAsBytes(violationChannelAnonymousDesensitizeDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ViolationChannelAnonymousDesensitizeDTO.class
        );

        // Validate the ViolationChannelAnonymousDesensitize in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedViolationChannelAnonymousDesensitize = violationChannelAnonymousDesensitizeMapper.toEntity(
            returnedViolationChannelAnonymousDesensitizeDTO
        );
        assertViolationChannelAnonymousDesensitizeUpdatableFieldsEquals(
            returnedViolationChannelAnonymousDesensitize,
            getPersistedViolationChannelAnonymousDesensitize(returnedViolationChannelAnonymousDesensitize)
        );

        insertedViolationChannelAnonymousDesensitize = returnedViolationChannelAnonymousDesensitize;
    }

    @Test
    @Transactional
    void createViolationChannelAnonymousDesensitizeWithExistingId() throws Exception {
        // Create the ViolationChannelAnonymousDesensitize with an existing ID
        violationChannelAnonymousDesensitize.setId(1L);
        ViolationChannelAnonymousDesensitizeDTO violationChannelAnonymousDesensitizeDTO = violationChannelAnonymousDesensitizeMapper.toDto(
            violationChannelAnonymousDesensitize
        );

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restViolationChannelAnonymousDesensitizeMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(violationChannelAnonymousDesensitizeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ViolationChannelAnonymousDesensitize in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        violationChannelAnonymousDesensitize.setTenantId(null);

        // Create the ViolationChannelAnonymousDesensitize, which fails.
        ViolationChannelAnonymousDesensitizeDTO violationChannelAnonymousDesensitizeDTO = violationChannelAnonymousDesensitizeMapper.toDto(
            violationChannelAnonymousDesensitize
        );

        restViolationChannelAnonymousDesensitizeMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(violationChannelAnonymousDesensitizeDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        violationChannelAnonymousDesensitize.setCreatedBy(null);

        // Create the ViolationChannelAnonymousDesensitize, which fails.
        ViolationChannelAnonymousDesensitizeDTO violationChannelAnonymousDesensitizeDTO = violationChannelAnonymousDesensitizeMapper.toDto(
            violationChannelAnonymousDesensitize
        );

        restViolationChannelAnonymousDesensitizeMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(violationChannelAnonymousDesensitizeDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllViolationChannelAnonymousDesensitizes() throws Exception {
        // Initialize the database
        insertedViolationChannelAnonymousDesensitize = violationChannelAnonymousDesensitizeRepository.saveAndFlush(
            violationChannelAnonymousDesensitize
        );

        // Get all the violationChannelAnonymousDesensitizeList
        restViolationChannelAnonymousDesensitizeMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(violationChannelAnonymousDesensitize.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].anonymousId").value(hasItem(DEFAULT_ANONYMOUS_ID.intValue())))
            .andExpect(jsonPath("$.[*].msgType").value(hasItem(DEFAULT_MSG_TYPE.toString())))
            .andExpect(jsonPath("$.[*].matchRule").value(hasItem(DEFAULT_MATCH_RULE)))
            .andExpect(jsonPath("$.[*].desensitizeWay").value(hasItem(DEFAULT_DESENSITIZE_WAY.toString())))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getViolationChannelAnonymousDesensitize() throws Exception {
        // Initialize the database
        insertedViolationChannelAnonymousDesensitize = violationChannelAnonymousDesensitizeRepository.saveAndFlush(
            violationChannelAnonymousDesensitize
        );

        // Get the violationChannelAnonymousDesensitize
        restViolationChannelAnonymousDesensitizeMockMvc
            .perform(get(ENTITY_API_URL_ID, violationChannelAnonymousDesensitize.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(violationChannelAnonymousDesensitize.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.anonymousId").value(DEFAULT_ANONYMOUS_ID.intValue()))
            .andExpect(jsonPath("$.msgType").value(DEFAULT_MSG_TYPE.toString()))
            .andExpect(jsonPath("$.matchRule").value(DEFAULT_MATCH_RULE))
            .andExpect(jsonPath("$.desensitizeWay").value(DEFAULT_DESENSITIZE_WAY.toString()))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingViolationChannelAnonymousDesensitize() throws Exception {
        // Get the violationChannelAnonymousDesensitize
        restViolationChannelAnonymousDesensitizeMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingViolationChannelAnonymousDesensitize() throws Exception {
        // Initialize the database
        insertedViolationChannelAnonymousDesensitize = violationChannelAnonymousDesensitizeRepository.saveAndFlush(
            violationChannelAnonymousDesensitize
        );

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the violationChannelAnonymousDesensitize
        ViolationChannelAnonymousDesensitize updatedViolationChannelAnonymousDesensitize = violationChannelAnonymousDesensitizeRepository
            .findById(violationChannelAnonymousDesensitize.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedViolationChannelAnonymousDesensitize are not directly saved in db
        em.detach(updatedViolationChannelAnonymousDesensitize);
        updatedViolationChannelAnonymousDesensitize
            .tenantId(UPDATED_TENANT_ID)
            .anonymousId(UPDATED_ANONYMOUS_ID)
            .msgType(UPDATED_MSG_TYPE)
            .matchRule(UPDATED_MATCH_RULE)
            .desensitizeWay(UPDATED_DESENSITIZE_WAY)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ViolationChannelAnonymousDesensitizeDTO violationChannelAnonymousDesensitizeDTO = violationChannelAnonymousDesensitizeMapper.toDto(
            updatedViolationChannelAnonymousDesensitize
        );

        restViolationChannelAnonymousDesensitizeMockMvc
            .perform(
                put(ENTITY_API_URL_ID, violationChannelAnonymousDesensitizeDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(violationChannelAnonymousDesensitizeDTO))
            )
            .andExpect(status().isOk());

        // Validate the ViolationChannelAnonymousDesensitize in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedViolationChannelAnonymousDesensitizeToMatchAllProperties(updatedViolationChannelAnonymousDesensitize);
    }

    @Test
    @Transactional
    void putNonExistingViolationChannelAnonymousDesensitize() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationChannelAnonymousDesensitize.setId(longCount.incrementAndGet());

        // Create the ViolationChannelAnonymousDesensitize
        ViolationChannelAnonymousDesensitizeDTO violationChannelAnonymousDesensitizeDTO = violationChannelAnonymousDesensitizeMapper.toDto(
            violationChannelAnonymousDesensitize
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restViolationChannelAnonymousDesensitizeMockMvc
            .perform(
                put(ENTITY_API_URL_ID, violationChannelAnonymousDesensitizeDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(violationChannelAnonymousDesensitizeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ViolationChannelAnonymousDesensitize in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchViolationChannelAnonymousDesensitize() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationChannelAnonymousDesensitize.setId(longCount.incrementAndGet());

        // Create the ViolationChannelAnonymousDesensitize
        ViolationChannelAnonymousDesensitizeDTO violationChannelAnonymousDesensitizeDTO = violationChannelAnonymousDesensitizeMapper.toDto(
            violationChannelAnonymousDesensitize
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restViolationChannelAnonymousDesensitizeMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(violationChannelAnonymousDesensitizeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ViolationChannelAnonymousDesensitize in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamViolationChannelAnonymousDesensitize() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationChannelAnonymousDesensitize.setId(longCount.incrementAndGet());

        // Create the ViolationChannelAnonymousDesensitize
        ViolationChannelAnonymousDesensitizeDTO violationChannelAnonymousDesensitizeDTO = violationChannelAnonymousDesensitizeMapper.toDto(
            violationChannelAnonymousDesensitize
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restViolationChannelAnonymousDesensitizeMockMvc
            .perform(
                put(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(violationChannelAnonymousDesensitizeDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ViolationChannelAnonymousDesensitize in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateViolationChannelAnonymousDesensitizeWithPatch() throws Exception {
        // Initialize the database
        insertedViolationChannelAnonymousDesensitize = violationChannelAnonymousDesensitizeRepository.saveAndFlush(
            violationChannelAnonymousDesensitize
        );

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the violationChannelAnonymousDesensitize using partial update
        ViolationChannelAnonymousDesensitize partialUpdatedViolationChannelAnonymousDesensitize =
            new ViolationChannelAnonymousDesensitize();
        partialUpdatedViolationChannelAnonymousDesensitize.setId(violationChannelAnonymousDesensitize.getId());

        partialUpdatedViolationChannelAnonymousDesensitize
            .anonymousId(UPDATED_ANONYMOUS_ID)
            .matchRule(UPDATED_MATCH_RULE)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restViolationChannelAnonymousDesensitizeMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedViolationChannelAnonymousDesensitize.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedViolationChannelAnonymousDesensitize))
            )
            .andExpect(status().isOk());

        // Validate the ViolationChannelAnonymousDesensitize in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertViolationChannelAnonymousDesensitizeUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedViolationChannelAnonymousDesensitize, violationChannelAnonymousDesensitize),
            getPersistedViolationChannelAnonymousDesensitize(violationChannelAnonymousDesensitize)
        );
    }

    @Test
    @Transactional
    void fullUpdateViolationChannelAnonymousDesensitizeWithPatch() throws Exception {
        // Initialize the database
        insertedViolationChannelAnonymousDesensitize = violationChannelAnonymousDesensitizeRepository.saveAndFlush(
            violationChannelAnonymousDesensitize
        );

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the violationChannelAnonymousDesensitize using partial update
        ViolationChannelAnonymousDesensitize partialUpdatedViolationChannelAnonymousDesensitize =
            new ViolationChannelAnonymousDesensitize();
        partialUpdatedViolationChannelAnonymousDesensitize.setId(violationChannelAnonymousDesensitize.getId());

        partialUpdatedViolationChannelAnonymousDesensitize
            .tenantId(UPDATED_TENANT_ID)
            .anonymousId(UPDATED_ANONYMOUS_ID)
            .msgType(UPDATED_MSG_TYPE)
            .matchRule(UPDATED_MATCH_RULE)
            .desensitizeWay(UPDATED_DESENSITIZE_WAY)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restViolationChannelAnonymousDesensitizeMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedViolationChannelAnonymousDesensitize.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedViolationChannelAnonymousDesensitize))
            )
            .andExpect(status().isOk());

        // Validate the ViolationChannelAnonymousDesensitize in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertViolationChannelAnonymousDesensitizeUpdatableFieldsEquals(
            partialUpdatedViolationChannelAnonymousDesensitize,
            getPersistedViolationChannelAnonymousDesensitize(partialUpdatedViolationChannelAnonymousDesensitize)
        );
    }

    @Test
    @Transactional
    void patchNonExistingViolationChannelAnonymousDesensitize() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationChannelAnonymousDesensitize.setId(longCount.incrementAndGet());

        // Create the ViolationChannelAnonymousDesensitize
        ViolationChannelAnonymousDesensitizeDTO violationChannelAnonymousDesensitizeDTO = violationChannelAnonymousDesensitizeMapper.toDto(
            violationChannelAnonymousDesensitize
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restViolationChannelAnonymousDesensitizeMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, violationChannelAnonymousDesensitizeDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(violationChannelAnonymousDesensitizeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ViolationChannelAnonymousDesensitize in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchViolationChannelAnonymousDesensitize() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationChannelAnonymousDesensitize.setId(longCount.incrementAndGet());

        // Create the ViolationChannelAnonymousDesensitize
        ViolationChannelAnonymousDesensitizeDTO violationChannelAnonymousDesensitizeDTO = violationChannelAnonymousDesensitizeMapper.toDto(
            violationChannelAnonymousDesensitize
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restViolationChannelAnonymousDesensitizeMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(violationChannelAnonymousDesensitizeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ViolationChannelAnonymousDesensitize in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamViolationChannelAnonymousDesensitize() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationChannelAnonymousDesensitize.setId(longCount.incrementAndGet());

        // Create the ViolationChannelAnonymousDesensitize
        ViolationChannelAnonymousDesensitizeDTO violationChannelAnonymousDesensitizeDTO = violationChannelAnonymousDesensitizeMapper.toDto(
            violationChannelAnonymousDesensitize
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restViolationChannelAnonymousDesensitizeMockMvc
            .perform(
                patch(ENTITY_API_URL)
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(violationChannelAnonymousDesensitizeDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ViolationChannelAnonymousDesensitize in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteViolationChannelAnonymousDesensitize() throws Exception {
        // Initialize the database
        insertedViolationChannelAnonymousDesensitize = violationChannelAnonymousDesensitizeRepository.saveAndFlush(
            violationChannelAnonymousDesensitize
        );

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the violationChannelAnonymousDesensitize
        restViolationChannelAnonymousDesensitizeMockMvc
            .perform(delete(ENTITY_API_URL_ID, violationChannelAnonymousDesensitize.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return violationChannelAnonymousDesensitizeRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ViolationChannelAnonymousDesensitize getPersistedViolationChannelAnonymousDesensitize(
        ViolationChannelAnonymousDesensitize violationChannelAnonymousDesensitize
    ) {
        return violationChannelAnonymousDesensitizeRepository.findById(violationChannelAnonymousDesensitize.getId()).orElseThrow();
    }

    protected void assertPersistedViolationChannelAnonymousDesensitizeToMatchAllProperties(
        ViolationChannelAnonymousDesensitize expectedViolationChannelAnonymousDesensitize
    ) {
        assertViolationChannelAnonymousDesensitizeAllPropertiesEquals(
            expectedViolationChannelAnonymousDesensitize,
            getPersistedViolationChannelAnonymousDesensitize(expectedViolationChannelAnonymousDesensitize)
        );
    }

    protected void assertPersistedViolationChannelAnonymousDesensitizeToMatchUpdatableProperties(
        ViolationChannelAnonymousDesensitize expectedViolationChannelAnonymousDesensitize
    ) {
        assertViolationChannelAnonymousDesensitizeAllUpdatablePropertiesEquals(
            expectedViolationChannelAnonymousDesensitize,
            getPersistedViolationChannelAnonymousDesensitize(expectedViolationChannelAnonymousDesensitize)
        );
    }
}
