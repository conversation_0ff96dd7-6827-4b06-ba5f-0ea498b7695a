package com.whiskerguard.violation.web.rest;

import static com.whiskerguard.violation.domain.ViolationChannelAccessAsserts.*;
import static com.whiskerguard.violation.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.violation.IntegrationTest;
import com.whiskerguard.violation.domain.ViolationChannelAccess;
import com.whiskerguard.violation.repository.ViolationChannelAccessRepository;
import com.whiskerguard.violation.service.dto.ViolationChannelAccessDTO;
import com.whiskerguard.violation.service.mapper.ViolationChannelAccessMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ViolationChannelAccessResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ViolationChannelAccessResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_CHANNEL_ID = 1L;
    private static final Long UPDATED_CHANNEL_ID = 2L;

    private static final Long DEFAULT_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_EMPLOYEE_ID = 2L;

    private static final Long DEFAULT_ORG_UNIT_ID = 1L;
    private static final Long UPDATED_ORG_UNIT_ID = 2L;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/violation-channel-accesses";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ViolationChannelAccessRepository violationChannelAccessRepository;

    @Autowired
    private ViolationChannelAccessMapper violationChannelAccessMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restViolationChannelAccessMockMvc;

    private ViolationChannelAccess violationChannelAccess;

    private ViolationChannelAccess insertedViolationChannelAccess;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ViolationChannelAccess createEntity() {
        return new ViolationChannelAccess()
            .tenantId(DEFAULT_TENANT_ID)
            .channelId(DEFAULT_CHANNEL_ID)
            .employeeId(DEFAULT_EMPLOYEE_ID)
            .orgUnitId(DEFAULT_ORG_UNIT_ID)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ViolationChannelAccess createUpdatedEntity() {
        return new ViolationChannelAccess()
            .tenantId(UPDATED_TENANT_ID)
            .channelId(UPDATED_CHANNEL_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .orgUnitId(UPDATED_ORG_UNIT_ID)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        violationChannelAccess = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedViolationChannelAccess != null) {
            violationChannelAccessRepository.delete(insertedViolationChannelAccess);
            insertedViolationChannelAccess = null;
        }
    }

    @Test
    @Transactional
    void createViolationChannelAccess() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ViolationChannelAccess
        ViolationChannelAccessDTO violationChannelAccessDTO = violationChannelAccessMapper.toDto(violationChannelAccess);
        var returnedViolationChannelAccessDTO = om.readValue(
            restViolationChannelAccessMockMvc
                .perform(
                    post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(violationChannelAccessDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ViolationChannelAccessDTO.class
        );

        // Validate the ViolationChannelAccess in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedViolationChannelAccess = violationChannelAccessMapper.toEntity(returnedViolationChannelAccessDTO);
        assertViolationChannelAccessUpdatableFieldsEquals(
            returnedViolationChannelAccess,
            getPersistedViolationChannelAccess(returnedViolationChannelAccess)
        );

        insertedViolationChannelAccess = returnedViolationChannelAccess;
    }

    @Test
    @Transactional
    void createViolationChannelAccessWithExistingId() throws Exception {
        // Create the ViolationChannelAccess with an existing ID
        violationChannelAccess.setId(1L);
        ViolationChannelAccessDTO violationChannelAccessDTO = violationChannelAccessMapper.toDto(violationChannelAccess);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restViolationChannelAccessMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(violationChannelAccessDTO)))
            .andExpect(status().isBadRequest());

        // Validate the ViolationChannelAccess in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        violationChannelAccess.setTenantId(null);

        // Create the ViolationChannelAccess, which fails.
        ViolationChannelAccessDTO violationChannelAccessDTO = violationChannelAccessMapper.toDto(violationChannelAccess);

        restViolationChannelAccessMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(violationChannelAccessDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        violationChannelAccess.setCreatedBy(null);

        // Create the ViolationChannelAccess, which fails.
        ViolationChannelAccessDTO violationChannelAccessDTO = violationChannelAccessMapper.toDto(violationChannelAccess);

        restViolationChannelAccessMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(violationChannelAccessDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllViolationChannelAccesses() throws Exception {
        // Initialize the database
        insertedViolationChannelAccess = violationChannelAccessRepository.saveAndFlush(violationChannelAccess);

        // Get all the violationChannelAccessList
        restViolationChannelAccessMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(violationChannelAccess.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].channelId").value(hasItem(DEFAULT_CHANNEL_ID.intValue())))
            .andExpect(jsonPath("$.[*].employeeId").value(hasItem(DEFAULT_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].orgUnitId").value(hasItem(DEFAULT_ORG_UNIT_ID.intValue())))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getViolationChannelAccess() throws Exception {
        // Initialize the database
        insertedViolationChannelAccess = violationChannelAccessRepository.saveAndFlush(violationChannelAccess);

        // Get the violationChannelAccess
        restViolationChannelAccessMockMvc
            .perform(get(ENTITY_API_URL_ID, violationChannelAccess.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(violationChannelAccess.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.channelId").value(DEFAULT_CHANNEL_ID.intValue()))
            .andExpect(jsonPath("$.employeeId").value(DEFAULT_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.orgUnitId").value(DEFAULT_ORG_UNIT_ID.intValue()))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingViolationChannelAccess() throws Exception {
        // Get the violationChannelAccess
        restViolationChannelAccessMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingViolationChannelAccess() throws Exception {
        // Initialize the database
        insertedViolationChannelAccess = violationChannelAccessRepository.saveAndFlush(violationChannelAccess);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the violationChannelAccess
        ViolationChannelAccess updatedViolationChannelAccess = violationChannelAccessRepository
            .findById(violationChannelAccess.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedViolationChannelAccess are not directly saved in db
        em.detach(updatedViolationChannelAccess);
        updatedViolationChannelAccess
            .tenantId(UPDATED_TENANT_ID)
            .channelId(UPDATED_CHANNEL_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .orgUnitId(UPDATED_ORG_UNIT_ID)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ViolationChannelAccessDTO violationChannelAccessDTO = violationChannelAccessMapper.toDto(updatedViolationChannelAccess);

        restViolationChannelAccessMockMvc
            .perform(
                put(ENTITY_API_URL_ID, violationChannelAccessDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(violationChannelAccessDTO))
            )
            .andExpect(status().isOk());

        // Validate the ViolationChannelAccess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedViolationChannelAccessToMatchAllProperties(updatedViolationChannelAccess);
    }

    @Test
    @Transactional
    void putNonExistingViolationChannelAccess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationChannelAccess.setId(longCount.incrementAndGet());

        // Create the ViolationChannelAccess
        ViolationChannelAccessDTO violationChannelAccessDTO = violationChannelAccessMapper.toDto(violationChannelAccess);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restViolationChannelAccessMockMvc
            .perform(
                put(ENTITY_API_URL_ID, violationChannelAccessDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(violationChannelAccessDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ViolationChannelAccess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchViolationChannelAccess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationChannelAccess.setId(longCount.incrementAndGet());

        // Create the ViolationChannelAccess
        ViolationChannelAccessDTO violationChannelAccessDTO = violationChannelAccessMapper.toDto(violationChannelAccess);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restViolationChannelAccessMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(violationChannelAccessDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ViolationChannelAccess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamViolationChannelAccess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationChannelAccess.setId(longCount.incrementAndGet());

        // Create the ViolationChannelAccess
        ViolationChannelAccessDTO violationChannelAccessDTO = violationChannelAccessMapper.toDto(violationChannelAccess);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restViolationChannelAccessMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(violationChannelAccessDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the ViolationChannelAccess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateViolationChannelAccessWithPatch() throws Exception {
        // Initialize the database
        insertedViolationChannelAccess = violationChannelAccessRepository.saveAndFlush(violationChannelAccess);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the violationChannelAccess using partial update
        ViolationChannelAccess partialUpdatedViolationChannelAccess = new ViolationChannelAccess();
        partialUpdatedViolationChannelAccess.setId(violationChannelAccess.getId());

        partialUpdatedViolationChannelAccess
            .channelId(UPDATED_CHANNEL_ID)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT);

        restViolationChannelAccessMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedViolationChannelAccess.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedViolationChannelAccess))
            )
            .andExpect(status().isOk());

        // Validate the ViolationChannelAccess in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertViolationChannelAccessUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedViolationChannelAccess, violationChannelAccess),
            getPersistedViolationChannelAccess(violationChannelAccess)
        );
    }

    @Test
    @Transactional
    void fullUpdateViolationChannelAccessWithPatch() throws Exception {
        // Initialize the database
        insertedViolationChannelAccess = violationChannelAccessRepository.saveAndFlush(violationChannelAccess);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the violationChannelAccess using partial update
        ViolationChannelAccess partialUpdatedViolationChannelAccess = new ViolationChannelAccess();
        partialUpdatedViolationChannelAccess.setId(violationChannelAccess.getId());

        partialUpdatedViolationChannelAccess
            .tenantId(UPDATED_TENANT_ID)
            .channelId(UPDATED_CHANNEL_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .orgUnitId(UPDATED_ORG_UNIT_ID)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restViolationChannelAccessMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedViolationChannelAccess.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedViolationChannelAccess))
            )
            .andExpect(status().isOk());

        // Validate the ViolationChannelAccess in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertViolationChannelAccessUpdatableFieldsEquals(
            partialUpdatedViolationChannelAccess,
            getPersistedViolationChannelAccess(partialUpdatedViolationChannelAccess)
        );
    }

    @Test
    @Transactional
    void patchNonExistingViolationChannelAccess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationChannelAccess.setId(longCount.incrementAndGet());

        // Create the ViolationChannelAccess
        ViolationChannelAccessDTO violationChannelAccessDTO = violationChannelAccessMapper.toDto(violationChannelAccess);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restViolationChannelAccessMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, violationChannelAccessDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(violationChannelAccessDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ViolationChannelAccess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchViolationChannelAccess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationChannelAccess.setId(longCount.incrementAndGet());

        // Create the ViolationChannelAccess
        ViolationChannelAccessDTO violationChannelAccessDTO = violationChannelAccessMapper.toDto(violationChannelAccess);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restViolationChannelAccessMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(violationChannelAccessDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ViolationChannelAccess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamViolationChannelAccess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationChannelAccess.setId(longCount.incrementAndGet());

        // Create the ViolationChannelAccess
        ViolationChannelAccessDTO violationChannelAccessDTO = violationChannelAccessMapper.toDto(violationChannelAccess);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restViolationChannelAccessMockMvc
            .perform(
                patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(violationChannelAccessDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ViolationChannelAccess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteViolationChannelAccess() throws Exception {
        // Initialize the database
        insertedViolationChannelAccess = violationChannelAccessRepository.saveAndFlush(violationChannelAccess);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the violationChannelAccess
        restViolationChannelAccessMockMvc
            .perform(delete(ENTITY_API_URL_ID, violationChannelAccess.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return violationChannelAccessRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ViolationChannelAccess getPersistedViolationChannelAccess(ViolationChannelAccess violationChannelAccess) {
        return violationChannelAccessRepository.findById(violationChannelAccess.getId()).orElseThrow();
    }

    protected void assertPersistedViolationChannelAccessToMatchAllProperties(ViolationChannelAccess expectedViolationChannelAccess) {
        assertViolationChannelAccessAllPropertiesEquals(
            expectedViolationChannelAccess,
            getPersistedViolationChannelAccess(expectedViolationChannelAccess)
        );
    }

    protected void assertPersistedViolationChannelAccessToMatchUpdatableProperties(ViolationChannelAccess expectedViolationChannelAccess) {
        assertViolationChannelAccessAllUpdatablePropertiesEquals(
            expectedViolationChannelAccess,
            getPersistedViolationChannelAccess(expectedViolationChannelAccess)
        );
    }
}
