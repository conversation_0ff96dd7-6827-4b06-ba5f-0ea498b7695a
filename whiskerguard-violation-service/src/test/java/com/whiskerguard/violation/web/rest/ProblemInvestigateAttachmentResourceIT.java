package com.whiskerguard.violation.web.rest;

import static com.whiskerguard.violation.domain.ProblemInvestigateAttachmentAsserts.*;
import static com.whiskerguard.violation.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.violation.IntegrationTest;
import com.whiskerguard.violation.domain.ProblemInvestigateAttachment;
import com.whiskerguard.violation.repository.ProblemInvestigateAttachmentRepository;
import com.whiskerguard.violation.service.dto.ProblemInvestigateAttachmentDTO;
import com.whiskerguard.violation.service.mapper.ProblemInvestigateAttachmentMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ProblemInvestigateAttachmentResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ProblemInvestigateAttachmentResourceIT {

    private static final Long DEFAULT_RELATED_ID = 1L;
    private static final Long UPDATED_RELATED_ID = 2L;

    private static final Integer DEFAULT_RELATED_TYPE = 1;
    private static final Integer UPDATED_RELATED_TYPE = 2;

    private static final String DEFAULT_FILE_NAME = "AAAAAAAAAA";
    private static final String UPDATED_FILE_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_PATH = "AAAAAAAAAA";
    private static final String UPDATED_FILE_PATH = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_TYPE = "AAAAAAAAAA";
    private static final String UPDATED_FILE_TYPE = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_SIZE = "AAAAAAAAAA";
    private static final String UPDATED_FILE_SIZE = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_DESC = "AAAAAAAAAA";
    private static final String UPDATED_FILE_DESC = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/problem-investigate-attachments";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ProblemInvestigateAttachmentRepository problemInvestigateAttachmentRepository;

    @Autowired
    private ProblemInvestigateAttachmentMapper problemInvestigateAttachmentMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restProblemInvestigateAttachmentMockMvc;

    private ProblemInvestigateAttachment problemInvestigateAttachment;

    private ProblemInvestigateAttachment insertedProblemInvestigateAttachment;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ProblemInvestigateAttachment createEntity() {
        return new ProblemInvestigateAttachment()
            .relatedId(DEFAULT_RELATED_ID)
            .relatedType(DEFAULT_RELATED_TYPE)
            .fileName(DEFAULT_FILE_NAME)
            .filePath(DEFAULT_FILE_PATH)
            .fileType(DEFAULT_FILE_TYPE)
            .fileSize(DEFAULT_FILE_SIZE)
            .fileDesc(DEFAULT_FILE_DESC)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ProblemInvestigateAttachment createUpdatedEntity() {
        return new ProblemInvestigateAttachment()
            .relatedId(UPDATED_RELATED_ID)
            .relatedType(UPDATED_RELATED_TYPE)
            .fileName(UPDATED_FILE_NAME)
            .filePath(UPDATED_FILE_PATH)
            .fileType(UPDATED_FILE_TYPE)
            .fileSize(UPDATED_FILE_SIZE)
            .fileDesc(UPDATED_FILE_DESC)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        problemInvestigateAttachment = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedProblemInvestigateAttachment != null) {
            problemInvestigateAttachmentRepository.delete(insertedProblemInvestigateAttachment);
            insertedProblemInvestigateAttachment = null;
        }
    }

    @Test
    @Transactional
    void createProblemInvestigateAttachment() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ProblemInvestigateAttachment
        ProblemInvestigateAttachmentDTO problemInvestigateAttachmentDTO = problemInvestigateAttachmentMapper.toDto(
            problemInvestigateAttachment
        );
        var returnedProblemInvestigateAttachmentDTO = om.readValue(
            restProblemInvestigateAttachmentMockMvc
                .perform(
                    post(ENTITY_API_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(om.writeValueAsBytes(problemInvestigateAttachmentDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ProblemInvestigateAttachmentDTO.class
        );

        // Validate the ProblemInvestigateAttachment in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedProblemInvestigateAttachment = problemInvestigateAttachmentMapper.toEntity(returnedProblemInvestigateAttachmentDTO);
        assertProblemInvestigateAttachmentUpdatableFieldsEquals(
            returnedProblemInvestigateAttachment,
            getPersistedProblemInvestigateAttachment(returnedProblemInvestigateAttachment)
        );

        insertedProblemInvestigateAttachment = returnedProblemInvestigateAttachment;
    }

    @Test
    @Transactional
    void createProblemInvestigateAttachmentWithExistingId() throws Exception {
        // Create the ProblemInvestigateAttachment with an existing ID
        problemInvestigateAttachment.setId(1L);
        ProblemInvestigateAttachmentDTO problemInvestigateAttachmentDTO = problemInvestigateAttachmentMapper.toDto(
            problemInvestigateAttachment
        );

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restProblemInvestigateAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkRelatedIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateAttachment.setRelatedId(null);

        // Create the ProblemInvestigateAttachment, which fails.
        ProblemInvestigateAttachmentDTO problemInvestigateAttachmentDTO = problemInvestigateAttachmentMapper.toDto(
            problemInvestigateAttachment
        );

        restProblemInvestigateAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkRelatedTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateAttachment.setRelatedType(null);

        // Create the ProblemInvestigateAttachment, which fails.
        ProblemInvestigateAttachmentDTO problemInvestigateAttachmentDTO = problemInvestigateAttachmentMapper.toDto(
            problemInvestigateAttachment
        );

        restProblemInvestigateAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkFileNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateAttachment.setFileName(null);

        // Create the ProblemInvestigateAttachment, which fails.
        ProblemInvestigateAttachmentDTO problemInvestigateAttachmentDTO = problemInvestigateAttachmentMapper.toDto(
            problemInvestigateAttachment
        );

        restProblemInvestigateAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkFilePathIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateAttachment.setFilePath(null);

        // Create the ProblemInvestigateAttachment, which fails.
        ProblemInvestigateAttachmentDTO problemInvestigateAttachmentDTO = problemInvestigateAttachmentMapper.toDto(
            problemInvestigateAttachment
        );

        restProblemInvestigateAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkFileTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateAttachment.setFileType(null);

        // Create the ProblemInvestigateAttachment, which fails.
        ProblemInvestigateAttachmentDTO problemInvestigateAttachmentDTO = problemInvestigateAttachmentMapper.toDto(
            problemInvestigateAttachment
        );

        restProblemInvestigateAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateAttachment.setCreatedBy(null);

        // Create the ProblemInvestigateAttachment, which fails.
        ProblemInvestigateAttachmentDTO problemInvestigateAttachmentDTO = problemInvestigateAttachmentMapper.toDto(
            problemInvestigateAttachment
        );

        restProblemInvestigateAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllProblemInvestigateAttachments() throws Exception {
        // Initialize the database
        insertedProblemInvestigateAttachment = problemInvestigateAttachmentRepository.saveAndFlush(problemInvestigateAttachment);

        // Get all the problemInvestigateAttachmentList
        restProblemInvestigateAttachmentMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(problemInvestigateAttachment.getId().intValue())))
            .andExpect(jsonPath("$.[*].relatedId").value(hasItem(DEFAULT_RELATED_ID.intValue())))
            .andExpect(jsonPath("$.[*].relatedType").value(hasItem(DEFAULT_RELATED_TYPE)))
            .andExpect(jsonPath("$.[*].fileName").value(hasItem(DEFAULT_FILE_NAME)))
            .andExpect(jsonPath("$.[*].filePath").value(hasItem(DEFAULT_FILE_PATH)))
            .andExpect(jsonPath("$.[*].fileType").value(hasItem(DEFAULT_FILE_TYPE)))
            .andExpect(jsonPath("$.[*].fileSize").value(hasItem(DEFAULT_FILE_SIZE)))
            .andExpect(jsonPath("$.[*].fileDesc").value(hasItem(DEFAULT_FILE_DESC)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getProblemInvestigateAttachment() throws Exception {
        // Initialize the database
        insertedProblemInvestigateAttachment = problemInvestigateAttachmentRepository.saveAndFlush(problemInvestigateAttachment);

        // Get the problemInvestigateAttachment
        restProblemInvestigateAttachmentMockMvc
            .perform(get(ENTITY_API_URL_ID, problemInvestigateAttachment.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(problemInvestigateAttachment.getId().intValue()))
            .andExpect(jsonPath("$.relatedId").value(DEFAULT_RELATED_ID.intValue()))
            .andExpect(jsonPath("$.relatedType").value(DEFAULT_RELATED_TYPE))
            .andExpect(jsonPath("$.fileName").value(DEFAULT_FILE_NAME))
            .andExpect(jsonPath("$.filePath").value(DEFAULT_FILE_PATH))
            .andExpect(jsonPath("$.fileType").value(DEFAULT_FILE_TYPE))
            .andExpect(jsonPath("$.fileSize").value(DEFAULT_FILE_SIZE))
            .andExpect(jsonPath("$.fileDesc").value(DEFAULT_FILE_DESC))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingProblemInvestigateAttachment() throws Exception {
        // Get the problemInvestigateAttachment
        restProblemInvestigateAttachmentMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingProblemInvestigateAttachment() throws Exception {
        // Initialize the database
        insertedProblemInvestigateAttachment = problemInvestigateAttachmentRepository.saveAndFlush(problemInvestigateAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateAttachment
        ProblemInvestigateAttachment updatedProblemInvestigateAttachment = problemInvestigateAttachmentRepository
            .findById(problemInvestigateAttachment.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedProblemInvestigateAttachment are not directly saved in db
        em.detach(updatedProblemInvestigateAttachment);
        updatedProblemInvestigateAttachment
            .relatedId(UPDATED_RELATED_ID)
            .relatedType(UPDATED_RELATED_TYPE)
            .fileName(UPDATED_FILE_NAME)
            .filePath(UPDATED_FILE_PATH)
            .fileType(UPDATED_FILE_TYPE)
            .fileSize(UPDATED_FILE_SIZE)
            .fileDesc(UPDATED_FILE_DESC)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ProblemInvestigateAttachmentDTO problemInvestigateAttachmentDTO = problemInvestigateAttachmentMapper.toDto(
            updatedProblemInvestigateAttachment
        );

        restProblemInvestigateAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, problemInvestigateAttachmentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateAttachmentDTO))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedProblemInvestigateAttachmentToMatchAllProperties(updatedProblemInvestigateAttachment);
    }

    @Test
    @Transactional
    void putNonExistingProblemInvestigateAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateAttachment.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateAttachment
        ProblemInvestigateAttachmentDTO problemInvestigateAttachmentDTO = problemInvestigateAttachmentMapper.toDto(
            problemInvestigateAttachment
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restProblemInvestigateAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, problemInvestigateAttachmentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchProblemInvestigateAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateAttachment.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateAttachment
        ProblemInvestigateAttachmentDTO problemInvestigateAttachmentDTO = problemInvestigateAttachmentMapper.toDto(
            problemInvestigateAttachment
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamProblemInvestigateAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateAttachment.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateAttachment
        ProblemInvestigateAttachmentDTO problemInvestigateAttachmentDTO = problemInvestigateAttachmentMapper.toDto(
            problemInvestigateAttachment
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateAttachmentDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ProblemInvestigateAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateProblemInvestigateAttachmentWithPatch() throws Exception {
        // Initialize the database
        insertedProblemInvestigateAttachment = problemInvestigateAttachmentRepository.saveAndFlush(problemInvestigateAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateAttachment using partial update
        ProblemInvestigateAttachment partialUpdatedProblemInvestigateAttachment = new ProblemInvestigateAttachment();
        partialUpdatedProblemInvestigateAttachment.setId(problemInvestigateAttachment.getId());

        partialUpdatedProblemInvestigateAttachment
            .relatedId(UPDATED_RELATED_ID)
            .relatedType(UPDATED_RELATED_TYPE)
            .fileSize(UPDATED_FILE_SIZE)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdAt(UPDATED_CREATED_AT);

        restProblemInvestigateAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedProblemInvestigateAttachment.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedProblemInvestigateAttachment))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateAttachment in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertProblemInvestigateAttachmentUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedProblemInvestigateAttachment, problemInvestigateAttachment),
            getPersistedProblemInvestigateAttachment(problemInvestigateAttachment)
        );
    }

    @Test
    @Transactional
    void fullUpdateProblemInvestigateAttachmentWithPatch() throws Exception {
        // Initialize the database
        insertedProblemInvestigateAttachment = problemInvestigateAttachmentRepository.saveAndFlush(problemInvestigateAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateAttachment using partial update
        ProblemInvestigateAttachment partialUpdatedProblemInvestigateAttachment = new ProblemInvestigateAttachment();
        partialUpdatedProblemInvestigateAttachment.setId(problemInvestigateAttachment.getId());

        partialUpdatedProblemInvestigateAttachment
            .relatedId(UPDATED_RELATED_ID)
            .relatedType(UPDATED_RELATED_TYPE)
            .fileName(UPDATED_FILE_NAME)
            .filePath(UPDATED_FILE_PATH)
            .fileType(UPDATED_FILE_TYPE)
            .fileSize(UPDATED_FILE_SIZE)
            .fileDesc(UPDATED_FILE_DESC)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restProblemInvestigateAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedProblemInvestigateAttachment.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedProblemInvestigateAttachment))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateAttachment in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertProblemInvestigateAttachmentUpdatableFieldsEquals(
            partialUpdatedProblemInvestigateAttachment,
            getPersistedProblemInvestigateAttachment(partialUpdatedProblemInvestigateAttachment)
        );
    }

    @Test
    @Transactional
    void patchNonExistingProblemInvestigateAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateAttachment.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateAttachment
        ProblemInvestigateAttachmentDTO problemInvestigateAttachmentDTO = problemInvestigateAttachmentMapper.toDto(
            problemInvestigateAttachment
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restProblemInvestigateAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, problemInvestigateAttachmentDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(problemInvestigateAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchProblemInvestigateAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateAttachment.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateAttachment
        ProblemInvestigateAttachmentDTO problemInvestigateAttachmentDTO = problemInvestigateAttachmentMapper.toDto(
            problemInvestigateAttachment
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(problemInvestigateAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamProblemInvestigateAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateAttachment.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateAttachment
        ProblemInvestigateAttachmentDTO problemInvestigateAttachmentDTO = problemInvestigateAttachmentMapper.toDto(
            problemInvestigateAttachment
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL)
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(problemInvestigateAttachmentDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ProblemInvestigateAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteProblemInvestigateAttachment() throws Exception {
        // Initialize the database
        insertedProblemInvestigateAttachment = problemInvestigateAttachmentRepository.saveAndFlush(problemInvestigateAttachment);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the problemInvestigateAttachment
        restProblemInvestigateAttachmentMockMvc
            .perform(delete(ENTITY_API_URL_ID, problemInvestigateAttachment.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return problemInvestigateAttachmentRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ProblemInvestigateAttachment getPersistedProblemInvestigateAttachment(
        ProblemInvestigateAttachment problemInvestigateAttachment
    ) {
        return problemInvestigateAttachmentRepository.findById(problemInvestigateAttachment.getId()).orElseThrow();
    }

    protected void assertPersistedProblemInvestigateAttachmentToMatchAllProperties(
        ProblemInvestigateAttachment expectedProblemInvestigateAttachment
    ) {
        assertProblemInvestigateAttachmentAllPropertiesEquals(
            expectedProblemInvestigateAttachment,
            getPersistedProblemInvestigateAttachment(expectedProblemInvestigateAttachment)
        );
    }

    protected void assertPersistedProblemInvestigateAttachmentToMatchUpdatableProperties(
        ProblemInvestigateAttachment expectedProblemInvestigateAttachment
    ) {
        assertProblemInvestigateAttachmentAllUpdatablePropertiesEquals(
            expectedProblemInvestigateAttachment,
            getPersistedProblemInvestigateAttachment(expectedProblemInvestigateAttachment)
        );
    }
}
