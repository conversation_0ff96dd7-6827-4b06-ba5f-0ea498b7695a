package com.whiskerguard.violation.web.rest;

import static com.whiskerguard.violation.domain.ContinuousImprovementAttachmentAsserts.*;
import static com.whiskerguard.violation.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.violation.IntegrationTest;
import com.whiskerguard.violation.domain.ContinuousImprovementAttachment;
import com.whiskerguard.violation.repository.ContinuousImprovementAttachmentRepository;
import com.whiskerguard.violation.service.dto.ContinuousImprovementAttachmentDTO;
import com.whiskerguard.violation.service.mapper.ContinuousImprovementAttachmentMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ContinuousImprovementAttachmentResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ContinuousImprovementAttachmentResourceIT {

    private static final Long DEFAULT_RELATED_ID = 1L;
    private static final Long UPDATED_RELATED_ID = 2L;

    private static final Integer DEFAULT_RELATED_TYPE = 1;
    private static final Integer UPDATED_RELATED_TYPE = 2;

    private static final String DEFAULT_FILE_NAME = "AAAAAAAAAA";
    private static final String UPDATED_FILE_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_PATH = "AAAAAAAAAA";
    private static final String UPDATED_FILE_PATH = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_TYPE = "AAAAAAAAAA";
    private static final String UPDATED_FILE_TYPE = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_SIZE = "AAAAAAAAAA";
    private static final String UPDATED_FILE_SIZE = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_DESC = "AAAAAAAAAA";
    private static final String UPDATED_FILE_DESC = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/continuous-improvement-attachments";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ContinuousImprovementAttachmentRepository continuousImprovementAttachmentRepository;

    @Autowired
    private ContinuousImprovementAttachmentMapper continuousImprovementAttachmentMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restContinuousImprovementAttachmentMockMvc;

    private ContinuousImprovementAttachment continuousImprovementAttachment;

    private ContinuousImprovementAttachment insertedContinuousImprovementAttachment;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContinuousImprovementAttachment createEntity() {
        return new ContinuousImprovementAttachment()
            .relatedId(DEFAULT_RELATED_ID)
            .relatedType(DEFAULT_RELATED_TYPE)
            .fileName(DEFAULT_FILE_NAME)
            .filePath(DEFAULT_FILE_PATH)
            .fileType(DEFAULT_FILE_TYPE)
            .fileSize(DEFAULT_FILE_SIZE)
            .fileDesc(DEFAULT_FILE_DESC)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContinuousImprovementAttachment createUpdatedEntity() {
        return new ContinuousImprovementAttachment()
            .relatedId(UPDATED_RELATED_ID)
            .relatedType(UPDATED_RELATED_TYPE)
            .fileName(UPDATED_FILE_NAME)
            .filePath(UPDATED_FILE_PATH)
            .fileType(UPDATED_FILE_TYPE)
            .fileSize(UPDATED_FILE_SIZE)
            .fileDesc(UPDATED_FILE_DESC)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        continuousImprovementAttachment = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedContinuousImprovementAttachment != null) {
            continuousImprovementAttachmentRepository.delete(insertedContinuousImprovementAttachment);
            insertedContinuousImprovementAttachment = null;
        }
    }

    @Test
    @Transactional
    void createContinuousImprovementAttachment() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ContinuousImprovementAttachment
        ContinuousImprovementAttachmentDTO continuousImprovementAttachmentDTO = continuousImprovementAttachmentMapper.toDto(
            continuousImprovementAttachment
        );
        var returnedContinuousImprovementAttachmentDTO = om.readValue(
            restContinuousImprovementAttachmentMockMvc
                .perform(
                    post(ENTITY_API_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(om.writeValueAsBytes(continuousImprovementAttachmentDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ContinuousImprovementAttachmentDTO.class
        );

        // Validate the ContinuousImprovementAttachment in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedContinuousImprovementAttachment = continuousImprovementAttachmentMapper.toEntity(
            returnedContinuousImprovementAttachmentDTO
        );
        assertContinuousImprovementAttachmentUpdatableFieldsEquals(
            returnedContinuousImprovementAttachment,
            getPersistedContinuousImprovementAttachment(returnedContinuousImprovementAttachment)
        );

        insertedContinuousImprovementAttachment = returnedContinuousImprovementAttachment;
    }

    @Test
    @Transactional
    void createContinuousImprovementAttachmentWithExistingId() throws Exception {
        // Create the ContinuousImprovementAttachment with an existing ID
        continuousImprovementAttachment.setId(1L);
        ContinuousImprovementAttachmentDTO continuousImprovementAttachmentDTO = continuousImprovementAttachmentMapper.toDto(
            continuousImprovementAttachment
        );

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restContinuousImprovementAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkRelatedIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementAttachment.setRelatedId(null);

        // Create the ContinuousImprovementAttachment, which fails.
        ContinuousImprovementAttachmentDTO continuousImprovementAttachmentDTO = continuousImprovementAttachmentMapper.toDto(
            continuousImprovementAttachment
        );

        restContinuousImprovementAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkRelatedTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementAttachment.setRelatedType(null);

        // Create the ContinuousImprovementAttachment, which fails.
        ContinuousImprovementAttachmentDTO continuousImprovementAttachmentDTO = continuousImprovementAttachmentMapper.toDto(
            continuousImprovementAttachment
        );

        restContinuousImprovementAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkFileNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementAttachment.setFileName(null);

        // Create the ContinuousImprovementAttachment, which fails.
        ContinuousImprovementAttachmentDTO continuousImprovementAttachmentDTO = continuousImprovementAttachmentMapper.toDto(
            continuousImprovementAttachment
        );

        restContinuousImprovementAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkFilePathIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementAttachment.setFilePath(null);

        // Create the ContinuousImprovementAttachment, which fails.
        ContinuousImprovementAttachmentDTO continuousImprovementAttachmentDTO = continuousImprovementAttachmentMapper.toDto(
            continuousImprovementAttachment
        );

        restContinuousImprovementAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkFileTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementAttachment.setFileType(null);

        // Create the ContinuousImprovementAttachment, which fails.
        ContinuousImprovementAttachmentDTO continuousImprovementAttachmentDTO = continuousImprovementAttachmentMapper.toDto(
            continuousImprovementAttachment
        );

        restContinuousImprovementAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementAttachment.setCreatedBy(null);

        // Create the ContinuousImprovementAttachment, which fails.
        ContinuousImprovementAttachmentDTO continuousImprovementAttachmentDTO = continuousImprovementAttachmentMapper.toDto(
            continuousImprovementAttachment
        );

        restContinuousImprovementAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllContinuousImprovementAttachments() throws Exception {
        // Initialize the database
        insertedContinuousImprovementAttachment = continuousImprovementAttachmentRepository.saveAndFlush(continuousImprovementAttachment);

        // Get all the continuousImprovementAttachmentList
        restContinuousImprovementAttachmentMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(continuousImprovementAttachment.getId().intValue())))
            .andExpect(jsonPath("$.[*].relatedId").value(hasItem(DEFAULT_RELATED_ID.intValue())))
            .andExpect(jsonPath("$.[*].relatedType").value(hasItem(DEFAULT_RELATED_TYPE)))
            .andExpect(jsonPath("$.[*].fileName").value(hasItem(DEFAULT_FILE_NAME)))
            .andExpect(jsonPath("$.[*].filePath").value(hasItem(DEFAULT_FILE_PATH)))
            .andExpect(jsonPath("$.[*].fileType").value(hasItem(DEFAULT_FILE_TYPE)))
            .andExpect(jsonPath("$.[*].fileSize").value(hasItem(DEFAULT_FILE_SIZE)))
            .andExpect(jsonPath("$.[*].fileDesc").value(hasItem(DEFAULT_FILE_DESC)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getContinuousImprovementAttachment() throws Exception {
        // Initialize the database
        insertedContinuousImprovementAttachment = continuousImprovementAttachmentRepository.saveAndFlush(continuousImprovementAttachment);

        // Get the continuousImprovementAttachment
        restContinuousImprovementAttachmentMockMvc
            .perform(get(ENTITY_API_URL_ID, continuousImprovementAttachment.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(continuousImprovementAttachment.getId().intValue()))
            .andExpect(jsonPath("$.relatedId").value(DEFAULT_RELATED_ID.intValue()))
            .andExpect(jsonPath("$.relatedType").value(DEFAULT_RELATED_TYPE))
            .andExpect(jsonPath("$.fileName").value(DEFAULT_FILE_NAME))
            .andExpect(jsonPath("$.filePath").value(DEFAULT_FILE_PATH))
            .andExpect(jsonPath("$.fileType").value(DEFAULT_FILE_TYPE))
            .andExpect(jsonPath("$.fileSize").value(DEFAULT_FILE_SIZE))
            .andExpect(jsonPath("$.fileDesc").value(DEFAULT_FILE_DESC))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingContinuousImprovementAttachment() throws Exception {
        // Get the continuousImprovementAttachment
        restContinuousImprovementAttachmentMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingContinuousImprovementAttachment() throws Exception {
        // Initialize the database
        insertedContinuousImprovementAttachment = continuousImprovementAttachmentRepository.saveAndFlush(continuousImprovementAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the continuousImprovementAttachment
        ContinuousImprovementAttachment updatedContinuousImprovementAttachment = continuousImprovementAttachmentRepository
            .findById(continuousImprovementAttachment.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedContinuousImprovementAttachment are not directly saved in db
        em.detach(updatedContinuousImprovementAttachment);
        updatedContinuousImprovementAttachment
            .relatedId(UPDATED_RELATED_ID)
            .relatedType(UPDATED_RELATED_TYPE)
            .fileName(UPDATED_FILE_NAME)
            .filePath(UPDATED_FILE_PATH)
            .fileType(UPDATED_FILE_TYPE)
            .fileSize(UPDATED_FILE_SIZE)
            .fileDesc(UPDATED_FILE_DESC)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ContinuousImprovementAttachmentDTO continuousImprovementAttachmentDTO = continuousImprovementAttachmentMapper.toDto(
            updatedContinuousImprovementAttachment
        );

        restContinuousImprovementAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, continuousImprovementAttachmentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementAttachmentDTO))
            )
            .andExpect(status().isOk());

        // Validate the ContinuousImprovementAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedContinuousImprovementAttachmentToMatchAllProperties(updatedContinuousImprovementAttachment);
    }

    @Test
    @Transactional
    void putNonExistingContinuousImprovementAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementAttachment.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementAttachment
        ContinuousImprovementAttachmentDTO continuousImprovementAttachmentDTO = continuousImprovementAttachmentMapper.toDto(
            continuousImprovementAttachment
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContinuousImprovementAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, continuousImprovementAttachmentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchContinuousImprovementAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementAttachment.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementAttachment
        ContinuousImprovementAttachmentDTO continuousImprovementAttachmentDTO = continuousImprovementAttachmentMapper.toDto(
            continuousImprovementAttachment
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamContinuousImprovementAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementAttachment.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementAttachment
        ContinuousImprovementAttachmentDTO continuousImprovementAttachmentDTO = continuousImprovementAttachmentMapper.toDto(
            continuousImprovementAttachment
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementAttachmentDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContinuousImprovementAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateContinuousImprovementAttachmentWithPatch() throws Exception {
        // Initialize the database
        insertedContinuousImprovementAttachment = continuousImprovementAttachmentRepository.saveAndFlush(continuousImprovementAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the continuousImprovementAttachment using partial update
        ContinuousImprovementAttachment partialUpdatedContinuousImprovementAttachment = new ContinuousImprovementAttachment();
        partialUpdatedContinuousImprovementAttachment.setId(continuousImprovementAttachment.getId());

        partialUpdatedContinuousImprovementAttachment
            .relatedId(UPDATED_RELATED_ID)
            .relatedType(UPDATED_RELATED_TYPE)
            .fileName(UPDATED_FILE_NAME)
            .fileType(UPDATED_FILE_TYPE)
            .fileSize(UPDATED_FILE_SIZE)
            .fileDesc(UPDATED_FILE_DESC)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restContinuousImprovementAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContinuousImprovementAttachment.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContinuousImprovementAttachment))
            )
            .andExpect(status().isOk());

        // Validate the ContinuousImprovementAttachment in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContinuousImprovementAttachmentUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedContinuousImprovementAttachment, continuousImprovementAttachment),
            getPersistedContinuousImprovementAttachment(continuousImprovementAttachment)
        );
    }

    @Test
    @Transactional
    void fullUpdateContinuousImprovementAttachmentWithPatch() throws Exception {
        // Initialize the database
        insertedContinuousImprovementAttachment = continuousImprovementAttachmentRepository.saveAndFlush(continuousImprovementAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the continuousImprovementAttachment using partial update
        ContinuousImprovementAttachment partialUpdatedContinuousImprovementAttachment = new ContinuousImprovementAttachment();
        partialUpdatedContinuousImprovementAttachment.setId(continuousImprovementAttachment.getId());

        partialUpdatedContinuousImprovementAttachment
            .relatedId(UPDATED_RELATED_ID)
            .relatedType(UPDATED_RELATED_TYPE)
            .fileName(UPDATED_FILE_NAME)
            .filePath(UPDATED_FILE_PATH)
            .fileType(UPDATED_FILE_TYPE)
            .fileSize(UPDATED_FILE_SIZE)
            .fileDesc(UPDATED_FILE_DESC)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restContinuousImprovementAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContinuousImprovementAttachment.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContinuousImprovementAttachment))
            )
            .andExpect(status().isOk());

        // Validate the ContinuousImprovementAttachment in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContinuousImprovementAttachmentUpdatableFieldsEquals(
            partialUpdatedContinuousImprovementAttachment,
            getPersistedContinuousImprovementAttachment(partialUpdatedContinuousImprovementAttachment)
        );
    }

    @Test
    @Transactional
    void patchNonExistingContinuousImprovementAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementAttachment.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementAttachment
        ContinuousImprovementAttachmentDTO continuousImprovementAttachmentDTO = continuousImprovementAttachmentMapper.toDto(
            continuousImprovementAttachment
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContinuousImprovementAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, continuousImprovementAttachmentDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(continuousImprovementAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchContinuousImprovementAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementAttachment.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementAttachment
        ContinuousImprovementAttachmentDTO continuousImprovementAttachmentDTO = continuousImprovementAttachmentMapper.toDto(
            continuousImprovementAttachment
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(continuousImprovementAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamContinuousImprovementAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementAttachment.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementAttachment
        ContinuousImprovementAttachmentDTO continuousImprovementAttachmentDTO = continuousImprovementAttachmentMapper.toDto(
            continuousImprovementAttachment
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL)
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(continuousImprovementAttachmentDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContinuousImprovementAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteContinuousImprovementAttachment() throws Exception {
        // Initialize the database
        insertedContinuousImprovementAttachment = continuousImprovementAttachmentRepository.saveAndFlush(continuousImprovementAttachment);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the continuousImprovementAttachment
        restContinuousImprovementAttachmentMockMvc
            .perform(delete(ENTITY_API_URL_ID, continuousImprovementAttachment.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return continuousImprovementAttachmentRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ContinuousImprovementAttachment getPersistedContinuousImprovementAttachment(
        ContinuousImprovementAttachment continuousImprovementAttachment
    ) {
        return continuousImprovementAttachmentRepository.findById(continuousImprovementAttachment.getId()).orElseThrow();
    }

    protected void assertPersistedContinuousImprovementAttachmentToMatchAllProperties(
        ContinuousImprovementAttachment expectedContinuousImprovementAttachment
    ) {
        assertContinuousImprovementAttachmentAllPropertiesEquals(
            expectedContinuousImprovementAttachment,
            getPersistedContinuousImprovementAttachment(expectedContinuousImprovementAttachment)
        );
    }

    protected void assertPersistedContinuousImprovementAttachmentToMatchUpdatableProperties(
        ContinuousImprovementAttachment expectedContinuousImprovementAttachment
    ) {
        assertContinuousImprovementAttachmentAllUpdatablePropertiesEquals(
            expectedContinuousImprovementAttachment,
            getPersistedContinuousImprovementAttachment(expectedContinuousImprovementAttachment)
        );
    }
}
