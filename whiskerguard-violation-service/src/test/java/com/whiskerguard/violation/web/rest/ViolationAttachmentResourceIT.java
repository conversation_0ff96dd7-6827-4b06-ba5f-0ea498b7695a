package com.whiskerguard.violation.web.rest;

import static com.whiskerguard.violation.domain.ViolationAttachmentAsserts.*;
import static com.whiskerguard.violation.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.violation.IntegrationTest;
import com.whiskerguard.violation.domain.ViolationAttachment;
import com.whiskerguard.violation.repository.ViolationAttachmentRepository;
import com.whiskerguard.violation.service.dto.ViolationAttachmentDTO;
import com.whiskerguard.violation.service.mapper.ViolationAttachmentMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ViolationAttachmentResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ViolationAttachmentResourceIT {

    private static final Long DEFAULT_VIOLATION_ID = 1L;
    private static final Long UPDATED_VIOLATION_ID = 2L;

    private static final String DEFAULT_FILE_NAME = "AAAAAAAAAA";
    private static final String UPDATED_FILE_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_PATH = "AAAAAAAAAA";
    private static final String UPDATED_FILE_PATH = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_TYPE = "AAAAAAAAAA";
    private static final String UPDATED_FILE_TYPE = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_SIZE = "AAAAAAAAAA";
    private static final String UPDATED_FILE_SIZE = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_DESC = "AAAAAAAAAA";
    private static final String UPDATED_FILE_DESC = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_UPLOADED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPLOADED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPLOADED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPLOADED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/violation-attachments";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ViolationAttachmentRepository violationAttachmentRepository;

    @Autowired
    private ViolationAttachmentMapper violationAttachmentMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restViolationAttachmentMockMvc;

    private ViolationAttachment violationAttachment;

    private ViolationAttachment insertedViolationAttachment;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ViolationAttachment createEntity() {
        return new ViolationAttachment()
            .violationId(DEFAULT_VIOLATION_ID)
            .fileName(DEFAULT_FILE_NAME)
            .filePath(DEFAULT_FILE_PATH)
            .fileType(DEFAULT_FILE_TYPE)
            .fileSize(DEFAULT_FILE_SIZE)
            .fileDesc(DEFAULT_FILE_DESC)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .uploadedBy(DEFAULT_UPLOADED_BY)
            .uploadedAt(DEFAULT_UPLOADED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ViolationAttachment createUpdatedEntity() {
        return new ViolationAttachment()
            .violationId(UPDATED_VIOLATION_ID)
            .fileName(UPDATED_FILE_NAME)
            .filePath(UPDATED_FILE_PATH)
            .fileType(UPDATED_FILE_TYPE)
            .fileSize(UPDATED_FILE_SIZE)
            .fileDesc(UPDATED_FILE_DESC)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .uploadedBy(UPDATED_UPLOADED_BY)
            .uploadedAt(UPDATED_UPLOADED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        violationAttachment = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedViolationAttachment != null) {
            violationAttachmentRepository.delete(insertedViolationAttachment);
            insertedViolationAttachment = null;
        }
    }

    @Test
    @Transactional
    void createViolationAttachment() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ViolationAttachment
        ViolationAttachmentDTO violationAttachmentDTO = violationAttachmentMapper.toDto(violationAttachment);
        var returnedViolationAttachmentDTO = om.readValue(
            restViolationAttachmentMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(violationAttachmentDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ViolationAttachmentDTO.class
        );

        // Validate the ViolationAttachment in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedViolationAttachment = violationAttachmentMapper.toEntity(returnedViolationAttachmentDTO);
        assertViolationAttachmentUpdatableFieldsEquals(
            returnedViolationAttachment,
            getPersistedViolationAttachment(returnedViolationAttachment)
        );

        insertedViolationAttachment = returnedViolationAttachment;
    }

    @Test
    @Transactional
    void createViolationAttachmentWithExistingId() throws Exception {
        // Create the ViolationAttachment with an existing ID
        violationAttachment.setId(1L);
        ViolationAttachmentDTO violationAttachmentDTO = violationAttachmentMapper.toDto(violationAttachment);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restViolationAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(violationAttachmentDTO)))
            .andExpect(status().isBadRequest());

        // Validate the ViolationAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkViolationIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        violationAttachment.setViolationId(null);

        // Create the ViolationAttachment, which fails.
        ViolationAttachmentDTO violationAttachmentDTO = violationAttachmentMapper.toDto(violationAttachment);

        restViolationAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(violationAttachmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkFileNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        violationAttachment.setFileName(null);

        // Create the ViolationAttachment, which fails.
        ViolationAttachmentDTO violationAttachmentDTO = violationAttachmentMapper.toDto(violationAttachment);

        restViolationAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(violationAttachmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkFilePathIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        violationAttachment.setFilePath(null);

        // Create the ViolationAttachment, which fails.
        ViolationAttachmentDTO violationAttachmentDTO = violationAttachmentMapper.toDto(violationAttachment);

        restViolationAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(violationAttachmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkFileTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        violationAttachment.setFileType(null);

        // Create the ViolationAttachment, which fails.
        ViolationAttachmentDTO violationAttachmentDTO = violationAttachmentMapper.toDto(violationAttachment);

        restViolationAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(violationAttachmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUploadedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        violationAttachment.setUploadedBy(null);

        // Create the ViolationAttachment, which fails.
        ViolationAttachmentDTO violationAttachmentDTO = violationAttachmentMapper.toDto(violationAttachment);

        restViolationAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(violationAttachmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllViolationAttachments() throws Exception {
        // Initialize the database
        insertedViolationAttachment = violationAttachmentRepository.saveAndFlush(violationAttachment);

        // Get all the violationAttachmentList
        restViolationAttachmentMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(violationAttachment.getId().intValue())))
            .andExpect(jsonPath("$.[*].violationId").value(hasItem(DEFAULT_VIOLATION_ID.intValue())))
            .andExpect(jsonPath("$.[*].fileName").value(hasItem(DEFAULT_FILE_NAME)))
            .andExpect(jsonPath("$.[*].filePath").value(hasItem(DEFAULT_FILE_PATH)))
            .andExpect(jsonPath("$.[*].fileType").value(hasItem(DEFAULT_FILE_TYPE)))
            .andExpect(jsonPath("$.[*].fileSize").value(hasItem(DEFAULT_FILE_SIZE)))
            .andExpect(jsonPath("$.[*].fileDesc").value(hasItem(DEFAULT_FILE_DESC)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].uploadedBy").value(hasItem(DEFAULT_UPLOADED_BY)))
            .andExpect(jsonPath("$.[*].uploadedAt").value(hasItem(DEFAULT_UPLOADED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getViolationAttachment() throws Exception {
        // Initialize the database
        insertedViolationAttachment = violationAttachmentRepository.saveAndFlush(violationAttachment);

        // Get the violationAttachment
        restViolationAttachmentMockMvc
            .perform(get(ENTITY_API_URL_ID, violationAttachment.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(violationAttachment.getId().intValue()))
            .andExpect(jsonPath("$.violationId").value(DEFAULT_VIOLATION_ID.intValue()))
            .andExpect(jsonPath("$.fileName").value(DEFAULT_FILE_NAME))
            .andExpect(jsonPath("$.filePath").value(DEFAULT_FILE_PATH))
            .andExpect(jsonPath("$.fileType").value(DEFAULT_FILE_TYPE))
            .andExpect(jsonPath("$.fileSize").value(DEFAULT_FILE_SIZE))
            .andExpect(jsonPath("$.fileDesc").value(DEFAULT_FILE_DESC))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.uploadedBy").value(DEFAULT_UPLOADED_BY))
            .andExpect(jsonPath("$.uploadedAt").value(DEFAULT_UPLOADED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingViolationAttachment() throws Exception {
        // Get the violationAttachment
        restViolationAttachmentMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingViolationAttachment() throws Exception {
        // Initialize the database
        insertedViolationAttachment = violationAttachmentRepository.saveAndFlush(violationAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the violationAttachment
        ViolationAttachment updatedViolationAttachment = violationAttachmentRepository.findById(violationAttachment.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedViolationAttachment are not directly saved in db
        em.detach(updatedViolationAttachment);
        updatedViolationAttachment
            .violationId(UPDATED_VIOLATION_ID)
            .fileName(UPDATED_FILE_NAME)
            .filePath(UPDATED_FILE_PATH)
            .fileType(UPDATED_FILE_TYPE)
            .fileSize(UPDATED_FILE_SIZE)
            .fileDesc(UPDATED_FILE_DESC)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .uploadedBy(UPDATED_UPLOADED_BY)
            .uploadedAt(UPDATED_UPLOADED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ViolationAttachmentDTO violationAttachmentDTO = violationAttachmentMapper.toDto(updatedViolationAttachment);

        restViolationAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, violationAttachmentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(violationAttachmentDTO))
            )
            .andExpect(status().isOk());

        // Validate the ViolationAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedViolationAttachmentToMatchAllProperties(updatedViolationAttachment);
    }

    @Test
    @Transactional
    void putNonExistingViolationAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationAttachment.setId(longCount.incrementAndGet());

        // Create the ViolationAttachment
        ViolationAttachmentDTO violationAttachmentDTO = violationAttachmentMapper.toDto(violationAttachment);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restViolationAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, violationAttachmentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(violationAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ViolationAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchViolationAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationAttachment.setId(longCount.incrementAndGet());

        // Create the ViolationAttachment
        ViolationAttachmentDTO violationAttachmentDTO = violationAttachmentMapper.toDto(violationAttachment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restViolationAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(violationAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ViolationAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamViolationAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationAttachment.setId(longCount.incrementAndGet());

        // Create the ViolationAttachment
        ViolationAttachmentDTO violationAttachmentDTO = violationAttachmentMapper.toDto(violationAttachment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restViolationAttachmentMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(violationAttachmentDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the ViolationAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateViolationAttachmentWithPatch() throws Exception {
        // Initialize the database
        insertedViolationAttachment = violationAttachmentRepository.saveAndFlush(violationAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the violationAttachment using partial update
        ViolationAttachment partialUpdatedViolationAttachment = new ViolationAttachment();
        partialUpdatedViolationAttachment.setId(violationAttachment.getId());

        partialUpdatedViolationAttachment
            .violationId(UPDATED_VIOLATION_ID)
            .filePath(UPDATED_FILE_PATH)
            .fileType(UPDATED_FILE_TYPE)
            .fileSize(UPDATED_FILE_SIZE)
            .fileDesc(UPDATED_FILE_DESC)
            .version(UPDATED_VERSION)
            .isDeleted(UPDATED_IS_DELETED);

        restViolationAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedViolationAttachment.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedViolationAttachment))
            )
            .andExpect(status().isOk());

        // Validate the ViolationAttachment in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertViolationAttachmentUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedViolationAttachment, violationAttachment),
            getPersistedViolationAttachment(violationAttachment)
        );
    }

    @Test
    @Transactional
    void fullUpdateViolationAttachmentWithPatch() throws Exception {
        // Initialize the database
        insertedViolationAttachment = violationAttachmentRepository.saveAndFlush(violationAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the violationAttachment using partial update
        ViolationAttachment partialUpdatedViolationAttachment = new ViolationAttachment();
        partialUpdatedViolationAttachment.setId(violationAttachment.getId());

        partialUpdatedViolationAttachment
            .violationId(UPDATED_VIOLATION_ID)
            .fileName(UPDATED_FILE_NAME)
            .filePath(UPDATED_FILE_PATH)
            .fileType(UPDATED_FILE_TYPE)
            .fileSize(UPDATED_FILE_SIZE)
            .fileDesc(UPDATED_FILE_DESC)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .uploadedBy(UPDATED_UPLOADED_BY)
            .uploadedAt(UPDATED_UPLOADED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restViolationAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedViolationAttachment.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedViolationAttachment))
            )
            .andExpect(status().isOk());

        // Validate the ViolationAttachment in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertViolationAttachmentUpdatableFieldsEquals(
            partialUpdatedViolationAttachment,
            getPersistedViolationAttachment(partialUpdatedViolationAttachment)
        );
    }

    @Test
    @Transactional
    void patchNonExistingViolationAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationAttachment.setId(longCount.incrementAndGet());

        // Create the ViolationAttachment
        ViolationAttachmentDTO violationAttachmentDTO = violationAttachmentMapper.toDto(violationAttachment);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restViolationAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, violationAttachmentDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(violationAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ViolationAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchViolationAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationAttachment.setId(longCount.incrementAndGet());

        // Create the ViolationAttachment
        ViolationAttachmentDTO violationAttachmentDTO = violationAttachmentMapper.toDto(violationAttachment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restViolationAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(violationAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ViolationAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamViolationAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        violationAttachment.setId(longCount.incrementAndGet());

        // Create the ViolationAttachment
        ViolationAttachmentDTO violationAttachmentDTO = violationAttachmentMapper.toDto(violationAttachment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restViolationAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(violationAttachmentDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ViolationAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteViolationAttachment() throws Exception {
        // Initialize the database
        insertedViolationAttachment = violationAttachmentRepository.saveAndFlush(violationAttachment);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the violationAttachment
        restViolationAttachmentMockMvc
            .perform(delete(ENTITY_API_URL_ID, violationAttachment.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return violationAttachmentRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ViolationAttachment getPersistedViolationAttachment(ViolationAttachment violationAttachment) {
        return violationAttachmentRepository.findById(violationAttachment.getId()).orElseThrow();
    }

    protected void assertPersistedViolationAttachmentToMatchAllProperties(ViolationAttachment expectedViolationAttachment) {
        assertViolationAttachmentAllPropertiesEquals(
            expectedViolationAttachment,
            getPersistedViolationAttachment(expectedViolationAttachment)
        );
    }

    protected void assertPersistedViolationAttachmentToMatchUpdatableProperties(ViolationAttachment expectedViolationAttachment) {
        assertViolationAttachmentAllUpdatablePropertiesEquals(
            expectedViolationAttachment,
            getPersistedViolationAttachment(expectedViolationAttachment)
        );
    }
}
