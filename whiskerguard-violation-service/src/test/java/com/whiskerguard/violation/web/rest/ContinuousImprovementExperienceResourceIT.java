package com.whiskerguard.violation.web.rest;

import static com.whiskerguard.violation.domain.ContinuousImprovementExperienceAsserts.*;
import static com.whiskerguard.violation.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.violation.IntegrationTest;
import com.whiskerguard.violation.domain.ContinuousImprovementExperience;
import com.whiskerguard.violation.domain.enumeration.ExperienceSource;
import com.whiskerguard.violation.domain.enumeration.ExperienceStatus;
import com.whiskerguard.violation.domain.enumeration.ExperienceType;
import com.whiskerguard.violation.domain.enumeration.PriorityLevel;
import com.whiskerguard.violation.repository.ContinuousImprovementExperienceRepository;
import com.whiskerguard.violation.service.dto.ContinuousImprovementExperienceDTO;
import com.whiskerguard.violation.service.mapper.ContinuousImprovementExperienceMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ContinuousImprovementExperienceResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ContinuousImprovementExperienceResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_TITLE = "AAAAAAAAAA";
    private static final String UPDATED_TITLE = "BBBBBBBBBB";

    private static final String DEFAULT_EXPERIENCE_CODE = "AAAAAAAAAA";
    private static final String UPDATED_EXPERIENCE_CODE = "BBBBBBBBBB";

    private static final ExperienceType DEFAULT_EXPERIENCE_TYPE = ExperienceType.PROCESS_ISSUE;
    private static final ExperienceType UPDATED_EXPERIENCE_TYPE = ExperienceType.PERSONNEL_ISSUE;

    private static final ExperienceSource DEFAULT_EXPERIENCE_SOURCE = ExperienceSource.AUDIT_FINDING;
    private static final ExperienceSource UPDATED_EXPERIENCE_SOURCE = ExperienceSource.EMPLOYEE_SUBMISSION;

    private static final PriorityLevel DEFAULT_LEVEL = PriorityLevel.LOW;
    private static final PriorityLevel UPDATED_LEVEL = PriorityLevel.MIDDLE;

    private static final String DEFAULT_SOURCE_CODE = "AAAAAAAAAA";
    private static final String UPDATED_SOURCE_CODE = "BBBBBBBBBB";

    private static final Long DEFAULT_DUTY_EMPLOYEE_ORG_ID = 1L;
    private static final Long UPDATED_DUTY_EMPLOYEE_ORG_ID = 2L;

    private static final String DEFAULT_LABEL = "AAAAAAAAAA";
    private static final String UPDATED_LABEL = "BBBBBBBBBB";

    private static final ExperienceStatus DEFAULT_STATUS = ExperienceStatus.DRAFT;
    private static final ExperienceStatus UPDATED_STATUS = ExperienceStatus.PUBLISHED;

    private static final String DEFAULT_PROBLEM_BACKGROUND = "AAAAAAAAAA";
    private static final String UPDATED_PROBLEM_BACKGROUND = "BBBBBBBBBB";

    private static final String DEFAULT_EVENT_COURSE = "AAAAAAAAAA";
    private static final String UPDATED_EVENT_COURSE = "BBBBBBBBBB";

    private static final String DEFAULT_PROBLEM_ANALYSIS = "AAAAAAAAAA";
    private static final String UPDATED_PROBLEM_ANALYSIS = "BBBBBBBBBB";

    private static final String DEFAULT_REASON_CATEGORY = "AAAAAAAAAA";
    private static final String UPDATED_REASON_CATEGORY = "BBBBBBBBBB";

    private static final String DEFAULT_EXPERIENCE_LEARNED = "AAAAAAAAAA";
    private static final String UPDATED_EXPERIENCE_LEARNED = "BBBBBBBBBB";

    private static final String DEFAULT_IMPROVEMENT_SUGGESTION = "AAAAAAAAAA";
    private static final String UPDATED_IMPROVEMENT_SUGGESTION = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/continuous-improvement-experiences";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ContinuousImprovementExperienceRepository continuousImprovementExperienceRepository;

    @Autowired
    private ContinuousImprovementExperienceMapper continuousImprovementExperienceMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restContinuousImprovementExperienceMockMvc;

    private ContinuousImprovementExperience continuousImprovementExperience;

    private ContinuousImprovementExperience insertedContinuousImprovementExperience;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContinuousImprovementExperience createEntity() {
        return new ContinuousImprovementExperience()
            .tenantId(DEFAULT_TENANT_ID)
            .title(DEFAULT_TITLE)
            .experienceCode(DEFAULT_EXPERIENCE_CODE)
            .experienceType(DEFAULT_EXPERIENCE_TYPE)
            .experienceSource(DEFAULT_EXPERIENCE_SOURCE)
            .level(DEFAULT_LEVEL)
            .sourceCode(DEFAULT_SOURCE_CODE)
            .dutyEmployeeOrgId(DEFAULT_DUTY_EMPLOYEE_ORG_ID)
            .label(DEFAULT_LABEL)
            .status(DEFAULT_STATUS)
            .problemBackground(DEFAULT_PROBLEM_BACKGROUND)
            .eventCourse(DEFAULT_EVENT_COURSE)
            .problemAnalysis(DEFAULT_PROBLEM_ANALYSIS)
            .reasonCategory(DEFAULT_REASON_CATEGORY)
            .experienceLearned(DEFAULT_EXPERIENCE_LEARNED)
            .improvementSuggestion(DEFAULT_IMPROVEMENT_SUGGESTION)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContinuousImprovementExperience createUpdatedEntity() {
        return new ContinuousImprovementExperience()
            .tenantId(UPDATED_TENANT_ID)
            .title(UPDATED_TITLE)
            .experienceCode(UPDATED_EXPERIENCE_CODE)
            .experienceType(UPDATED_EXPERIENCE_TYPE)
            .experienceSource(UPDATED_EXPERIENCE_SOURCE)
            .level(UPDATED_LEVEL)
            .sourceCode(UPDATED_SOURCE_CODE)
            .dutyEmployeeOrgId(UPDATED_DUTY_EMPLOYEE_ORG_ID)
            .label(UPDATED_LABEL)
            .status(UPDATED_STATUS)
            .problemBackground(UPDATED_PROBLEM_BACKGROUND)
            .eventCourse(UPDATED_EVENT_COURSE)
            .problemAnalysis(UPDATED_PROBLEM_ANALYSIS)
            .reasonCategory(UPDATED_REASON_CATEGORY)
            .experienceLearned(UPDATED_EXPERIENCE_LEARNED)
            .improvementSuggestion(UPDATED_IMPROVEMENT_SUGGESTION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        continuousImprovementExperience = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedContinuousImprovementExperience != null) {
            continuousImprovementExperienceRepository.delete(insertedContinuousImprovementExperience);
            insertedContinuousImprovementExperience = null;
        }
    }

    @Test
    @Transactional
    void createContinuousImprovementExperience() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ContinuousImprovementExperience
        ContinuousImprovementExperienceDTO continuousImprovementExperienceDTO = continuousImprovementExperienceMapper.toDto(
            continuousImprovementExperience
        );
        var returnedContinuousImprovementExperienceDTO = om.readValue(
            restContinuousImprovementExperienceMockMvc
                .perform(
                    post(ENTITY_API_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(om.writeValueAsBytes(continuousImprovementExperienceDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ContinuousImprovementExperienceDTO.class
        );

        // Validate the ContinuousImprovementExperience in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedContinuousImprovementExperience = continuousImprovementExperienceMapper.toEntity(
            returnedContinuousImprovementExperienceDTO
        );
        assertContinuousImprovementExperienceUpdatableFieldsEquals(
            returnedContinuousImprovementExperience,
            getPersistedContinuousImprovementExperience(returnedContinuousImprovementExperience)
        );

        insertedContinuousImprovementExperience = returnedContinuousImprovementExperience;
    }

    @Test
    @Transactional
    void createContinuousImprovementExperienceWithExistingId() throws Exception {
        // Create the ContinuousImprovementExperience with an existing ID
        continuousImprovementExperience.setId(1L);
        ContinuousImprovementExperienceDTO continuousImprovementExperienceDTO = continuousImprovementExperienceMapper.toDto(
            continuousImprovementExperience
        );

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restContinuousImprovementExperienceMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementExperienceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementExperience in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementExperience.setTenantId(null);

        // Create the ContinuousImprovementExperience, which fails.
        ContinuousImprovementExperienceDTO continuousImprovementExperienceDTO = continuousImprovementExperienceMapper.toDto(
            continuousImprovementExperience
        );

        restContinuousImprovementExperienceMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementExperienceDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkExperienceCodeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementExperience.setExperienceCode(null);

        // Create the ContinuousImprovementExperience, which fails.
        ContinuousImprovementExperienceDTO continuousImprovementExperienceDTO = continuousImprovementExperienceMapper.toDto(
            continuousImprovementExperience
        );

        restContinuousImprovementExperienceMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementExperienceDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkSourceCodeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementExperience.setSourceCode(null);

        // Create the ContinuousImprovementExperience, which fails.
        ContinuousImprovementExperienceDTO continuousImprovementExperienceDTO = continuousImprovementExperienceMapper.toDto(
            continuousImprovementExperience
        );

        restContinuousImprovementExperienceMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementExperienceDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkDutyEmployeeOrgIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementExperience.setDutyEmployeeOrgId(null);

        // Create the ContinuousImprovementExperience, which fails.
        ContinuousImprovementExperienceDTO continuousImprovementExperienceDTO = continuousImprovementExperienceMapper.toDto(
            continuousImprovementExperience
        );

        restContinuousImprovementExperienceMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementExperienceDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementExperience.setCreatedBy(null);

        // Create the ContinuousImprovementExperience, which fails.
        ContinuousImprovementExperienceDTO continuousImprovementExperienceDTO = continuousImprovementExperienceMapper.toDto(
            continuousImprovementExperience
        );

        restContinuousImprovementExperienceMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementExperienceDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllContinuousImprovementExperiences() throws Exception {
        // Initialize the database
        insertedContinuousImprovementExperience = continuousImprovementExperienceRepository.saveAndFlush(continuousImprovementExperience);

        // Get all the continuousImprovementExperienceList
        restContinuousImprovementExperienceMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(continuousImprovementExperience.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].title").value(hasItem(DEFAULT_TITLE)))
            .andExpect(jsonPath("$.[*].experienceCode").value(hasItem(DEFAULT_EXPERIENCE_CODE)))
            .andExpect(jsonPath("$.[*].experienceType").value(hasItem(DEFAULT_EXPERIENCE_TYPE.toString())))
            .andExpect(jsonPath("$.[*].experienceSource").value(hasItem(DEFAULT_EXPERIENCE_SOURCE.toString())))
            .andExpect(jsonPath("$.[*].level").value(hasItem(DEFAULT_LEVEL.toString())))
            .andExpect(jsonPath("$.[*].sourceCode").value(hasItem(DEFAULT_SOURCE_CODE)))
            .andExpect(jsonPath("$.[*].dutyEmployeeOrgId").value(hasItem(DEFAULT_DUTY_EMPLOYEE_ORG_ID.intValue())))
            .andExpect(jsonPath("$.[*].label").value(hasItem(DEFAULT_LABEL)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].problemBackground").value(hasItem(DEFAULT_PROBLEM_BACKGROUND)))
            .andExpect(jsonPath("$.[*].eventCourse").value(hasItem(DEFAULT_EVENT_COURSE)))
            .andExpect(jsonPath("$.[*].problemAnalysis").value(hasItem(DEFAULT_PROBLEM_ANALYSIS)))
            .andExpect(jsonPath("$.[*].reasonCategory").value(hasItem(DEFAULT_REASON_CATEGORY)))
            .andExpect(jsonPath("$.[*].experienceLearned").value(hasItem(DEFAULT_EXPERIENCE_LEARNED)))
            .andExpect(jsonPath("$.[*].improvementSuggestion").value(hasItem(DEFAULT_IMPROVEMENT_SUGGESTION)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getContinuousImprovementExperience() throws Exception {
        // Initialize the database
        insertedContinuousImprovementExperience = continuousImprovementExperienceRepository.saveAndFlush(continuousImprovementExperience);

        // Get the continuousImprovementExperience
        restContinuousImprovementExperienceMockMvc
            .perform(get(ENTITY_API_URL_ID, continuousImprovementExperience.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(continuousImprovementExperience.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.title").value(DEFAULT_TITLE))
            .andExpect(jsonPath("$.experienceCode").value(DEFAULT_EXPERIENCE_CODE))
            .andExpect(jsonPath("$.experienceType").value(DEFAULT_EXPERIENCE_TYPE.toString()))
            .andExpect(jsonPath("$.experienceSource").value(DEFAULT_EXPERIENCE_SOURCE.toString()))
            .andExpect(jsonPath("$.level").value(DEFAULT_LEVEL.toString()))
            .andExpect(jsonPath("$.sourceCode").value(DEFAULT_SOURCE_CODE))
            .andExpect(jsonPath("$.dutyEmployeeOrgId").value(DEFAULT_DUTY_EMPLOYEE_ORG_ID.intValue()))
            .andExpect(jsonPath("$.label").value(DEFAULT_LABEL))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.problemBackground").value(DEFAULT_PROBLEM_BACKGROUND))
            .andExpect(jsonPath("$.eventCourse").value(DEFAULT_EVENT_COURSE))
            .andExpect(jsonPath("$.problemAnalysis").value(DEFAULT_PROBLEM_ANALYSIS))
            .andExpect(jsonPath("$.reasonCategory").value(DEFAULT_REASON_CATEGORY))
            .andExpect(jsonPath("$.experienceLearned").value(DEFAULT_EXPERIENCE_LEARNED))
            .andExpect(jsonPath("$.improvementSuggestion").value(DEFAULT_IMPROVEMENT_SUGGESTION))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingContinuousImprovementExperience() throws Exception {
        // Get the continuousImprovementExperience
        restContinuousImprovementExperienceMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingContinuousImprovementExperience() throws Exception {
        // Initialize the database
        insertedContinuousImprovementExperience = continuousImprovementExperienceRepository.saveAndFlush(continuousImprovementExperience);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the continuousImprovementExperience
        ContinuousImprovementExperience updatedContinuousImprovementExperience = continuousImprovementExperienceRepository
            .findById(continuousImprovementExperience.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedContinuousImprovementExperience are not directly saved in db
        em.detach(updatedContinuousImprovementExperience);
        updatedContinuousImprovementExperience
            .tenantId(UPDATED_TENANT_ID)
            .title(UPDATED_TITLE)
            .experienceCode(UPDATED_EXPERIENCE_CODE)
            .experienceType(UPDATED_EXPERIENCE_TYPE)
            .experienceSource(UPDATED_EXPERIENCE_SOURCE)
            .level(UPDATED_LEVEL)
            .sourceCode(UPDATED_SOURCE_CODE)
            .dutyEmployeeOrgId(UPDATED_DUTY_EMPLOYEE_ORG_ID)
            .label(UPDATED_LABEL)
            .status(UPDATED_STATUS)
            .problemBackground(UPDATED_PROBLEM_BACKGROUND)
            .eventCourse(UPDATED_EVENT_COURSE)
            .problemAnalysis(UPDATED_PROBLEM_ANALYSIS)
            .reasonCategory(UPDATED_REASON_CATEGORY)
            .experienceLearned(UPDATED_EXPERIENCE_LEARNED)
            .improvementSuggestion(UPDATED_IMPROVEMENT_SUGGESTION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ContinuousImprovementExperienceDTO continuousImprovementExperienceDTO = continuousImprovementExperienceMapper.toDto(
            updatedContinuousImprovementExperience
        );

        restContinuousImprovementExperienceMockMvc
            .perform(
                put(ENTITY_API_URL_ID, continuousImprovementExperienceDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementExperienceDTO))
            )
            .andExpect(status().isOk());

        // Validate the ContinuousImprovementExperience in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedContinuousImprovementExperienceToMatchAllProperties(updatedContinuousImprovementExperience);
    }

    @Test
    @Transactional
    void putNonExistingContinuousImprovementExperience() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementExperience.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementExperience
        ContinuousImprovementExperienceDTO continuousImprovementExperienceDTO = continuousImprovementExperienceMapper.toDto(
            continuousImprovementExperience
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContinuousImprovementExperienceMockMvc
            .perform(
                put(ENTITY_API_URL_ID, continuousImprovementExperienceDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementExperienceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementExperience in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchContinuousImprovementExperience() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementExperience.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementExperience
        ContinuousImprovementExperienceDTO continuousImprovementExperienceDTO = continuousImprovementExperienceMapper.toDto(
            continuousImprovementExperience
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementExperienceMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementExperienceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementExperience in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamContinuousImprovementExperience() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementExperience.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementExperience
        ContinuousImprovementExperienceDTO continuousImprovementExperienceDTO = continuousImprovementExperienceMapper.toDto(
            continuousImprovementExperience
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementExperienceMockMvc
            .perform(
                put(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementExperienceDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContinuousImprovementExperience in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateContinuousImprovementExperienceWithPatch() throws Exception {
        // Initialize the database
        insertedContinuousImprovementExperience = continuousImprovementExperienceRepository.saveAndFlush(continuousImprovementExperience);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the continuousImprovementExperience using partial update
        ContinuousImprovementExperience partialUpdatedContinuousImprovementExperience = new ContinuousImprovementExperience();
        partialUpdatedContinuousImprovementExperience.setId(continuousImprovementExperience.getId());

        partialUpdatedContinuousImprovementExperience
            .tenantId(UPDATED_TENANT_ID)
            .experienceType(UPDATED_EXPERIENCE_TYPE)
            .experienceSource(UPDATED_EXPERIENCE_SOURCE)
            .sourceCode(UPDATED_SOURCE_CODE)
            .dutyEmployeeOrgId(UPDATED_DUTY_EMPLOYEE_ORG_ID)
            .label(UPDATED_LABEL)
            .status(UPDATED_STATUS)
            .experienceLearned(UPDATED_EXPERIENCE_LEARNED)
            .version(UPDATED_VERSION)
            .updatedAt(UPDATED_UPDATED_AT);

        restContinuousImprovementExperienceMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContinuousImprovementExperience.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContinuousImprovementExperience))
            )
            .andExpect(status().isOk());

        // Validate the ContinuousImprovementExperience in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContinuousImprovementExperienceUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedContinuousImprovementExperience, continuousImprovementExperience),
            getPersistedContinuousImprovementExperience(continuousImprovementExperience)
        );
    }

    @Test
    @Transactional
    void fullUpdateContinuousImprovementExperienceWithPatch() throws Exception {
        // Initialize the database
        insertedContinuousImprovementExperience = continuousImprovementExperienceRepository.saveAndFlush(continuousImprovementExperience);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the continuousImprovementExperience using partial update
        ContinuousImprovementExperience partialUpdatedContinuousImprovementExperience = new ContinuousImprovementExperience();
        partialUpdatedContinuousImprovementExperience.setId(continuousImprovementExperience.getId());

        partialUpdatedContinuousImprovementExperience
            .tenantId(UPDATED_TENANT_ID)
            .title(UPDATED_TITLE)
            .experienceCode(UPDATED_EXPERIENCE_CODE)
            .experienceType(UPDATED_EXPERIENCE_TYPE)
            .experienceSource(UPDATED_EXPERIENCE_SOURCE)
            .level(UPDATED_LEVEL)
            .sourceCode(UPDATED_SOURCE_CODE)
            .dutyEmployeeOrgId(UPDATED_DUTY_EMPLOYEE_ORG_ID)
            .label(UPDATED_LABEL)
            .status(UPDATED_STATUS)
            .problemBackground(UPDATED_PROBLEM_BACKGROUND)
            .eventCourse(UPDATED_EVENT_COURSE)
            .problemAnalysis(UPDATED_PROBLEM_ANALYSIS)
            .reasonCategory(UPDATED_REASON_CATEGORY)
            .experienceLearned(UPDATED_EXPERIENCE_LEARNED)
            .improvementSuggestion(UPDATED_IMPROVEMENT_SUGGESTION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restContinuousImprovementExperienceMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContinuousImprovementExperience.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContinuousImprovementExperience))
            )
            .andExpect(status().isOk());

        // Validate the ContinuousImprovementExperience in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContinuousImprovementExperienceUpdatableFieldsEquals(
            partialUpdatedContinuousImprovementExperience,
            getPersistedContinuousImprovementExperience(partialUpdatedContinuousImprovementExperience)
        );
    }

    @Test
    @Transactional
    void patchNonExistingContinuousImprovementExperience() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementExperience.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementExperience
        ContinuousImprovementExperienceDTO continuousImprovementExperienceDTO = continuousImprovementExperienceMapper.toDto(
            continuousImprovementExperience
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContinuousImprovementExperienceMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, continuousImprovementExperienceDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(continuousImprovementExperienceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementExperience in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchContinuousImprovementExperience() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementExperience.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementExperience
        ContinuousImprovementExperienceDTO continuousImprovementExperienceDTO = continuousImprovementExperienceMapper.toDto(
            continuousImprovementExperience
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementExperienceMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(continuousImprovementExperienceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementExperience in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamContinuousImprovementExperience() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementExperience.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementExperience
        ContinuousImprovementExperienceDTO continuousImprovementExperienceDTO = continuousImprovementExperienceMapper.toDto(
            continuousImprovementExperience
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementExperienceMockMvc
            .perform(
                patch(ENTITY_API_URL)
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(continuousImprovementExperienceDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContinuousImprovementExperience in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteContinuousImprovementExperience() throws Exception {
        // Initialize the database
        insertedContinuousImprovementExperience = continuousImprovementExperienceRepository.saveAndFlush(continuousImprovementExperience);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the continuousImprovementExperience
        restContinuousImprovementExperienceMockMvc
            .perform(delete(ENTITY_API_URL_ID, continuousImprovementExperience.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return continuousImprovementExperienceRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ContinuousImprovementExperience getPersistedContinuousImprovementExperience(
        ContinuousImprovementExperience continuousImprovementExperience
    ) {
        return continuousImprovementExperienceRepository.findById(continuousImprovementExperience.getId()).orElseThrow();
    }

    protected void assertPersistedContinuousImprovementExperienceToMatchAllProperties(
        ContinuousImprovementExperience expectedContinuousImprovementExperience
    ) {
        assertContinuousImprovementExperienceAllPropertiesEquals(
            expectedContinuousImprovementExperience,
            getPersistedContinuousImprovementExperience(expectedContinuousImprovementExperience)
        );
    }

    protected void assertPersistedContinuousImprovementExperienceToMatchUpdatableProperties(
        ContinuousImprovementExperience expectedContinuousImprovementExperience
    ) {
        assertContinuousImprovementExperienceAllUpdatablePropertiesEquals(
            expectedContinuousImprovementExperience,
            getPersistedContinuousImprovementExperience(expectedContinuousImprovementExperience)
        );
    }
}
