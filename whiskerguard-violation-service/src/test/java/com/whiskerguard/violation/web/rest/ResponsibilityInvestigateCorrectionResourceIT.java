package com.whiskerguard.violation.web.rest;

import static com.whiskerguard.violation.domain.ResponsibilityInvestigateCorrectionAsserts.*;
import static com.whiskerguard.violation.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.violation.IntegrationTest;
import com.whiskerguard.violation.domain.ResponsibilityInvestigateCorrection;
import com.whiskerguard.violation.domain.enumeration.CorrectionType;
import com.whiskerguard.violation.domain.enumeration.PriorityLevel;
import com.whiskerguard.violation.domain.enumeration.Status;
import com.whiskerguard.violation.repository.ResponsibilityInvestigateCorrectionRepository;
import com.whiskerguard.violation.service.dto.ResponsibilityInvestigateCorrectionDTO;
import com.whiskerguard.violation.service.mapper.ResponsibilityInvestigateCorrectionMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ResponsibilityInvestigateCorrectionResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ResponsibilityInvestigateCorrectionResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_CORRECTION_CODE = "AAAAAAAAAA";
    private static final String UPDATED_CORRECTION_CODE = "BBBBBBBBBB";

    private static final CorrectionType DEFAULT_CORRECTION_TYPE = CorrectionType.COMPLIANCE_RISK;
    private static final CorrectionType UPDATED_CORRECTION_TYPE = CorrectionType.OPERATIONAL_RISK;

    private static final PriorityLevel DEFAULT_LEVEL = PriorityLevel.LOW;
    private static final PriorityLevel UPDATED_LEVEL = PriorityLevel.MIDDLE;

    private static final Long DEFAULT_INVESTIGATE_ID = 1L;
    private static final Long UPDATED_INVESTIGATE_ID = 2L;

    private static final Long DEFAULT_DUTY_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_DUTY_EMPLOYEE_ID = 2L;

    private static final Long DEFAULT_DUTY_EMPLOYEE_ORG_ID = 1L;
    private static final Long UPDATED_DUTY_EMPLOYEE_ORG_ID = 2L;

    private static final Long DEFAULT_COLLABORATION_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_COLLABORATION_EMPLOYEE_ID = 2L;

    private static final Long DEFAULT_SUPERVISION_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_SUPERVISION_EMPLOYEE_ID = 2L;

    private static final LocalDate DEFAULT_START_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_START_DATE = LocalDate.now(ZoneId.systemDefault());

    private static final LocalDate DEFAULT_FINISH_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_FINISH_DATE = LocalDate.now(ZoneId.systemDefault());

    private static final Status DEFAULT_STATUS = Status.NO_START;
    private static final Status UPDATED_STATUS = Status.PROGRESSING;

    private static final String DEFAULT_CORRECTION_BACKGROUND = "AAAAAAAAAA";
    private static final String UPDATED_CORRECTION_BACKGROUND = "BBBBBBBBBB";

    private static final String DEFAULT_CORRECTION_REQUIRE = "AAAAAAAAAA";
    private static final String UPDATED_CORRECTION_REQUIRE = "BBBBBBBBBB";

    private static final String DEFAULT_CORRECTION_RANGE = "AAAAAAAAAA";
    private static final String UPDATED_CORRECTION_RANGE = "BBBBBBBBBB";

    private static final String DEFAULT_CORRECTION_SCHEME = "AAAAAAAAAA";
    private static final String UPDATED_CORRECTION_SCHEME = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/responsibility-investigate-corrections";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ResponsibilityInvestigateCorrectionRepository responsibilityInvestigateCorrectionRepository;

    @Autowired
    private ResponsibilityInvestigateCorrectionMapper responsibilityInvestigateCorrectionMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restResponsibilityInvestigateCorrectionMockMvc;

    private ResponsibilityInvestigateCorrection responsibilityInvestigateCorrection;

    private ResponsibilityInvestigateCorrection insertedResponsibilityInvestigateCorrection;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ResponsibilityInvestigateCorrection createEntity() {
        return new ResponsibilityInvestigateCorrection()
            .tenantId(DEFAULT_TENANT_ID)
            .name(DEFAULT_NAME)
            .correctionCode(DEFAULT_CORRECTION_CODE)
            .correctionType(DEFAULT_CORRECTION_TYPE)
            .level(DEFAULT_LEVEL)
            .investigateId(DEFAULT_INVESTIGATE_ID)
            .dutyEmployeeId(DEFAULT_DUTY_EMPLOYEE_ID)
            .dutyEmployeeOrgId(DEFAULT_DUTY_EMPLOYEE_ORG_ID)
            .collaborationEmployeeId(DEFAULT_COLLABORATION_EMPLOYEE_ID)
            .supervisionEmployeeId(DEFAULT_SUPERVISION_EMPLOYEE_ID)
            .startDate(DEFAULT_START_DATE)
            .finishDate(DEFAULT_FINISH_DATE)
            .status(DEFAULT_STATUS)
            .correctionBackground(DEFAULT_CORRECTION_BACKGROUND)
            .correctionRequire(DEFAULT_CORRECTION_REQUIRE)
            .correctionRange(DEFAULT_CORRECTION_RANGE)
            .correctionScheme(DEFAULT_CORRECTION_SCHEME)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ResponsibilityInvestigateCorrection createUpdatedEntity() {
        return new ResponsibilityInvestigateCorrection()
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .correctionCode(UPDATED_CORRECTION_CODE)
            .correctionType(UPDATED_CORRECTION_TYPE)
            .level(UPDATED_LEVEL)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .dutyEmployeeId(UPDATED_DUTY_EMPLOYEE_ID)
            .dutyEmployeeOrgId(UPDATED_DUTY_EMPLOYEE_ORG_ID)
            .collaborationEmployeeId(UPDATED_COLLABORATION_EMPLOYEE_ID)
            .supervisionEmployeeId(UPDATED_SUPERVISION_EMPLOYEE_ID)
            .startDate(UPDATED_START_DATE)
            .finishDate(UPDATED_FINISH_DATE)
            .status(UPDATED_STATUS)
            .correctionBackground(UPDATED_CORRECTION_BACKGROUND)
            .correctionRequire(UPDATED_CORRECTION_REQUIRE)
            .correctionRange(UPDATED_CORRECTION_RANGE)
            .correctionScheme(UPDATED_CORRECTION_SCHEME)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        responsibilityInvestigateCorrection = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedResponsibilityInvestigateCorrection != null) {
            responsibilityInvestigateCorrectionRepository.delete(insertedResponsibilityInvestigateCorrection);
            insertedResponsibilityInvestigateCorrection = null;
        }
    }

    @Test
    @Transactional
    void createResponsibilityInvestigateCorrection() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ResponsibilityInvestigateCorrection
        ResponsibilityInvestigateCorrectionDTO responsibilityInvestigateCorrectionDTO = responsibilityInvestigateCorrectionMapper.toDto(
            responsibilityInvestigateCorrection
        );
        var returnedResponsibilityInvestigateCorrectionDTO = om.readValue(
            restResponsibilityInvestigateCorrectionMockMvc
                .perform(
                    post(ENTITY_API_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(om.writeValueAsBytes(responsibilityInvestigateCorrectionDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ResponsibilityInvestigateCorrectionDTO.class
        );

        // Validate the ResponsibilityInvestigateCorrection in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedResponsibilityInvestigateCorrection = responsibilityInvestigateCorrectionMapper.toEntity(
            returnedResponsibilityInvestigateCorrectionDTO
        );
        assertResponsibilityInvestigateCorrectionUpdatableFieldsEquals(
            returnedResponsibilityInvestigateCorrection,
            getPersistedResponsibilityInvestigateCorrection(returnedResponsibilityInvestigateCorrection)
        );

        insertedResponsibilityInvestigateCorrection = returnedResponsibilityInvestigateCorrection;
    }

    @Test
    @Transactional
    void createResponsibilityInvestigateCorrectionWithExistingId() throws Exception {
        // Create the ResponsibilityInvestigateCorrection with an existing ID
        responsibilityInvestigateCorrection.setId(1L);
        ResponsibilityInvestigateCorrectionDTO responsibilityInvestigateCorrectionDTO = responsibilityInvestigateCorrectionMapper.toDto(
            responsibilityInvestigateCorrection
        );

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateCorrectionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ResponsibilityInvestigateCorrection in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateCorrection.setTenantId(null);

        // Create the ResponsibilityInvestigateCorrection, which fails.
        ResponsibilityInvestigateCorrectionDTO responsibilityInvestigateCorrectionDTO = responsibilityInvestigateCorrectionMapper.toDto(
            responsibilityInvestigateCorrection
        );

        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateCorrectionDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCorrectionCodeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateCorrection.setCorrectionCode(null);

        // Create the ResponsibilityInvestigateCorrection, which fails.
        ResponsibilityInvestigateCorrectionDTO responsibilityInvestigateCorrectionDTO = responsibilityInvestigateCorrectionMapper.toDto(
            responsibilityInvestigateCorrection
        );

        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateCorrectionDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkInvestigateIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateCorrection.setInvestigateId(null);

        // Create the ResponsibilityInvestigateCorrection, which fails.
        ResponsibilityInvestigateCorrectionDTO responsibilityInvestigateCorrectionDTO = responsibilityInvestigateCorrectionMapper.toDto(
            responsibilityInvestigateCorrection
        );

        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateCorrectionDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkDutyEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateCorrection.setDutyEmployeeId(null);

        // Create the ResponsibilityInvestigateCorrection, which fails.
        ResponsibilityInvestigateCorrectionDTO responsibilityInvestigateCorrectionDTO = responsibilityInvestigateCorrectionMapper.toDto(
            responsibilityInvestigateCorrection
        );

        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateCorrectionDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkDutyEmployeeOrgIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateCorrection.setDutyEmployeeOrgId(null);

        // Create the ResponsibilityInvestigateCorrection, which fails.
        ResponsibilityInvestigateCorrectionDTO responsibilityInvestigateCorrectionDTO = responsibilityInvestigateCorrectionMapper.toDto(
            responsibilityInvestigateCorrection
        );

        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateCorrectionDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCollaborationEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateCorrection.setCollaborationEmployeeId(null);

        // Create the ResponsibilityInvestigateCorrection, which fails.
        ResponsibilityInvestigateCorrectionDTO responsibilityInvestigateCorrectionDTO = responsibilityInvestigateCorrectionMapper.toDto(
            responsibilityInvestigateCorrection
        );

        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateCorrectionDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkSupervisionEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateCorrection.setSupervisionEmployeeId(null);

        // Create the ResponsibilityInvestigateCorrection, which fails.
        ResponsibilityInvestigateCorrectionDTO responsibilityInvestigateCorrectionDTO = responsibilityInvestigateCorrectionMapper.toDto(
            responsibilityInvestigateCorrection
        );

        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateCorrectionDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateCorrection.setCreatedBy(null);

        // Create the ResponsibilityInvestigateCorrection, which fails.
        ResponsibilityInvestigateCorrectionDTO responsibilityInvestigateCorrectionDTO = responsibilityInvestigateCorrectionMapper.toDto(
            responsibilityInvestigateCorrection
        );

        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateCorrectionDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllResponsibilityInvestigateCorrections() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateCorrection = responsibilityInvestigateCorrectionRepository.saveAndFlush(
            responsibilityInvestigateCorrection
        );

        // Get all the responsibilityInvestigateCorrectionList
        restResponsibilityInvestigateCorrectionMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(responsibilityInvestigateCorrection.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].name").value(hasItem(DEFAULT_NAME)))
            .andExpect(jsonPath("$.[*].correctionCode").value(hasItem(DEFAULT_CORRECTION_CODE)))
            .andExpect(jsonPath("$.[*].correctionType").value(hasItem(DEFAULT_CORRECTION_TYPE.toString())))
            .andExpect(jsonPath("$.[*].level").value(hasItem(DEFAULT_LEVEL.toString())))
            .andExpect(jsonPath("$.[*].investigateId").value(hasItem(DEFAULT_INVESTIGATE_ID.intValue())))
            .andExpect(jsonPath("$.[*].dutyEmployeeId").value(hasItem(DEFAULT_DUTY_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].dutyEmployeeOrgId").value(hasItem(DEFAULT_DUTY_EMPLOYEE_ORG_ID.intValue())))
            .andExpect(jsonPath("$.[*].collaborationEmployeeId").value(hasItem(DEFAULT_COLLABORATION_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].supervisionEmployeeId").value(hasItem(DEFAULT_SUPERVISION_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].startDate").value(hasItem(DEFAULT_START_DATE.toString())))
            .andExpect(jsonPath("$.[*].finishDate").value(hasItem(DEFAULT_FINISH_DATE.toString())))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].correctionBackground").value(hasItem(DEFAULT_CORRECTION_BACKGROUND)))
            .andExpect(jsonPath("$.[*].correctionRequire").value(hasItem(DEFAULT_CORRECTION_REQUIRE)))
            .andExpect(jsonPath("$.[*].correctionRange").value(hasItem(DEFAULT_CORRECTION_RANGE)))
            .andExpect(jsonPath("$.[*].correctionScheme").value(hasItem(DEFAULT_CORRECTION_SCHEME)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getResponsibilityInvestigateCorrection() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateCorrection = responsibilityInvestigateCorrectionRepository.saveAndFlush(
            responsibilityInvestigateCorrection
        );

        // Get the responsibilityInvestigateCorrection
        restResponsibilityInvestigateCorrectionMockMvc
            .perform(get(ENTITY_API_URL_ID, responsibilityInvestigateCorrection.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(responsibilityInvestigateCorrection.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.name").value(DEFAULT_NAME))
            .andExpect(jsonPath("$.correctionCode").value(DEFAULT_CORRECTION_CODE))
            .andExpect(jsonPath("$.correctionType").value(DEFAULT_CORRECTION_TYPE.toString()))
            .andExpect(jsonPath("$.level").value(DEFAULT_LEVEL.toString()))
            .andExpect(jsonPath("$.investigateId").value(DEFAULT_INVESTIGATE_ID.intValue()))
            .andExpect(jsonPath("$.dutyEmployeeId").value(DEFAULT_DUTY_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.dutyEmployeeOrgId").value(DEFAULT_DUTY_EMPLOYEE_ORG_ID.intValue()))
            .andExpect(jsonPath("$.collaborationEmployeeId").value(DEFAULT_COLLABORATION_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.supervisionEmployeeId").value(DEFAULT_SUPERVISION_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.startDate").value(DEFAULT_START_DATE.toString()))
            .andExpect(jsonPath("$.finishDate").value(DEFAULT_FINISH_DATE.toString()))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.correctionBackground").value(DEFAULT_CORRECTION_BACKGROUND))
            .andExpect(jsonPath("$.correctionRequire").value(DEFAULT_CORRECTION_REQUIRE))
            .andExpect(jsonPath("$.correctionRange").value(DEFAULT_CORRECTION_RANGE))
            .andExpect(jsonPath("$.correctionScheme").value(DEFAULT_CORRECTION_SCHEME))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingResponsibilityInvestigateCorrection() throws Exception {
        // Get the responsibilityInvestigateCorrection
        restResponsibilityInvestigateCorrectionMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingResponsibilityInvestigateCorrection() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateCorrection = responsibilityInvestigateCorrectionRepository.saveAndFlush(
            responsibilityInvestigateCorrection
        );

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the responsibilityInvestigateCorrection
        ResponsibilityInvestigateCorrection updatedResponsibilityInvestigateCorrection = responsibilityInvestigateCorrectionRepository
            .findById(responsibilityInvestigateCorrection.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedResponsibilityInvestigateCorrection are not directly saved in db
        em.detach(updatedResponsibilityInvestigateCorrection);
        updatedResponsibilityInvestigateCorrection
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .correctionCode(UPDATED_CORRECTION_CODE)
            .correctionType(UPDATED_CORRECTION_TYPE)
            .level(UPDATED_LEVEL)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .dutyEmployeeId(UPDATED_DUTY_EMPLOYEE_ID)
            .dutyEmployeeOrgId(UPDATED_DUTY_EMPLOYEE_ORG_ID)
            .collaborationEmployeeId(UPDATED_COLLABORATION_EMPLOYEE_ID)
            .supervisionEmployeeId(UPDATED_SUPERVISION_EMPLOYEE_ID)
            .startDate(UPDATED_START_DATE)
            .finishDate(UPDATED_FINISH_DATE)
            .status(UPDATED_STATUS)
            .correctionBackground(UPDATED_CORRECTION_BACKGROUND)
            .correctionRequire(UPDATED_CORRECTION_REQUIRE)
            .correctionRange(UPDATED_CORRECTION_RANGE)
            .correctionScheme(UPDATED_CORRECTION_SCHEME)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ResponsibilityInvestigateCorrectionDTO responsibilityInvestigateCorrectionDTO = responsibilityInvestigateCorrectionMapper.toDto(
            updatedResponsibilityInvestigateCorrection
        );

        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, responsibilityInvestigateCorrectionDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateCorrectionDTO))
            )
            .andExpect(status().isOk());

        // Validate the ResponsibilityInvestigateCorrection in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedResponsibilityInvestigateCorrectionToMatchAllProperties(updatedResponsibilityInvestigateCorrection);
    }

    @Test
    @Transactional
    void putNonExistingResponsibilityInvestigateCorrection() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateCorrection.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateCorrection
        ResponsibilityInvestigateCorrectionDTO responsibilityInvestigateCorrectionDTO = responsibilityInvestigateCorrectionMapper.toDto(
            responsibilityInvestigateCorrection
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, responsibilityInvestigateCorrectionDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateCorrectionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ResponsibilityInvestigateCorrection in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchResponsibilityInvestigateCorrection() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateCorrection.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateCorrection
        ResponsibilityInvestigateCorrectionDTO responsibilityInvestigateCorrectionDTO = responsibilityInvestigateCorrectionMapper.toDto(
            responsibilityInvestigateCorrection
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateCorrectionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ResponsibilityInvestigateCorrection in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamResponsibilityInvestigateCorrection() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateCorrection.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateCorrection
        ResponsibilityInvestigateCorrectionDTO responsibilityInvestigateCorrectionDTO = responsibilityInvestigateCorrectionMapper.toDto(
            responsibilityInvestigateCorrection
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                put(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateCorrectionDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ResponsibilityInvestigateCorrection in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateResponsibilityInvestigateCorrectionWithPatch() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateCorrection = responsibilityInvestigateCorrectionRepository.saveAndFlush(
            responsibilityInvestigateCorrection
        );

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the responsibilityInvestigateCorrection using partial update
        ResponsibilityInvestigateCorrection partialUpdatedResponsibilityInvestigateCorrection = new ResponsibilityInvestigateCorrection();
        partialUpdatedResponsibilityInvestigateCorrection.setId(responsibilityInvestigateCorrection.getId());

        partialUpdatedResponsibilityInvestigateCorrection
            .name(UPDATED_NAME)
            .correctionCode(UPDATED_CORRECTION_CODE)
            .correctionType(UPDATED_CORRECTION_TYPE)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .dutyEmployeeId(UPDATED_DUTY_EMPLOYEE_ID)
            .dutyEmployeeOrgId(UPDATED_DUTY_EMPLOYEE_ORG_ID)
            .collaborationEmployeeId(UPDATED_COLLABORATION_EMPLOYEE_ID)
            .finishDate(UPDATED_FINISH_DATE)
            .status(UPDATED_STATUS)
            .correctionRange(UPDATED_CORRECTION_RANGE)
            .version(UPDATED_VERSION)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY);

        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedResponsibilityInvestigateCorrection.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedResponsibilityInvestigateCorrection))
            )
            .andExpect(status().isOk());

        // Validate the ResponsibilityInvestigateCorrection in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertResponsibilityInvestigateCorrectionUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedResponsibilityInvestigateCorrection, responsibilityInvestigateCorrection),
            getPersistedResponsibilityInvestigateCorrection(responsibilityInvestigateCorrection)
        );
    }

    @Test
    @Transactional
    void fullUpdateResponsibilityInvestigateCorrectionWithPatch() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateCorrection = responsibilityInvestigateCorrectionRepository.saveAndFlush(
            responsibilityInvestigateCorrection
        );

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the responsibilityInvestigateCorrection using partial update
        ResponsibilityInvestigateCorrection partialUpdatedResponsibilityInvestigateCorrection = new ResponsibilityInvestigateCorrection();
        partialUpdatedResponsibilityInvestigateCorrection.setId(responsibilityInvestigateCorrection.getId());

        partialUpdatedResponsibilityInvestigateCorrection
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .correctionCode(UPDATED_CORRECTION_CODE)
            .correctionType(UPDATED_CORRECTION_TYPE)
            .level(UPDATED_LEVEL)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .dutyEmployeeId(UPDATED_DUTY_EMPLOYEE_ID)
            .dutyEmployeeOrgId(UPDATED_DUTY_EMPLOYEE_ORG_ID)
            .collaborationEmployeeId(UPDATED_COLLABORATION_EMPLOYEE_ID)
            .supervisionEmployeeId(UPDATED_SUPERVISION_EMPLOYEE_ID)
            .startDate(UPDATED_START_DATE)
            .finishDate(UPDATED_FINISH_DATE)
            .status(UPDATED_STATUS)
            .correctionBackground(UPDATED_CORRECTION_BACKGROUND)
            .correctionRequire(UPDATED_CORRECTION_REQUIRE)
            .correctionRange(UPDATED_CORRECTION_RANGE)
            .correctionScheme(UPDATED_CORRECTION_SCHEME)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedResponsibilityInvestigateCorrection.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedResponsibilityInvestigateCorrection))
            )
            .andExpect(status().isOk());

        // Validate the ResponsibilityInvestigateCorrection in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertResponsibilityInvestigateCorrectionUpdatableFieldsEquals(
            partialUpdatedResponsibilityInvestigateCorrection,
            getPersistedResponsibilityInvestigateCorrection(partialUpdatedResponsibilityInvestigateCorrection)
        );
    }

    @Test
    @Transactional
    void patchNonExistingResponsibilityInvestigateCorrection() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateCorrection.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateCorrection
        ResponsibilityInvestigateCorrectionDTO responsibilityInvestigateCorrectionDTO = responsibilityInvestigateCorrectionMapper.toDto(
            responsibilityInvestigateCorrection
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, responsibilityInvestigateCorrectionDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(responsibilityInvestigateCorrectionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ResponsibilityInvestigateCorrection in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchResponsibilityInvestigateCorrection() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateCorrection.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateCorrection
        ResponsibilityInvestigateCorrectionDTO responsibilityInvestigateCorrectionDTO = responsibilityInvestigateCorrectionMapper.toDto(
            responsibilityInvestigateCorrection
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(responsibilityInvestigateCorrectionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ResponsibilityInvestigateCorrection in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamResponsibilityInvestigateCorrection() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateCorrection.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateCorrection
        ResponsibilityInvestigateCorrectionDTO responsibilityInvestigateCorrectionDTO = responsibilityInvestigateCorrectionMapper.toDto(
            responsibilityInvestigateCorrection
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateCorrectionMockMvc
            .perform(
                patch(ENTITY_API_URL)
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(responsibilityInvestigateCorrectionDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ResponsibilityInvestigateCorrection in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteResponsibilityInvestigateCorrection() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateCorrection = responsibilityInvestigateCorrectionRepository.saveAndFlush(
            responsibilityInvestigateCorrection
        );

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the responsibilityInvestigateCorrection
        restResponsibilityInvestigateCorrectionMockMvc
            .perform(delete(ENTITY_API_URL_ID, responsibilityInvestigateCorrection.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return responsibilityInvestigateCorrectionRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ResponsibilityInvestigateCorrection getPersistedResponsibilityInvestigateCorrection(
        ResponsibilityInvestigateCorrection responsibilityInvestigateCorrection
    ) {
        return responsibilityInvestigateCorrectionRepository.findById(responsibilityInvestigateCorrection.getId()).orElseThrow();
    }

    protected void assertPersistedResponsibilityInvestigateCorrectionToMatchAllProperties(
        ResponsibilityInvestigateCorrection expectedResponsibilityInvestigateCorrection
    ) {
        assertResponsibilityInvestigateCorrectionAllPropertiesEquals(
            expectedResponsibilityInvestigateCorrection,
            getPersistedResponsibilityInvestigateCorrection(expectedResponsibilityInvestigateCorrection)
        );
    }

    protected void assertPersistedResponsibilityInvestigateCorrectionToMatchUpdatableProperties(
        ResponsibilityInvestigateCorrection expectedResponsibilityInvestigateCorrection
    ) {
        assertResponsibilityInvestigateCorrectionAllUpdatablePropertiesEquals(
            expectedResponsibilityInvestigateCorrection,
            getPersistedResponsibilityInvestigateCorrection(expectedResponsibilityInvestigateCorrection)
        );
    }
}
