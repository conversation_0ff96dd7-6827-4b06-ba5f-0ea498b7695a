package com.whiskerguard.violation.web.rest;

import static com.whiskerguard.violation.domain.ContinuousImprovementImproveAsserts.*;
import static com.whiskerguard.violation.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.violation.IntegrationTest;
import com.whiskerguard.violation.domain.ContinuousImprovementImprove;
import com.whiskerguard.violation.domain.enumeration.ImproveSource;
import com.whiskerguard.violation.domain.enumeration.ImproveType;
import com.whiskerguard.violation.domain.enumeration.PriorityLevel;
import com.whiskerguard.violation.domain.enumeration.Status;
import com.whiskerguard.violation.repository.ContinuousImprovementImproveRepository;
import com.whiskerguard.violation.service.dto.ContinuousImprovementImproveDTO;
import com.whiskerguard.violation.service.mapper.ContinuousImprovementImproveMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ContinuousImprovementImproveResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ContinuousImprovementImproveResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_IMPROVE_NAME = "AAAAAAAAAA";
    private static final String UPDATED_IMPROVE_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_IMPROVE_CODE = "AAAAAAAAAA";
    private static final String UPDATED_IMPROVE_CODE = "BBBBBBBBBB";

    private static final ImproveType DEFAULT_IMPROVE_TYPE = ImproveType.SYSTEM_UPGRADE;
    private static final ImproveType UPDATED_IMPROVE_TYPE = ImproveType.TRAINING_EDUCATION;

    private static final ImproveSource DEFAULT_IMPROVE_SOURCE = ImproveSource.INTERNAL_AUDIT;
    private static final ImproveSource UPDATED_IMPROVE_SOURCE = ImproveSource.ANNUAL_PLAN;

    private static final String DEFAULT_SOURCE_CODE = "AAAAAAAAAA";
    private static final String UPDATED_SOURCE_CODE = "BBBBBBBBBB";

    private static final PriorityLevel DEFAULT_LEVEL = PriorityLevel.LOW;
    private static final PriorityLevel UPDATED_LEVEL = PriorityLevel.MIDDLE;

    private static final Long DEFAULT_DUTY_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_DUTY_EMPLOYEE_ID = 2L;

    private static final Long DEFAULT_DUTY_EMPLOYEE_ORG_ID = 1L;
    private static final Long UPDATED_DUTY_EMPLOYEE_ORG_ID = 2L;

    private static final Long DEFAULT_COLLABORATION_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_COLLABORATION_EMPLOYEE_ID = 2L;

    private static final LocalDate DEFAULT_START_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_START_DATE = LocalDate.now(ZoneId.systemDefault());

    private static final LocalDate DEFAULT_FINISH_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_FINISH_DATE = LocalDate.now(ZoneId.systemDefault());

    private static final Status DEFAULT_STATUS = Status.NO_START;
    private static final Status UPDATED_STATUS = Status.PROGRESSING;

    private static final String DEFAULT_IMPROVE_BACKGROUND = "AAAAAAAAAA";
    private static final String UPDATED_IMPROVE_BACKGROUND = "BBBBBBBBBB";

    private static final String DEFAULT_PROBLEM_ANALYSIS = "AAAAAAAAAA";
    private static final String UPDATED_PROBLEM_ANALYSIS = "BBBBBBBBBB";

    private static final String DEFAULT_IMPROVE_TARGET = "AAAAAAAAAA";
    private static final String UPDATED_IMPROVE_TARGET = "BBBBBBBBBB";

    private static final String DEFAULT_RELATED_REGION = "AAAAAAAAAA";
    private static final String UPDATED_RELATED_REGION = "BBBBBBBBBB";

    private static final String DEFAULT_SCHEME_SUMMARY = "AAAAAAAAAA";
    private static final String UPDATED_SCHEME_SUMMARY = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/continuous-improvement-improves";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ContinuousImprovementImproveRepository continuousImprovementImproveRepository;

    @Autowired
    private ContinuousImprovementImproveMapper continuousImprovementImproveMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restContinuousImprovementImproveMockMvc;

    private ContinuousImprovementImprove continuousImprovementImprove;

    private ContinuousImprovementImprove insertedContinuousImprovementImprove;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContinuousImprovementImprove createEntity() {
        return new ContinuousImprovementImprove()
            .tenantId(DEFAULT_TENANT_ID)
            .improveName(DEFAULT_IMPROVE_NAME)
            .improveCode(DEFAULT_IMPROVE_CODE)
            .improveType(DEFAULT_IMPROVE_TYPE)
            .improveSource(DEFAULT_IMPROVE_SOURCE)
            .sourceCode(DEFAULT_SOURCE_CODE)
            .level(DEFAULT_LEVEL)
            .dutyEmployeeId(DEFAULT_DUTY_EMPLOYEE_ID)
            .dutyEmployeeOrgId(DEFAULT_DUTY_EMPLOYEE_ORG_ID)
            .collaborationEmployeeId(DEFAULT_COLLABORATION_EMPLOYEE_ID)
            .startDate(DEFAULT_START_DATE)
            .finishDate(DEFAULT_FINISH_DATE)
            .status(DEFAULT_STATUS)
            .improveBackground(DEFAULT_IMPROVE_BACKGROUND)
            .problemAnalysis(DEFAULT_PROBLEM_ANALYSIS)
            .improveTarget(DEFAULT_IMPROVE_TARGET)
            .relatedRegion(DEFAULT_RELATED_REGION)
            .schemeSummary(DEFAULT_SCHEME_SUMMARY)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContinuousImprovementImprove createUpdatedEntity() {
        return new ContinuousImprovementImprove()
            .tenantId(UPDATED_TENANT_ID)
            .improveName(UPDATED_IMPROVE_NAME)
            .improveCode(UPDATED_IMPROVE_CODE)
            .improveType(UPDATED_IMPROVE_TYPE)
            .improveSource(UPDATED_IMPROVE_SOURCE)
            .sourceCode(UPDATED_SOURCE_CODE)
            .level(UPDATED_LEVEL)
            .dutyEmployeeId(UPDATED_DUTY_EMPLOYEE_ID)
            .dutyEmployeeOrgId(UPDATED_DUTY_EMPLOYEE_ORG_ID)
            .collaborationEmployeeId(UPDATED_COLLABORATION_EMPLOYEE_ID)
            .startDate(UPDATED_START_DATE)
            .finishDate(UPDATED_FINISH_DATE)
            .status(UPDATED_STATUS)
            .improveBackground(UPDATED_IMPROVE_BACKGROUND)
            .problemAnalysis(UPDATED_PROBLEM_ANALYSIS)
            .improveTarget(UPDATED_IMPROVE_TARGET)
            .relatedRegion(UPDATED_RELATED_REGION)
            .schemeSummary(UPDATED_SCHEME_SUMMARY)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        continuousImprovementImprove = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedContinuousImprovementImprove != null) {
            continuousImprovementImproveRepository.delete(insertedContinuousImprovementImprove);
            insertedContinuousImprovementImprove = null;
        }
    }

    @Test
    @Transactional
    void createContinuousImprovementImprove() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ContinuousImprovementImprove
        ContinuousImprovementImproveDTO continuousImprovementImproveDTO = continuousImprovementImproveMapper.toDto(
            continuousImprovementImprove
        );
        var returnedContinuousImprovementImproveDTO = om.readValue(
            restContinuousImprovementImproveMockMvc
                .perform(
                    post(ENTITY_API_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(om.writeValueAsBytes(continuousImprovementImproveDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ContinuousImprovementImproveDTO.class
        );

        // Validate the ContinuousImprovementImprove in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedContinuousImprovementImprove = continuousImprovementImproveMapper.toEntity(returnedContinuousImprovementImproveDTO);
        assertContinuousImprovementImproveUpdatableFieldsEquals(
            returnedContinuousImprovementImprove,
            getPersistedContinuousImprovementImprove(returnedContinuousImprovementImprove)
        );

        insertedContinuousImprovementImprove = returnedContinuousImprovementImprove;
    }

    @Test
    @Transactional
    void createContinuousImprovementImproveWithExistingId() throws Exception {
        // Create the ContinuousImprovementImprove with an existing ID
        continuousImprovementImprove.setId(1L);
        ContinuousImprovementImproveDTO continuousImprovementImproveDTO = continuousImprovementImproveMapper.toDto(
            continuousImprovementImprove
        );

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restContinuousImprovementImproveMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(continuousImprovementImproveDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementImprove in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementImprove.setTenantId(null);

        // Create the ContinuousImprovementImprove, which fails.
        ContinuousImprovementImproveDTO continuousImprovementImproveDTO = continuousImprovementImproveMapper.toDto(
            continuousImprovementImprove
        );

        restContinuousImprovementImproveMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(continuousImprovementImproveDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkImproveCodeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementImprove.setImproveCode(null);

        // Create the ContinuousImprovementImprove, which fails.
        ContinuousImprovementImproveDTO continuousImprovementImproveDTO = continuousImprovementImproveMapper.toDto(
            continuousImprovementImprove
        );

        restContinuousImprovementImproveMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(continuousImprovementImproveDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkSourceCodeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementImprove.setSourceCode(null);

        // Create the ContinuousImprovementImprove, which fails.
        ContinuousImprovementImproveDTO continuousImprovementImproveDTO = continuousImprovementImproveMapper.toDto(
            continuousImprovementImprove
        );

        restContinuousImprovementImproveMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(continuousImprovementImproveDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkDutyEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementImprove.setDutyEmployeeId(null);

        // Create the ContinuousImprovementImprove, which fails.
        ContinuousImprovementImproveDTO continuousImprovementImproveDTO = continuousImprovementImproveMapper.toDto(
            continuousImprovementImprove
        );

        restContinuousImprovementImproveMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(continuousImprovementImproveDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkDutyEmployeeOrgIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementImprove.setDutyEmployeeOrgId(null);

        // Create the ContinuousImprovementImprove, which fails.
        ContinuousImprovementImproveDTO continuousImprovementImproveDTO = continuousImprovementImproveMapper.toDto(
            continuousImprovementImprove
        );

        restContinuousImprovementImproveMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(continuousImprovementImproveDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCollaborationEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementImprove.setCollaborationEmployeeId(null);

        // Create the ContinuousImprovementImprove, which fails.
        ContinuousImprovementImproveDTO continuousImprovementImproveDTO = continuousImprovementImproveMapper.toDto(
            continuousImprovementImprove
        );

        restContinuousImprovementImproveMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(continuousImprovementImproveDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementImprove.setCreatedBy(null);

        // Create the ContinuousImprovementImprove, which fails.
        ContinuousImprovementImproveDTO continuousImprovementImproveDTO = continuousImprovementImproveMapper.toDto(
            continuousImprovementImprove
        );

        restContinuousImprovementImproveMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(continuousImprovementImproveDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllContinuousImprovementImproves() throws Exception {
        // Initialize the database
        insertedContinuousImprovementImprove = continuousImprovementImproveRepository.saveAndFlush(continuousImprovementImprove);

        // Get all the continuousImprovementImproveList
        restContinuousImprovementImproveMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(continuousImprovementImprove.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].improveName").value(hasItem(DEFAULT_IMPROVE_NAME)))
            .andExpect(jsonPath("$.[*].improveCode").value(hasItem(DEFAULT_IMPROVE_CODE)))
            .andExpect(jsonPath("$.[*].improveType").value(hasItem(DEFAULT_IMPROVE_TYPE.toString())))
            .andExpect(jsonPath("$.[*].improveSource").value(hasItem(DEFAULT_IMPROVE_SOURCE.toString())))
            .andExpect(jsonPath("$.[*].sourceCode").value(hasItem(DEFAULT_SOURCE_CODE)))
            .andExpect(jsonPath("$.[*].level").value(hasItem(DEFAULT_LEVEL.toString())))
            .andExpect(jsonPath("$.[*].dutyEmployeeId").value(hasItem(DEFAULT_DUTY_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].dutyEmployeeOrgId").value(hasItem(DEFAULT_DUTY_EMPLOYEE_ORG_ID.intValue())))
            .andExpect(jsonPath("$.[*].collaborationEmployeeId").value(hasItem(DEFAULT_COLLABORATION_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].startDate").value(hasItem(DEFAULT_START_DATE.toString())))
            .andExpect(jsonPath("$.[*].finishDate").value(hasItem(DEFAULT_FINISH_DATE.toString())))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].improveBackground").value(hasItem(DEFAULT_IMPROVE_BACKGROUND)))
            .andExpect(jsonPath("$.[*].problemAnalysis").value(hasItem(DEFAULT_PROBLEM_ANALYSIS)))
            .andExpect(jsonPath("$.[*].improveTarget").value(hasItem(DEFAULT_IMPROVE_TARGET)))
            .andExpect(jsonPath("$.[*].relatedRegion").value(hasItem(DEFAULT_RELATED_REGION)))
            .andExpect(jsonPath("$.[*].schemeSummary").value(hasItem(DEFAULT_SCHEME_SUMMARY)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getContinuousImprovementImprove() throws Exception {
        // Initialize the database
        insertedContinuousImprovementImprove = continuousImprovementImproveRepository.saveAndFlush(continuousImprovementImprove);

        // Get the continuousImprovementImprove
        restContinuousImprovementImproveMockMvc
            .perform(get(ENTITY_API_URL_ID, continuousImprovementImprove.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(continuousImprovementImprove.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.improveName").value(DEFAULT_IMPROVE_NAME))
            .andExpect(jsonPath("$.improveCode").value(DEFAULT_IMPROVE_CODE))
            .andExpect(jsonPath("$.improveType").value(DEFAULT_IMPROVE_TYPE.toString()))
            .andExpect(jsonPath("$.improveSource").value(DEFAULT_IMPROVE_SOURCE.toString()))
            .andExpect(jsonPath("$.sourceCode").value(DEFAULT_SOURCE_CODE))
            .andExpect(jsonPath("$.level").value(DEFAULT_LEVEL.toString()))
            .andExpect(jsonPath("$.dutyEmployeeId").value(DEFAULT_DUTY_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.dutyEmployeeOrgId").value(DEFAULT_DUTY_EMPLOYEE_ORG_ID.intValue()))
            .andExpect(jsonPath("$.collaborationEmployeeId").value(DEFAULT_COLLABORATION_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.startDate").value(DEFAULT_START_DATE.toString()))
            .andExpect(jsonPath("$.finishDate").value(DEFAULT_FINISH_DATE.toString()))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.improveBackground").value(DEFAULT_IMPROVE_BACKGROUND))
            .andExpect(jsonPath("$.problemAnalysis").value(DEFAULT_PROBLEM_ANALYSIS))
            .andExpect(jsonPath("$.improveTarget").value(DEFAULT_IMPROVE_TARGET))
            .andExpect(jsonPath("$.relatedRegion").value(DEFAULT_RELATED_REGION))
            .andExpect(jsonPath("$.schemeSummary").value(DEFAULT_SCHEME_SUMMARY))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingContinuousImprovementImprove() throws Exception {
        // Get the continuousImprovementImprove
        restContinuousImprovementImproveMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingContinuousImprovementImprove() throws Exception {
        // Initialize the database
        insertedContinuousImprovementImprove = continuousImprovementImproveRepository.saveAndFlush(continuousImprovementImprove);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the continuousImprovementImprove
        ContinuousImprovementImprove updatedContinuousImprovementImprove = continuousImprovementImproveRepository
            .findById(continuousImprovementImprove.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedContinuousImprovementImprove are not directly saved in db
        em.detach(updatedContinuousImprovementImprove);
        updatedContinuousImprovementImprove
            .tenantId(UPDATED_TENANT_ID)
            .improveName(UPDATED_IMPROVE_NAME)
            .improveCode(UPDATED_IMPROVE_CODE)
            .improveType(UPDATED_IMPROVE_TYPE)
            .improveSource(UPDATED_IMPROVE_SOURCE)
            .sourceCode(UPDATED_SOURCE_CODE)
            .level(UPDATED_LEVEL)
            .dutyEmployeeId(UPDATED_DUTY_EMPLOYEE_ID)
            .dutyEmployeeOrgId(UPDATED_DUTY_EMPLOYEE_ORG_ID)
            .collaborationEmployeeId(UPDATED_COLLABORATION_EMPLOYEE_ID)
            .startDate(UPDATED_START_DATE)
            .finishDate(UPDATED_FINISH_DATE)
            .status(UPDATED_STATUS)
            .improveBackground(UPDATED_IMPROVE_BACKGROUND)
            .problemAnalysis(UPDATED_PROBLEM_ANALYSIS)
            .improveTarget(UPDATED_IMPROVE_TARGET)
            .relatedRegion(UPDATED_RELATED_REGION)
            .schemeSummary(UPDATED_SCHEME_SUMMARY)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ContinuousImprovementImproveDTO continuousImprovementImproveDTO = continuousImprovementImproveMapper.toDto(
            updatedContinuousImprovementImprove
        );

        restContinuousImprovementImproveMockMvc
            .perform(
                put(ENTITY_API_URL_ID, continuousImprovementImproveDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementImproveDTO))
            )
            .andExpect(status().isOk());

        // Validate the ContinuousImprovementImprove in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedContinuousImprovementImproveToMatchAllProperties(updatedContinuousImprovementImprove);
    }

    @Test
    @Transactional
    void putNonExistingContinuousImprovementImprove() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementImprove.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementImprove
        ContinuousImprovementImproveDTO continuousImprovementImproveDTO = continuousImprovementImproveMapper.toDto(
            continuousImprovementImprove
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContinuousImprovementImproveMockMvc
            .perform(
                put(ENTITY_API_URL_ID, continuousImprovementImproveDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementImproveDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementImprove in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchContinuousImprovementImprove() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementImprove.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementImprove
        ContinuousImprovementImproveDTO continuousImprovementImproveDTO = continuousImprovementImproveMapper.toDto(
            continuousImprovementImprove
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementImproveMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementImproveDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementImprove in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamContinuousImprovementImprove() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementImprove.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementImprove
        ContinuousImprovementImproveDTO continuousImprovementImproveDTO = continuousImprovementImproveMapper.toDto(
            continuousImprovementImprove
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementImproveMockMvc
            .perform(
                put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(continuousImprovementImproveDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContinuousImprovementImprove in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateContinuousImprovementImproveWithPatch() throws Exception {
        // Initialize the database
        insertedContinuousImprovementImprove = continuousImprovementImproveRepository.saveAndFlush(continuousImprovementImprove);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the continuousImprovementImprove using partial update
        ContinuousImprovementImprove partialUpdatedContinuousImprovementImprove = new ContinuousImprovementImprove();
        partialUpdatedContinuousImprovementImprove.setId(continuousImprovementImprove.getId());

        partialUpdatedContinuousImprovementImprove
            .tenantId(UPDATED_TENANT_ID)
            .improveName(UPDATED_IMPROVE_NAME)
            .dutyEmployeeId(UPDATED_DUTY_EMPLOYEE_ID)
            .startDate(UPDATED_START_DATE)
            .status(UPDATED_STATUS)
            .improveBackground(UPDATED_IMPROVE_BACKGROUND)
            .problemAnalysis(UPDATED_PROBLEM_ANALYSIS)
            .improveTarget(UPDATED_IMPROVE_TARGET)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .updatedAt(UPDATED_UPDATED_AT);

        restContinuousImprovementImproveMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContinuousImprovementImprove.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContinuousImprovementImprove))
            )
            .andExpect(status().isOk());

        // Validate the ContinuousImprovementImprove in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContinuousImprovementImproveUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedContinuousImprovementImprove, continuousImprovementImprove),
            getPersistedContinuousImprovementImprove(continuousImprovementImprove)
        );
    }

    @Test
    @Transactional
    void fullUpdateContinuousImprovementImproveWithPatch() throws Exception {
        // Initialize the database
        insertedContinuousImprovementImprove = continuousImprovementImproveRepository.saveAndFlush(continuousImprovementImprove);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the continuousImprovementImprove using partial update
        ContinuousImprovementImprove partialUpdatedContinuousImprovementImprove = new ContinuousImprovementImprove();
        partialUpdatedContinuousImprovementImprove.setId(continuousImprovementImprove.getId());

        partialUpdatedContinuousImprovementImprove
            .tenantId(UPDATED_TENANT_ID)
            .improveName(UPDATED_IMPROVE_NAME)
            .improveCode(UPDATED_IMPROVE_CODE)
            .improveType(UPDATED_IMPROVE_TYPE)
            .improveSource(UPDATED_IMPROVE_SOURCE)
            .sourceCode(UPDATED_SOURCE_CODE)
            .level(UPDATED_LEVEL)
            .dutyEmployeeId(UPDATED_DUTY_EMPLOYEE_ID)
            .dutyEmployeeOrgId(UPDATED_DUTY_EMPLOYEE_ORG_ID)
            .collaborationEmployeeId(UPDATED_COLLABORATION_EMPLOYEE_ID)
            .startDate(UPDATED_START_DATE)
            .finishDate(UPDATED_FINISH_DATE)
            .status(UPDATED_STATUS)
            .improveBackground(UPDATED_IMPROVE_BACKGROUND)
            .problemAnalysis(UPDATED_PROBLEM_ANALYSIS)
            .improveTarget(UPDATED_IMPROVE_TARGET)
            .relatedRegion(UPDATED_RELATED_REGION)
            .schemeSummary(UPDATED_SCHEME_SUMMARY)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restContinuousImprovementImproveMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContinuousImprovementImprove.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContinuousImprovementImprove))
            )
            .andExpect(status().isOk());

        // Validate the ContinuousImprovementImprove in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContinuousImprovementImproveUpdatableFieldsEquals(
            partialUpdatedContinuousImprovementImprove,
            getPersistedContinuousImprovementImprove(partialUpdatedContinuousImprovementImprove)
        );
    }

    @Test
    @Transactional
    void patchNonExistingContinuousImprovementImprove() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementImprove.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementImprove
        ContinuousImprovementImproveDTO continuousImprovementImproveDTO = continuousImprovementImproveMapper.toDto(
            continuousImprovementImprove
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContinuousImprovementImproveMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, continuousImprovementImproveDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(continuousImprovementImproveDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementImprove in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchContinuousImprovementImprove() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementImprove.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementImprove
        ContinuousImprovementImproveDTO continuousImprovementImproveDTO = continuousImprovementImproveMapper.toDto(
            continuousImprovementImprove
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementImproveMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(continuousImprovementImproveDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementImprove in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamContinuousImprovementImprove() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementImprove.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementImprove
        ContinuousImprovementImproveDTO continuousImprovementImproveDTO = continuousImprovementImproveMapper.toDto(
            continuousImprovementImprove
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementImproveMockMvc
            .perform(
                patch(ENTITY_API_URL)
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(continuousImprovementImproveDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContinuousImprovementImprove in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteContinuousImprovementImprove() throws Exception {
        // Initialize the database
        insertedContinuousImprovementImprove = continuousImprovementImproveRepository.saveAndFlush(continuousImprovementImprove);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the continuousImprovementImprove
        restContinuousImprovementImproveMockMvc
            .perform(delete(ENTITY_API_URL_ID, continuousImprovementImprove.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return continuousImprovementImproveRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ContinuousImprovementImprove getPersistedContinuousImprovementImprove(
        ContinuousImprovementImprove continuousImprovementImprove
    ) {
        return continuousImprovementImproveRepository.findById(continuousImprovementImprove.getId()).orElseThrow();
    }

    protected void assertPersistedContinuousImprovementImproveToMatchAllProperties(
        ContinuousImprovementImprove expectedContinuousImprovementImprove
    ) {
        assertContinuousImprovementImproveAllPropertiesEquals(
            expectedContinuousImprovementImprove,
            getPersistedContinuousImprovementImprove(expectedContinuousImprovementImprove)
        );
    }

    protected void assertPersistedContinuousImprovementImproveToMatchUpdatableProperties(
        ContinuousImprovementImprove expectedContinuousImprovementImprove
    ) {
        assertContinuousImprovementImproveAllUpdatablePropertiesEquals(
            expectedContinuousImprovementImprove,
            getPersistedContinuousImprovementImprove(expectedContinuousImprovementImprove)
        );
    }
}
