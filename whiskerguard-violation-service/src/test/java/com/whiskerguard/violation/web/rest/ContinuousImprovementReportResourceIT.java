package com.whiskerguard.violation.web.rest;

import static com.whiskerguard.violation.domain.ContinuousImprovementReportAsserts.*;
import static com.whiskerguard.violation.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.violation.IntegrationTest;
import com.whiskerguard.violation.domain.ContinuousImprovementReport;
import com.whiskerguard.violation.domain.enumeration.ReportType;
import com.whiskerguard.violation.domain.enumeration.SecurityLevel;
import com.whiskerguard.violation.domain.enumeration.Status;
import com.whiskerguard.violation.repository.ContinuousImprovementReportRepository;
import com.whiskerguard.violation.service.dto.ContinuousImprovementReportDTO;
import com.whiskerguard.violation.service.mapper.ContinuousImprovementReportMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ContinuousImprovementReportResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ContinuousImprovementReportResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_TITLE = "AAAAAAAAAA";
    private static final String UPDATED_TITLE = "BBBBBBBBBB";

    private static final String DEFAULT_REPORT_CODE = "AAAAAAAAAA";
    private static final String UPDATED_REPORT_CODE = "BBBBBBBBBB";

    private static final ReportType DEFAULT_REPORT_TYPE = ReportType.SYSTEM_UPGRADE;
    private static final ReportType UPDATED_REPORT_TYPE = ReportType.TRAINING_EDUCATION;

    private static final LocalDate DEFAULT_REPORT_CYCLE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_REPORT_CYCLE = LocalDate.now(ZoneId.systemDefault());

    private static final Instant DEFAULT_START_DATE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_START_DATE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_END_DATE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_END_DATE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Long DEFAULT_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_EMPLOYEE_ID = 2L;

    private static final Long DEFAULT_ORG_ID = 1L;
    private static final Long UPDATED_ORG_ID = 2L;

    private static final LocalDate DEFAULT_ESTABLISH_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_ESTABLISH_DATE = LocalDate.now(ZoneId.systemDefault());

    private static final SecurityLevel DEFAULT_LEVEL = SecurityLevel.ORDINARY;
    private static final SecurityLevel UPDATED_LEVEL = SecurityLevel.INTERNAL;

    private static final String DEFAULT_SUMMARY = "AAAAAAAAAA";
    private static final String UPDATED_SUMMARY = "BBBBBBBBBB";

    private static final String DEFAULT_IMPROVE_SUMMARY = "AAAAAAAAAA";
    private static final String UPDATED_IMPROVE_SUMMARY = "BBBBBBBBBB";

    private static final String DEFAULT_IMPROVE_INVEST = "AAAAAAAAAA";
    private static final String UPDATED_IMPROVE_INVEST = "BBBBBBBBBB";

    private static final String DEFAULT_IMPROVE_PROCESS = "AAAAAAAAAA";
    private static final String UPDATED_IMPROVE_PROCESS = "BBBBBBBBBB";

    private static final String DEFAULT_IMPROVE_ACHIEVEMENT = "AAAAAAAAAA";
    private static final String UPDATED_IMPROVE_ACHIEVEMENT = "BBBBBBBBBB";

    private static final Status DEFAULT_STATUS = Status.NO_START;
    private static final Status UPDATED_STATUS = Status.PROGRESSING;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/continuous-improvement-reports";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ContinuousImprovementReportRepository continuousImprovementReportRepository;

    @Autowired
    private ContinuousImprovementReportMapper continuousImprovementReportMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restContinuousImprovementReportMockMvc;

    private ContinuousImprovementReport continuousImprovementReport;

    private ContinuousImprovementReport insertedContinuousImprovementReport;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContinuousImprovementReport createEntity() {
        return new ContinuousImprovementReport()
            .tenantId(DEFAULT_TENANT_ID)
            .title(DEFAULT_TITLE)
            .reportCode(DEFAULT_REPORT_CODE)
            .reportType(DEFAULT_REPORT_TYPE)
            .reportCycle(DEFAULT_REPORT_CYCLE)
            .startDate(DEFAULT_START_DATE)
            .endDate(DEFAULT_END_DATE)
            .employeeId(DEFAULT_EMPLOYEE_ID)
            .orgId(DEFAULT_ORG_ID)
            .establishDate(DEFAULT_ESTABLISH_DATE)
            .level(DEFAULT_LEVEL)
            .summary(DEFAULT_SUMMARY)
            .improveSummary(DEFAULT_IMPROVE_SUMMARY)
            .improveInvest(DEFAULT_IMPROVE_INVEST)
            .improveProcess(DEFAULT_IMPROVE_PROCESS)
            .improveAchievement(DEFAULT_IMPROVE_ACHIEVEMENT)
            .status(DEFAULT_STATUS)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContinuousImprovementReport createUpdatedEntity() {
        return new ContinuousImprovementReport()
            .tenantId(UPDATED_TENANT_ID)
            .title(UPDATED_TITLE)
            .reportCode(UPDATED_REPORT_CODE)
            .reportType(UPDATED_REPORT_TYPE)
            .reportCycle(UPDATED_REPORT_CYCLE)
            .startDate(UPDATED_START_DATE)
            .endDate(UPDATED_END_DATE)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .orgId(UPDATED_ORG_ID)
            .establishDate(UPDATED_ESTABLISH_DATE)
            .level(UPDATED_LEVEL)
            .summary(UPDATED_SUMMARY)
            .improveSummary(UPDATED_IMPROVE_SUMMARY)
            .improveInvest(UPDATED_IMPROVE_INVEST)
            .improveProcess(UPDATED_IMPROVE_PROCESS)
            .improveAchievement(UPDATED_IMPROVE_ACHIEVEMENT)
            .status(UPDATED_STATUS)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        continuousImprovementReport = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedContinuousImprovementReport != null) {
            continuousImprovementReportRepository.delete(insertedContinuousImprovementReport);
            insertedContinuousImprovementReport = null;
        }
    }

    @Test
    @Transactional
    void createContinuousImprovementReport() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ContinuousImprovementReport
        ContinuousImprovementReportDTO continuousImprovementReportDTO = continuousImprovementReportMapper.toDto(
            continuousImprovementReport
        );
        var returnedContinuousImprovementReportDTO = om.readValue(
            restContinuousImprovementReportMockMvc
                .perform(
                    post(ENTITY_API_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(om.writeValueAsBytes(continuousImprovementReportDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ContinuousImprovementReportDTO.class
        );

        // Validate the ContinuousImprovementReport in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedContinuousImprovementReport = continuousImprovementReportMapper.toEntity(returnedContinuousImprovementReportDTO);
        assertContinuousImprovementReportUpdatableFieldsEquals(
            returnedContinuousImprovementReport,
            getPersistedContinuousImprovementReport(returnedContinuousImprovementReport)
        );

        insertedContinuousImprovementReport = returnedContinuousImprovementReport;
    }

    @Test
    @Transactional
    void createContinuousImprovementReportWithExistingId() throws Exception {
        // Create the ContinuousImprovementReport with an existing ID
        continuousImprovementReport.setId(1L);
        ContinuousImprovementReportDTO continuousImprovementReportDTO = continuousImprovementReportMapper.toDto(
            continuousImprovementReport
        );

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restContinuousImprovementReportMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(continuousImprovementReportDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementReport in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementReport.setTenantId(null);

        // Create the ContinuousImprovementReport, which fails.
        ContinuousImprovementReportDTO continuousImprovementReportDTO = continuousImprovementReportMapper.toDto(
            continuousImprovementReport
        );

        restContinuousImprovementReportMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(continuousImprovementReportDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkReportCodeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementReport.setReportCode(null);

        // Create the ContinuousImprovementReport, which fails.
        ContinuousImprovementReportDTO continuousImprovementReportDTO = continuousImprovementReportMapper.toDto(
            continuousImprovementReport
        );

        restContinuousImprovementReportMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(continuousImprovementReportDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementReport.setEmployeeId(null);

        // Create the ContinuousImprovementReport, which fails.
        ContinuousImprovementReportDTO continuousImprovementReportDTO = continuousImprovementReportMapper.toDto(
            continuousImprovementReport
        );

        restContinuousImprovementReportMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(continuousImprovementReportDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkOrgIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementReport.setOrgId(null);

        // Create the ContinuousImprovementReport, which fails.
        ContinuousImprovementReportDTO continuousImprovementReportDTO = continuousImprovementReportMapper.toDto(
            continuousImprovementReport
        );

        restContinuousImprovementReportMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(continuousImprovementReportDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementReport.setCreatedBy(null);

        // Create the ContinuousImprovementReport, which fails.
        ContinuousImprovementReportDTO continuousImprovementReportDTO = continuousImprovementReportMapper.toDto(
            continuousImprovementReport
        );

        restContinuousImprovementReportMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(continuousImprovementReportDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllContinuousImprovementReports() throws Exception {
        // Initialize the database
        insertedContinuousImprovementReport = continuousImprovementReportRepository.saveAndFlush(continuousImprovementReport);

        // Get all the continuousImprovementReportList
        restContinuousImprovementReportMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(continuousImprovementReport.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].title").value(hasItem(DEFAULT_TITLE)))
            .andExpect(jsonPath("$.[*].reportCode").value(hasItem(DEFAULT_REPORT_CODE)))
            .andExpect(jsonPath("$.[*].reportType").value(hasItem(DEFAULT_REPORT_TYPE.toString())))
            .andExpect(jsonPath("$.[*].reportCycle").value(hasItem(DEFAULT_REPORT_CYCLE.toString())))
            .andExpect(jsonPath("$.[*].startDate").value(hasItem(DEFAULT_START_DATE.toString())))
            .andExpect(jsonPath("$.[*].endDate").value(hasItem(DEFAULT_END_DATE.toString())))
            .andExpect(jsonPath("$.[*].employeeId").value(hasItem(DEFAULT_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].orgId").value(hasItem(DEFAULT_ORG_ID.intValue())))
            .andExpect(jsonPath("$.[*].establishDate").value(hasItem(DEFAULT_ESTABLISH_DATE.toString())))
            .andExpect(jsonPath("$.[*].level").value(hasItem(DEFAULT_LEVEL.toString())))
            .andExpect(jsonPath("$.[*].summary").value(hasItem(DEFAULT_SUMMARY)))
            .andExpect(jsonPath("$.[*].improveSummary").value(hasItem(DEFAULT_IMPROVE_SUMMARY)))
            .andExpect(jsonPath("$.[*].improveInvest").value(hasItem(DEFAULT_IMPROVE_INVEST)))
            .andExpect(jsonPath("$.[*].improveProcess").value(hasItem(DEFAULT_IMPROVE_PROCESS)))
            .andExpect(jsonPath("$.[*].improveAchievement").value(hasItem(DEFAULT_IMPROVE_ACHIEVEMENT)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getContinuousImprovementReport() throws Exception {
        // Initialize the database
        insertedContinuousImprovementReport = continuousImprovementReportRepository.saveAndFlush(continuousImprovementReport);

        // Get the continuousImprovementReport
        restContinuousImprovementReportMockMvc
            .perform(get(ENTITY_API_URL_ID, continuousImprovementReport.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(continuousImprovementReport.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.title").value(DEFAULT_TITLE))
            .andExpect(jsonPath("$.reportCode").value(DEFAULT_REPORT_CODE))
            .andExpect(jsonPath("$.reportType").value(DEFAULT_REPORT_TYPE.toString()))
            .andExpect(jsonPath("$.reportCycle").value(DEFAULT_REPORT_CYCLE.toString()))
            .andExpect(jsonPath("$.startDate").value(DEFAULT_START_DATE.toString()))
            .andExpect(jsonPath("$.endDate").value(DEFAULT_END_DATE.toString()))
            .andExpect(jsonPath("$.employeeId").value(DEFAULT_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.orgId").value(DEFAULT_ORG_ID.intValue()))
            .andExpect(jsonPath("$.establishDate").value(DEFAULT_ESTABLISH_DATE.toString()))
            .andExpect(jsonPath("$.level").value(DEFAULT_LEVEL.toString()))
            .andExpect(jsonPath("$.summary").value(DEFAULT_SUMMARY))
            .andExpect(jsonPath("$.improveSummary").value(DEFAULT_IMPROVE_SUMMARY))
            .andExpect(jsonPath("$.improveInvest").value(DEFAULT_IMPROVE_INVEST))
            .andExpect(jsonPath("$.improveProcess").value(DEFAULT_IMPROVE_PROCESS))
            .andExpect(jsonPath("$.improveAchievement").value(DEFAULT_IMPROVE_ACHIEVEMENT))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingContinuousImprovementReport() throws Exception {
        // Get the continuousImprovementReport
        restContinuousImprovementReportMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingContinuousImprovementReport() throws Exception {
        // Initialize the database
        insertedContinuousImprovementReport = continuousImprovementReportRepository.saveAndFlush(continuousImprovementReport);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the continuousImprovementReport
        ContinuousImprovementReport updatedContinuousImprovementReport = continuousImprovementReportRepository
            .findById(continuousImprovementReport.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedContinuousImprovementReport are not directly saved in db
        em.detach(updatedContinuousImprovementReport);
        updatedContinuousImprovementReport
            .tenantId(UPDATED_TENANT_ID)
            .title(UPDATED_TITLE)
            .reportCode(UPDATED_REPORT_CODE)
            .reportType(UPDATED_REPORT_TYPE)
            .reportCycle(UPDATED_REPORT_CYCLE)
            .startDate(UPDATED_START_DATE)
            .endDate(UPDATED_END_DATE)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .orgId(UPDATED_ORG_ID)
            .establishDate(UPDATED_ESTABLISH_DATE)
            .level(UPDATED_LEVEL)
            .summary(UPDATED_SUMMARY)
            .improveSummary(UPDATED_IMPROVE_SUMMARY)
            .improveInvest(UPDATED_IMPROVE_INVEST)
            .improveProcess(UPDATED_IMPROVE_PROCESS)
            .improveAchievement(UPDATED_IMPROVE_ACHIEVEMENT)
            .status(UPDATED_STATUS)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ContinuousImprovementReportDTO continuousImprovementReportDTO = continuousImprovementReportMapper.toDto(
            updatedContinuousImprovementReport
        );

        restContinuousImprovementReportMockMvc
            .perform(
                put(ENTITY_API_URL_ID, continuousImprovementReportDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementReportDTO))
            )
            .andExpect(status().isOk());

        // Validate the ContinuousImprovementReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedContinuousImprovementReportToMatchAllProperties(updatedContinuousImprovementReport);
    }

    @Test
    @Transactional
    void putNonExistingContinuousImprovementReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementReport.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementReport
        ContinuousImprovementReportDTO continuousImprovementReportDTO = continuousImprovementReportMapper.toDto(
            continuousImprovementReport
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContinuousImprovementReportMockMvc
            .perform(
                put(ENTITY_API_URL_ID, continuousImprovementReportDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementReportDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchContinuousImprovementReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementReport.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementReport
        ContinuousImprovementReportDTO continuousImprovementReportDTO = continuousImprovementReportMapper.toDto(
            continuousImprovementReport
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementReportMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementReportDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamContinuousImprovementReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementReport.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementReport
        ContinuousImprovementReportDTO continuousImprovementReportDTO = continuousImprovementReportMapper.toDto(
            continuousImprovementReport
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementReportMockMvc
            .perform(
                put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(continuousImprovementReportDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContinuousImprovementReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateContinuousImprovementReportWithPatch() throws Exception {
        // Initialize the database
        insertedContinuousImprovementReport = continuousImprovementReportRepository.saveAndFlush(continuousImprovementReport);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the continuousImprovementReport using partial update
        ContinuousImprovementReport partialUpdatedContinuousImprovementReport = new ContinuousImprovementReport();
        partialUpdatedContinuousImprovementReport.setId(continuousImprovementReport.getId());

        partialUpdatedContinuousImprovementReport
            .title(UPDATED_TITLE)
            .reportCycle(UPDATED_REPORT_CYCLE)
            .improveSummary(UPDATED_IMPROVE_SUMMARY)
            .improveInvest(UPDATED_IMPROVE_INVEST)
            .improveAchievement(UPDATED_IMPROVE_ACHIEVEMENT)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY)
            .updatedAt(UPDATED_UPDATED_AT);

        restContinuousImprovementReportMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContinuousImprovementReport.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContinuousImprovementReport))
            )
            .andExpect(status().isOk());

        // Validate the ContinuousImprovementReport in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContinuousImprovementReportUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedContinuousImprovementReport, continuousImprovementReport),
            getPersistedContinuousImprovementReport(continuousImprovementReport)
        );
    }

    @Test
    @Transactional
    void fullUpdateContinuousImprovementReportWithPatch() throws Exception {
        // Initialize the database
        insertedContinuousImprovementReport = continuousImprovementReportRepository.saveAndFlush(continuousImprovementReport);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the continuousImprovementReport using partial update
        ContinuousImprovementReport partialUpdatedContinuousImprovementReport = new ContinuousImprovementReport();
        partialUpdatedContinuousImprovementReport.setId(continuousImprovementReport.getId());

        partialUpdatedContinuousImprovementReport
            .tenantId(UPDATED_TENANT_ID)
            .title(UPDATED_TITLE)
            .reportCode(UPDATED_REPORT_CODE)
            .reportType(UPDATED_REPORT_TYPE)
            .reportCycle(UPDATED_REPORT_CYCLE)
            .startDate(UPDATED_START_DATE)
            .endDate(UPDATED_END_DATE)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .orgId(UPDATED_ORG_ID)
            .establishDate(UPDATED_ESTABLISH_DATE)
            .level(UPDATED_LEVEL)
            .summary(UPDATED_SUMMARY)
            .improveSummary(UPDATED_IMPROVE_SUMMARY)
            .improveInvest(UPDATED_IMPROVE_INVEST)
            .improveProcess(UPDATED_IMPROVE_PROCESS)
            .improveAchievement(UPDATED_IMPROVE_ACHIEVEMENT)
            .status(UPDATED_STATUS)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restContinuousImprovementReportMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContinuousImprovementReport.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContinuousImprovementReport))
            )
            .andExpect(status().isOk());

        // Validate the ContinuousImprovementReport in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContinuousImprovementReportUpdatableFieldsEquals(
            partialUpdatedContinuousImprovementReport,
            getPersistedContinuousImprovementReport(partialUpdatedContinuousImprovementReport)
        );
    }

    @Test
    @Transactional
    void patchNonExistingContinuousImprovementReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementReport.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementReport
        ContinuousImprovementReportDTO continuousImprovementReportDTO = continuousImprovementReportMapper.toDto(
            continuousImprovementReport
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContinuousImprovementReportMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, continuousImprovementReportDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(continuousImprovementReportDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchContinuousImprovementReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementReport.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementReport
        ContinuousImprovementReportDTO continuousImprovementReportDTO = continuousImprovementReportMapper.toDto(
            continuousImprovementReport
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementReportMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(continuousImprovementReportDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamContinuousImprovementReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementReport.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementReport
        ContinuousImprovementReportDTO continuousImprovementReportDTO = continuousImprovementReportMapper.toDto(
            continuousImprovementReport
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementReportMockMvc
            .perform(
                patch(ENTITY_API_URL)
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(continuousImprovementReportDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContinuousImprovementReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteContinuousImprovementReport() throws Exception {
        // Initialize the database
        insertedContinuousImprovementReport = continuousImprovementReportRepository.saveAndFlush(continuousImprovementReport);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the continuousImprovementReport
        restContinuousImprovementReportMockMvc
            .perform(delete(ENTITY_API_URL_ID, continuousImprovementReport.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return continuousImprovementReportRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ContinuousImprovementReport getPersistedContinuousImprovementReport(ContinuousImprovementReport continuousImprovementReport) {
        return continuousImprovementReportRepository.findById(continuousImprovementReport.getId()).orElseThrow();
    }

    protected void assertPersistedContinuousImprovementReportToMatchAllProperties(
        ContinuousImprovementReport expectedContinuousImprovementReport
    ) {
        assertContinuousImprovementReportAllPropertiesEquals(
            expectedContinuousImprovementReport,
            getPersistedContinuousImprovementReport(expectedContinuousImprovementReport)
        );
    }

    protected void assertPersistedContinuousImprovementReportToMatchUpdatableProperties(
        ContinuousImprovementReport expectedContinuousImprovementReport
    ) {
        assertContinuousImprovementReportAllUpdatablePropertiesEquals(
            expectedContinuousImprovementReport,
            getPersistedContinuousImprovementReport(expectedContinuousImprovementReport)
        );
    }
}
