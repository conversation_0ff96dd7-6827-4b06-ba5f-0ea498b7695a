package com.whiskerguard.violation.web.rest;

import static com.whiskerguard.violation.domain.ProblemInvestigateReportAsserts.*;
import static com.whiskerguard.violation.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.violation.IntegrationTest;
import com.whiskerguard.violation.domain.ProblemInvestigateReport;
import com.whiskerguard.violation.domain.enumeration.InvestigateReportType;
import com.whiskerguard.violation.domain.enumeration.InvestigateSource;
import com.whiskerguard.violation.domain.enumeration.PriorityLevel;
import com.whiskerguard.violation.domain.enumeration.ReportStatus;
import com.whiskerguard.violation.repository.ProblemInvestigateReportRepository;
import com.whiskerguard.violation.service.dto.ProblemInvestigateReportDTO;
import com.whiskerguard.violation.service.mapper.ProblemInvestigateReportMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ProblemInvestigateReportResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ProblemInvestigateReportResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_TITLE = "AAAAAAAAAA";
    private static final String UPDATED_TITLE = "BBBBBBBBBB";

    private static final String DEFAULT_REPORT_CODE = "AAAAAAAAAA";
    private static final String UPDATED_REPORT_CODE = "BBBBBBBBBB";

    private static final Long DEFAULT_INVESTIGATE_ID = 1L;
    private static final Long UPDATED_INVESTIGATE_ID = 2L;

    private static final InvestigateReportType DEFAULT_REPORT_TYPE = InvestigateReportType.SPECIAL_INVESTIGE;
    private static final InvestigateReportType UPDATED_REPORT_TYPE = InvestigateReportType.SPECIAL_INVESTIGE;

    private static final InvestigateSource DEFAULT_INVESTIGATE_SOURCE = InvestigateSource.MARKETING;
    private static final InvestigateSource UPDATED_INVESTIGATE_SOURCE = InvestigateSource.PROCUREMENT;

    private static final Long DEFAULT_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_EMPLOYEE_ID = 2L;

    private static final Long DEFAULT_ORG_ID = 1L;
    private static final Long UPDATED_ORG_ID = 2L;

    private static final LocalDate DEFAULT_ESTABLISH_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_ESTABLISH_DATE = LocalDate.now(ZoneId.systemDefault());

    private static final PriorityLevel DEFAULT_LEVEL = PriorityLevel.LOW;
    private static final PriorityLevel UPDATED_LEVEL = PriorityLevel.MIDDLE;

    private static final String DEFAULT_SUMMARY = "AAAAAAAAAA";
    private static final String UPDATED_SUMMARY = "BBBBBBBBBB";

    private static final ReportStatus DEFAULT_STATUS = ReportStatus.MODIFY;
    private static final ReportStatus UPDATED_STATUS = ReportStatus.PENDING;

    private static final String DEFAULT_INVESTIGATE_BACKGROUND = "AAAAAAAAAA";
    private static final String UPDATED_INVESTIGATE_BACKGROUND = "BBBBBBBBBB";

    private static final String DEFAULT_INVESTIGATE_METHOD = "AAAAAAAAAA";
    private static final String UPDATED_INVESTIGATE_METHOD = "BBBBBBBBBB";

    private static final String DEFAULT_INVESTIGATE_PROCESS = "AAAAAAAAAA";
    private static final String UPDATED_INVESTIGATE_PROCESS = "BBBBBBBBBB";

    private static final String DEFAULT_INVESTIGATE_FOUND = "AAAAAAAAAA";
    private static final String UPDATED_INVESTIGATE_FOUND = "BBBBBBBBBB";

    private static final String DEFAULT_INVESTIGATE_CONCLUSION = "AAAAAAAAAA";
    private static final String UPDATED_INVESTIGATE_CONCLUSION = "BBBBBBBBBB";

    private static final String DEFAULT_RECOMMEND_MEASURE = "AAAAAAAAAA";
    private static final String UPDATED_RECOMMEND_MEASURE = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/problem-investigate-reports";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ProblemInvestigateReportRepository problemInvestigateReportRepository;

    @Autowired
    private ProblemInvestigateReportMapper problemInvestigateReportMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restProblemInvestigateReportMockMvc;

    private ProblemInvestigateReport problemInvestigateReport;

    private ProblemInvestigateReport insertedProblemInvestigateReport;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ProblemInvestigateReport createEntity() {
        return new ProblemInvestigateReport()
            .tenantId(DEFAULT_TENANT_ID)
            .title(DEFAULT_TITLE)
            .reportCode(DEFAULT_REPORT_CODE)
            .investigateId(DEFAULT_INVESTIGATE_ID)
            .reportType(DEFAULT_REPORT_TYPE)
            .investigateSource(DEFAULT_INVESTIGATE_SOURCE)
            .employeeId(DEFAULT_EMPLOYEE_ID)
            .orgId(DEFAULT_ORG_ID)
            .establishDate(DEFAULT_ESTABLISH_DATE)
            .level(DEFAULT_LEVEL)
            .summary(DEFAULT_SUMMARY)
            .status(DEFAULT_STATUS)
            .investigateBackground(DEFAULT_INVESTIGATE_BACKGROUND)
            .investigateMethod(DEFAULT_INVESTIGATE_METHOD)
            .investigateProcess(DEFAULT_INVESTIGATE_PROCESS)
            .investigateFound(DEFAULT_INVESTIGATE_FOUND)
            .investigateConclusion(DEFAULT_INVESTIGATE_CONCLUSION)
            .recommendMeasure(DEFAULT_RECOMMEND_MEASURE)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ProblemInvestigateReport createUpdatedEntity() {
        return new ProblemInvestigateReport()
            .tenantId(UPDATED_TENANT_ID)
            .title(UPDATED_TITLE)
            .reportCode(UPDATED_REPORT_CODE)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .reportType(UPDATED_REPORT_TYPE)
            .investigateSource(UPDATED_INVESTIGATE_SOURCE)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .orgId(UPDATED_ORG_ID)
            .establishDate(UPDATED_ESTABLISH_DATE)
            .level(UPDATED_LEVEL)
            .summary(UPDATED_SUMMARY)
            .status(UPDATED_STATUS)
            .investigateBackground(UPDATED_INVESTIGATE_BACKGROUND)
            .investigateMethod(UPDATED_INVESTIGATE_METHOD)
            .investigateProcess(UPDATED_INVESTIGATE_PROCESS)
            .investigateFound(UPDATED_INVESTIGATE_FOUND)
            .investigateConclusion(UPDATED_INVESTIGATE_CONCLUSION)
            .recommendMeasure(UPDATED_RECOMMEND_MEASURE)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        problemInvestigateReport = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedProblemInvestigateReport != null) {
            problemInvestigateReportRepository.delete(insertedProblemInvestigateReport);
            insertedProblemInvestigateReport = null;
        }
    }

    @Test
    @Transactional
    void createProblemInvestigateReport() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ProblemInvestigateReport
        ProblemInvestigateReportDTO problemInvestigateReportDTO = problemInvestigateReportMapper.toDto(problemInvestigateReport);
        var returnedProblemInvestigateReportDTO = om.readValue(
            restProblemInvestigateReportMockMvc
                .perform(
                    post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateReportDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ProblemInvestigateReportDTO.class
        );

        // Validate the ProblemInvestigateReport in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedProblemInvestigateReport = problemInvestigateReportMapper.toEntity(returnedProblemInvestigateReportDTO);
        assertProblemInvestigateReportUpdatableFieldsEquals(
            returnedProblemInvestigateReport,
            getPersistedProblemInvestigateReport(returnedProblemInvestigateReport)
        );

        insertedProblemInvestigateReport = returnedProblemInvestigateReport;
    }

    @Test
    @Transactional
    void createProblemInvestigateReportWithExistingId() throws Exception {
        // Create the ProblemInvestigateReport with an existing ID
        problemInvestigateReport.setId(1L);
        ProblemInvestigateReportDTO problemInvestigateReportDTO = problemInvestigateReportMapper.toDto(problemInvestigateReport);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restProblemInvestigateReportMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateReportDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateReport in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateReport.setTenantId(null);

        // Create the ProblemInvestigateReport, which fails.
        ProblemInvestigateReportDTO problemInvestigateReportDTO = problemInvestigateReportMapper.toDto(problemInvestigateReport);

        restProblemInvestigateReportMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateReportDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkReportCodeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateReport.setReportCode(null);

        // Create the ProblemInvestigateReport, which fails.
        ProblemInvestigateReportDTO problemInvestigateReportDTO = problemInvestigateReportMapper.toDto(problemInvestigateReport);

        restProblemInvestigateReportMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateReportDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkInvestigateIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateReport.setInvestigateId(null);

        // Create the ProblemInvestigateReport, which fails.
        ProblemInvestigateReportDTO problemInvestigateReportDTO = problemInvestigateReportMapper.toDto(problemInvestigateReport);

        restProblemInvestigateReportMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateReportDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateReport.setEmployeeId(null);

        // Create the ProblemInvestigateReport, which fails.
        ProblemInvestigateReportDTO problemInvestigateReportDTO = problemInvestigateReportMapper.toDto(problemInvestigateReport);

        restProblemInvestigateReportMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateReportDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkOrgIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateReport.setOrgId(null);

        // Create the ProblemInvestigateReport, which fails.
        ProblemInvestigateReportDTO problemInvestigateReportDTO = problemInvestigateReportMapper.toDto(problemInvestigateReport);

        restProblemInvestigateReportMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateReportDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateReport.setCreatedBy(null);

        // Create the ProblemInvestigateReport, which fails.
        ProblemInvestigateReportDTO problemInvestigateReportDTO = problemInvestigateReportMapper.toDto(problemInvestigateReport);

        restProblemInvestigateReportMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateReportDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllProblemInvestigateReports() throws Exception {
        // Initialize the database
        insertedProblemInvestigateReport = problemInvestigateReportRepository.saveAndFlush(problemInvestigateReport);

        // Get all the problemInvestigateReportList
        restProblemInvestigateReportMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(problemInvestigateReport.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].title").value(hasItem(DEFAULT_TITLE)))
            .andExpect(jsonPath("$.[*].reportCode").value(hasItem(DEFAULT_REPORT_CODE)))
            .andExpect(jsonPath("$.[*].investigateId").value(hasItem(DEFAULT_INVESTIGATE_ID.intValue())))
            .andExpect(jsonPath("$.[*].reportType").value(hasItem(DEFAULT_REPORT_TYPE.toString())))
            .andExpect(jsonPath("$.[*].investigateSource").value(hasItem(DEFAULT_INVESTIGATE_SOURCE.toString())))
            .andExpect(jsonPath("$.[*].employeeId").value(hasItem(DEFAULT_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].orgId").value(hasItem(DEFAULT_ORG_ID.intValue())))
            .andExpect(jsonPath("$.[*].establishDate").value(hasItem(DEFAULT_ESTABLISH_DATE.toString())))
            .andExpect(jsonPath("$.[*].level").value(hasItem(DEFAULT_LEVEL.toString())))
            .andExpect(jsonPath("$.[*].summary").value(hasItem(DEFAULT_SUMMARY)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].investigateBackground").value(hasItem(DEFAULT_INVESTIGATE_BACKGROUND)))
            .andExpect(jsonPath("$.[*].investigateMethod").value(hasItem(DEFAULT_INVESTIGATE_METHOD)))
            .andExpect(jsonPath("$.[*].investigateProcess").value(hasItem(DEFAULT_INVESTIGATE_PROCESS)))
            .andExpect(jsonPath("$.[*].investigateFound").value(hasItem(DEFAULT_INVESTIGATE_FOUND)))
            .andExpect(jsonPath("$.[*].investigateConclusion").value(hasItem(DEFAULT_INVESTIGATE_CONCLUSION)))
            .andExpect(jsonPath("$.[*].recommendMeasure").value(hasItem(DEFAULT_RECOMMEND_MEASURE)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getProblemInvestigateReport() throws Exception {
        // Initialize the database
        insertedProblemInvestigateReport = problemInvestigateReportRepository.saveAndFlush(problemInvestigateReport);

        // Get the problemInvestigateReport
        restProblemInvestigateReportMockMvc
            .perform(get(ENTITY_API_URL_ID, problemInvestigateReport.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(problemInvestigateReport.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.title").value(DEFAULT_TITLE))
            .andExpect(jsonPath("$.reportCode").value(DEFAULT_REPORT_CODE))
            .andExpect(jsonPath("$.investigateId").value(DEFAULT_INVESTIGATE_ID.intValue()))
            .andExpect(jsonPath("$.reportType").value(DEFAULT_REPORT_TYPE.toString()))
            .andExpect(jsonPath("$.investigateSource").value(DEFAULT_INVESTIGATE_SOURCE.toString()))
            .andExpect(jsonPath("$.employeeId").value(DEFAULT_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.orgId").value(DEFAULT_ORG_ID.intValue()))
            .andExpect(jsonPath("$.establishDate").value(DEFAULT_ESTABLISH_DATE.toString()))
            .andExpect(jsonPath("$.level").value(DEFAULT_LEVEL.toString()))
            .andExpect(jsonPath("$.summary").value(DEFAULT_SUMMARY))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.investigateBackground").value(DEFAULT_INVESTIGATE_BACKGROUND))
            .andExpect(jsonPath("$.investigateMethod").value(DEFAULT_INVESTIGATE_METHOD))
            .andExpect(jsonPath("$.investigateProcess").value(DEFAULT_INVESTIGATE_PROCESS))
            .andExpect(jsonPath("$.investigateFound").value(DEFAULT_INVESTIGATE_FOUND))
            .andExpect(jsonPath("$.investigateConclusion").value(DEFAULT_INVESTIGATE_CONCLUSION))
            .andExpect(jsonPath("$.recommendMeasure").value(DEFAULT_RECOMMEND_MEASURE))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingProblemInvestigateReport() throws Exception {
        // Get the problemInvestigateReport
        restProblemInvestigateReportMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingProblemInvestigateReport() throws Exception {
        // Initialize the database
        insertedProblemInvestigateReport = problemInvestigateReportRepository.saveAndFlush(problemInvestigateReport);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateReport
        ProblemInvestigateReport updatedProblemInvestigateReport = problemInvestigateReportRepository
            .findById(problemInvestigateReport.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedProblemInvestigateReport are not directly saved in db
        em.detach(updatedProblemInvestigateReport);
        updatedProblemInvestigateReport
            .tenantId(UPDATED_TENANT_ID)
            .title(UPDATED_TITLE)
            .reportCode(UPDATED_REPORT_CODE)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .reportType(UPDATED_REPORT_TYPE)
            .investigateSource(UPDATED_INVESTIGATE_SOURCE)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .orgId(UPDATED_ORG_ID)
            .establishDate(UPDATED_ESTABLISH_DATE)
            .level(UPDATED_LEVEL)
            .summary(UPDATED_SUMMARY)
            .status(UPDATED_STATUS)
            .investigateBackground(UPDATED_INVESTIGATE_BACKGROUND)
            .investigateMethod(UPDATED_INVESTIGATE_METHOD)
            .investigateProcess(UPDATED_INVESTIGATE_PROCESS)
            .investigateFound(UPDATED_INVESTIGATE_FOUND)
            .investigateConclusion(UPDATED_INVESTIGATE_CONCLUSION)
            .recommendMeasure(UPDATED_RECOMMEND_MEASURE)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ProblemInvestigateReportDTO problemInvestigateReportDTO = problemInvestigateReportMapper.toDto(updatedProblemInvestigateReport);

        restProblemInvestigateReportMockMvc
            .perform(
                put(ENTITY_API_URL_ID, problemInvestigateReportDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateReportDTO))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedProblemInvestigateReportToMatchAllProperties(updatedProblemInvestigateReport);
    }

    @Test
    @Transactional
    void putNonExistingProblemInvestigateReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateReport.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateReport
        ProblemInvestigateReportDTO problemInvestigateReportDTO = problemInvestigateReportMapper.toDto(problemInvestigateReport);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restProblemInvestigateReportMockMvc
            .perform(
                put(ENTITY_API_URL_ID, problemInvestigateReportDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateReportDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchProblemInvestigateReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateReport.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateReport
        ProblemInvestigateReportDTO problemInvestigateReportDTO = problemInvestigateReportMapper.toDto(problemInvestigateReport);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateReportMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateReportDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamProblemInvestigateReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateReport.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateReport
        ProblemInvestigateReportDTO problemInvestigateReportDTO = problemInvestigateReportMapper.toDto(problemInvestigateReport);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateReportMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(problemInvestigateReportDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the ProblemInvestigateReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateProblemInvestigateReportWithPatch() throws Exception {
        // Initialize the database
        insertedProblemInvestigateReport = problemInvestigateReportRepository.saveAndFlush(problemInvestigateReport);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateReport using partial update
        ProblemInvestigateReport partialUpdatedProblemInvestigateReport = new ProblemInvestigateReport();
        partialUpdatedProblemInvestigateReport.setId(problemInvestigateReport.getId());

        partialUpdatedProblemInvestigateReport
            .title(UPDATED_TITLE)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .reportType(UPDATED_REPORT_TYPE)
            .status(UPDATED_STATUS)
            .investigateFound(UPDATED_INVESTIGATE_FOUND)
            .investigateConclusion(UPDATED_INVESTIGATE_CONCLUSION)
            .recommendMeasure(UPDATED_RECOMMEND_MEASURE)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT);

        restProblemInvestigateReportMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedProblemInvestigateReport.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedProblemInvestigateReport))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateReport in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertProblemInvestigateReportUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedProblemInvestigateReport, problemInvestigateReport),
            getPersistedProblemInvestigateReport(problemInvestigateReport)
        );
    }

    @Test
    @Transactional
    void fullUpdateProblemInvestigateReportWithPatch() throws Exception {
        // Initialize the database
        insertedProblemInvestigateReport = problemInvestigateReportRepository.saveAndFlush(problemInvestigateReport);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateReport using partial update
        ProblemInvestigateReport partialUpdatedProblemInvestigateReport = new ProblemInvestigateReport();
        partialUpdatedProblemInvestigateReport.setId(problemInvestigateReport.getId());

        partialUpdatedProblemInvestigateReport
            .tenantId(UPDATED_TENANT_ID)
            .title(UPDATED_TITLE)
            .reportCode(UPDATED_REPORT_CODE)
            .investigateId(UPDATED_INVESTIGATE_ID)
            .reportType(UPDATED_REPORT_TYPE)
            .investigateSource(UPDATED_INVESTIGATE_SOURCE)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .orgId(UPDATED_ORG_ID)
            .establishDate(UPDATED_ESTABLISH_DATE)
            .level(UPDATED_LEVEL)
            .summary(UPDATED_SUMMARY)
            .status(UPDATED_STATUS)
            .investigateBackground(UPDATED_INVESTIGATE_BACKGROUND)
            .investigateMethod(UPDATED_INVESTIGATE_METHOD)
            .investigateProcess(UPDATED_INVESTIGATE_PROCESS)
            .investigateFound(UPDATED_INVESTIGATE_FOUND)
            .investigateConclusion(UPDATED_INVESTIGATE_CONCLUSION)
            .recommendMeasure(UPDATED_RECOMMEND_MEASURE)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restProblemInvestigateReportMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedProblemInvestigateReport.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedProblemInvestigateReport))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateReport in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertProblemInvestigateReportUpdatableFieldsEquals(
            partialUpdatedProblemInvestigateReport,
            getPersistedProblemInvestigateReport(partialUpdatedProblemInvestigateReport)
        );
    }

    @Test
    @Transactional
    void patchNonExistingProblemInvestigateReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateReport.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateReport
        ProblemInvestigateReportDTO problemInvestigateReportDTO = problemInvestigateReportMapper.toDto(problemInvestigateReport);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restProblemInvestigateReportMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, problemInvestigateReportDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(problemInvestigateReportDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchProblemInvestigateReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateReport.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateReport
        ProblemInvestigateReportDTO problemInvestigateReportDTO = problemInvestigateReportMapper.toDto(problemInvestigateReport);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateReportMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(problemInvestigateReportDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamProblemInvestigateReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateReport.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateReport
        ProblemInvestigateReportDTO problemInvestigateReportDTO = problemInvestigateReportMapper.toDto(problemInvestigateReport);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateReportMockMvc
            .perform(
                patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(problemInvestigateReportDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ProblemInvestigateReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteProblemInvestigateReport() throws Exception {
        // Initialize the database
        insertedProblemInvestigateReport = problemInvestigateReportRepository.saveAndFlush(problemInvestigateReport);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the problemInvestigateReport
        restProblemInvestigateReportMockMvc
            .perform(delete(ENTITY_API_URL_ID, problemInvestigateReport.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return problemInvestigateReportRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ProblemInvestigateReport getPersistedProblemInvestigateReport(ProblemInvestigateReport problemInvestigateReport) {
        return problemInvestigateReportRepository.findById(problemInvestigateReport.getId()).orElseThrow();
    }

    protected void assertPersistedProblemInvestigateReportToMatchAllProperties(ProblemInvestigateReport expectedProblemInvestigateReport) {
        assertProblemInvestigateReportAllPropertiesEquals(
            expectedProblemInvestigateReport,
            getPersistedProblemInvestigateReport(expectedProblemInvestigateReport)
        );
    }

    protected void assertPersistedProblemInvestigateReportToMatchUpdatableProperties(
        ProblemInvestigateReport expectedProblemInvestigateReport
    ) {
        assertProblemInvestigateReportAllUpdatablePropertiesEquals(
            expectedProblemInvestigateReport,
            getPersistedProblemInvestigateReport(expectedProblemInvestigateReport)
        );
    }
}
