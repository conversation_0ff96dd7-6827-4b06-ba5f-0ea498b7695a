package com.whiskerguard.violation.web.rest;

import static com.whiskerguard.violation.domain.ResponsibilityInvestigateMeasureAsserts.*;
import static com.whiskerguard.violation.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.violation.IntegrationTest;
import com.whiskerguard.violation.domain.ResponsibilityInvestigateMeasure;
import com.whiskerguard.violation.domain.enumeration.MeasureType;
import com.whiskerguard.violation.domain.enumeration.Status;
import com.whiskerguard.violation.repository.ResponsibilityInvestigateMeasureRepository;
import com.whiskerguard.violation.service.dto.ResponsibilityInvestigateMeasureDTO;
import com.whiskerguard.violation.service.mapper.ResponsibilityInvestigateMeasureMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ResponsibilityInvestigateMeasureResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ResponsibilityInvestigateMeasureResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_NAME = "BBBBBBBBBB";

    private static final Long DEFAULT_DEAL_ID = 1L;
    private static final Long UPDATED_DEAL_ID = 2L;

    private static final MeasureType DEFAULT_MEASURE_TYPE = MeasureType.DISCIPLINARY_ACTION;
    private static final MeasureType UPDATED_MEASURE_TYPE = MeasureType.ECONOMIC_PENALTIES;

    private static final LocalDate DEFAULT_START_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_START_DATE = LocalDate.now(ZoneId.systemDefault());

    private static final LocalDate DEFAULT_FINISH_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_FINISH_DATE = LocalDate.now(ZoneId.systemDefault());

    private static final Status DEFAULT_STATUS = Status.NO_START;
    private static final Status UPDATED_STATUS = Status.PROGRESSING;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/responsibility-investigate-measures";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ResponsibilityInvestigateMeasureRepository responsibilityInvestigateMeasureRepository;

    @Autowired
    private ResponsibilityInvestigateMeasureMapper responsibilityInvestigateMeasureMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restResponsibilityInvestigateMeasureMockMvc;

    private ResponsibilityInvestigateMeasure responsibilityInvestigateMeasure;

    private ResponsibilityInvestigateMeasure insertedResponsibilityInvestigateMeasure;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ResponsibilityInvestigateMeasure createEntity() {
        return new ResponsibilityInvestigateMeasure()
            .tenantId(DEFAULT_TENANT_ID)
            .name(DEFAULT_NAME)
            .dealId(DEFAULT_DEAL_ID)
            .measureType(DEFAULT_MEASURE_TYPE)
            .startDate(DEFAULT_START_DATE)
            .finishDate(DEFAULT_FINISH_DATE)
            .status(DEFAULT_STATUS)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ResponsibilityInvestigateMeasure createUpdatedEntity() {
        return new ResponsibilityInvestigateMeasure()
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .dealId(UPDATED_DEAL_ID)
            .measureType(UPDATED_MEASURE_TYPE)
            .startDate(UPDATED_START_DATE)
            .finishDate(UPDATED_FINISH_DATE)
            .status(UPDATED_STATUS)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        responsibilityInvestigateMeasure = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedResponsibilityInvestigateMeasure != null) {
            responsibilityInvestigateMeasureRepository.delete(insertedResponsibilityInvestigateMeasure);
            insertedResponsibilityInvestigateMeasure = null;
        }
    }

    @Test
    @Transactional
    void createResponsibilityInvestigateMeasure() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ResponsibilityInvestigateMeasure
        ResponsibilityInvestigateMeasureDTO responsibilityInvestigateMeasureDTO = responsibilityInvestigateMeasureMapper.toDto(
            responsibilityInvestigateMeasure
        );
        var returnedResponsibilityInvestigateMeasureDTO = om.readValue(
            restResponsibilityInvestigateMeasureMockMvc
                .perform(
                    post(ENTITY_API_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(om.writeValueAsBytes(responsibilityInvestigateMeasureDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ResponsibilityInvestigateMeasureDTO.class
        );

        // Validate the ResponsibilityInvestigateMeasure in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedResponsibilityInvestigateMeasure = responsibilityInvestigateMeasureMapper.toEntity(
            returnedResponsibilityInvestigateMeasureDTO
        );
        assertResponsibilityInvestigateMeasureUpdatableFieldsEquals(
            returnedResponsibilityInvestigateMeasure,
            getPersistedResponsibilityInvestigateMeasure(returnedResponsibilityInvestigateMeasure)
        );

        insertedResponsibilityInvestigateMeasure = returnedResponsibilityInvestigateMeasure;
    }

    @Test
    @Transactional
    void createResponsibilityInvestigateMeasureWithExistingId() throws Exception {
        // Create the ResponsibilityInvestigateMeasure with an existing ID
        responsibilityInvestigateMeasure.setId(1L);
        ResponsibilityInvestigateMeasureDTO responsibilityInvestigateMeasureDTO = responsibilityInvestigateMeasureMapper.toDto(
            responsibilityInvestigateMeasure
        );

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restResponsibilityInvestigateMeasureMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateMeasureDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ResponsibilityInvestigateMeasure in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateMeasure.setTenantId(null);

        // Create the ResponsibilityInvestigateMeasure, which fails.
        ResponsibilityInvestigateMeasureDTO responsibilityInvestigateMeasureDTO = responsibilityInvestigateMeasureMapper.toDto(
            responsibilityInvestigateMeasure
        );

        restResponsibilityInvestigateMeasureMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateMeasureDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkDealIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateMeasure.setDealId(null);

        // Create the ResponsibilityInvestigateMeasure, which fails.
        ResponsibilityInvestigateMeasureDTO responsibilityInvestigateMeasureDTO = responsibilityInvestigateMeasureMapper.toDto(
            responsibilityInvestigateMeasure
        );

        restResponsibilityInvestigateMeasureMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateMeasureDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        responsibilityInvestigateMeasure.setCreatedBy(null);

        // Create the ResponsibilityInvestigateMeasure, which fails.
        ResponsibilityInvestigateMeasureDTO responsibilityInvestigateMeasureDTO = responsibilityInvestigateMeasureMapper.toDto(
            responsibilityInvestigateMeasure
        );

        restResponsibilityInvestigateMeasureMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateMeasureDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllResponsibilityInvestigateMeasures() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateMeasure = responsibilityInvestigateMeasureRepository.saveAndFlush(
            responsibilityInvestigateMeasure
        );

        // Get all the responsibilityInvestigateMeasureList
        restResponsibilityInvestigateMeasureMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(responsibilityInvestigateMeasure.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].name").value(hasItem(DEFAULT_NAME)))
            .andExpect(jsonPath("$.[*].dealId").value(hasItem(DEFAULT_DEAL_ID.intValue())))
            .andExpect(jsonPath("$.[*].measureType").value(hasItem(DEFAULT_MEASURE_TYPE.toString())))
            .andExpect(jsonPath("$.[*].startDate").value(hasItem(DEFAULT_START_DATE.toString())))
            .andExpect(jsonPath("$.[*].finishDate").value(hasItem(DEFAULT_FINISH_DATE.toString())))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getResponsibilityInvestigateMeasure() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateMeasure = responsibilityInvestigateMeasureRepository.saveAndFlush(
            responsibilityInvestigateMeasure
        );

        // Get the responsibilityInvestigateMeasure
        restResponsibilityInvestigateMeasureMockMvc
            .perform(get(ENTITY_API_URL_ID, responsibilityInvestigateMeasure.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(responsibilityInvestigateMeasure.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.name").value(DEFAULT_NAME))
            .andExpect(jsonPath("$.dealId").value(DEFAULT_DEAL_ID.intValue()))
            .andExpect(jsonPath("$.measureType").value(DEFAULT_MEASURE_TYPE.toString()))
            .andExpect(jsonPath("$.startDate").value(DEFAULT_START_DATE.toString()))
            .andExpect(jsonPath("$.finishDate").value(DEFAULT_FINISH_DATE.toString()))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingResponsibilityInvestigateMeasure() throws Exception {
        // Get the responsibilityInvestigateMeasure
        restResponsibilityInvestigateMeasureMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingResponsibilityInvestigateMeasure() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateMeasure = responsibilityInvestigateMeasureRepository.saveAndFlush(
            responsibilityInvestigateMeasure
        );

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the responsibilityInvestigateMeasure
        ResponsibilityInvestigateMeasure updatedResponsibilityInvestigateMeasure = responsibilityInvestigateMeasureRepository
            .findById(responsibilityInvestigateMeasure.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedResponsibilityInvestigateMeasure are not directly saved in db
        em.detach(updatedResponsibilityInvestigateMeasure);
        updatedResponsibilityInvestigateMeasure
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .dealId(UPDATED_DEAL_ID)
            .measureType(UPDATED_MEASURE_TYPE)
            .startDate(UPDATED_START_DATE)
            .finishDate(UPDATED_FINISH_DATE)
            .status(UPDATED_STATUS)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ResponsibilityInvestigateMeasureDTO responsibilityInvestigateMeasureDTO = responsibilityInvestigateMeasureMapper.toDto(
            updatedResponsibilityInvestigateMeasure
        );

        restResponsibilityInvestigateMeasureMockMvc
            .perform(
                put(ENTITY_API_URL_ID, responsibilityInvestigateMeasureDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateMeasureDTO))
            )
            .andExpect(status().isOk());

        // Validate the ResponsibilityInvestigateMeasure in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedResponsibilityInvestigateMeasureToMatchAllProperties(updatedResponsibilityInvestigateMeasure);
    }

    @Test
    @Transactional
    void putNonExistingResponsibilityInvestigateMeasure() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateMeasure.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateMeasure
        ResponsibilityInvestigateMeasureDTO responsibilityInvestigateMeasureDTO = responsibilityInvestigateMeasureMapper.toDto(
            responsibilityInvestigateMeasure
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateMeasureMockMvc
            .perform(
                put(ENTITY_API_URL_ID, responsibilityInvestigateMeasureDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateMeasureDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ResponsibilityInvestigateMeasure in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchResponsibilityInvestigateMeasure() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateMeasure.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateMeasure
        ResponsibilityInvestigateMeasureDTO responsibilityInvestigateMeasureDTO = responsibilityInvestigateMeasureMapper.toDto(
            responsibilityInvestigateMeasure
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateMeasureMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateMeasureDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ResponsibilityInvestigateMeasure in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamResponsibilityInvestigateMeasure() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateMeasure.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateMeasure
        ResponsibilityInvestigateMeasureDTO responsibilityInvestigateMeasureDTO = responsibilityInvestigateMeasureMapper.toDto(
            responsibilityInvestigateMeasure
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateMeasureMockMvc
            .perform(
                put(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(responsibilityInvestigateMeasureDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ResponsibilityInvestigateMeasure in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateResponsibilityInvestigateMeasureWithPatch() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateMeasure = responsibilityInvestigateMeasureRepository.saveAndFlush(
            responsibilityInvestigateMeasure
        );

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the responsibilityInvestigateMeasure using partial update
        ResponsibilityInvestigateMeasure partialUpdatedResponsibilityInvestigateMeasure = new ResponsibilityInvestigateMeasure();
        partialUpdatedResponsibilityInvestigateMeasure.setId(responsibilityInvestigateMeasure.getId());

        partialUpdatedResponsibilityInvestigateMeasure
            .tenantId(UPDATED_TENANT_ID)
            .dealId(UPDATED_DEAL_ID)
            .measureType(UPDATED_MEASURE_TYPE)
            .startDate(UPDATED_START_DATE)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restResponsibilityInvestigateMeasureMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedResponsibilityInvestigateMeasure.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedResponsibilityInvestigateMeasure))
            )
            .andExpect(status().isOk());

        // Validate the ResponsibilityInvestigateMeasure in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertResponsibilityInvestigateMeasureUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedResponsibilityInvestigateMeasure, responsibilityInvestigateMeasure),
            getPersistedResponsibilityInvestigateMeasure(responsibilityInvestigateMeasure)
        );
    }

    @Test
    @Transactional
    void fullUpdateResponsibilityInvestigateMeasureWithPatch() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateMeasure = responsibilityInvestigateMeasureRepository.saveAndFlush(
            responsibilityInvestigateMeasure
        );

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the responsibilityInvestigateMeasure using partial update
        ResponsibilityInvestigateMeasure partialUpdatedResponsibilityInvestigateMeasure = new ResponsibilityInvestigateMeasure();
        partialUpdatedResponsibilityInvestigateMeasure.setId(responsibilityInvestigateMeasure.getId());

        partialUpdatedResponsibilityInvestigateMeasure
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .dealId(UPDATED_DEAL_ID)
            .measureType(UPDATED_MEASURE_TYPE)
            .startDate(UPDATED_START_DATE)
            .finishDate(UPDATED_FINISH_DATE)
            .status(UPDATED_STATUS)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restResponsibilityInvestigateMeasureMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedResponsibilityInvestigateMeasure.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedResponsibilityInvestigateMeasure))
            )
            .andExpect(status().isOk());

        // Validate the ResponsibilityInvestigateMeasure in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertResponsibilityInvestigateMeasureUpdatableFieldsEquals(
            partialUpdatedResponsibilityInvestigateMeasure,
            getPersistedResponsibilityInvestigateMeasure(partialUpdatedResponsibilityInvestigateMeasure)
        );
    }

    @Test
    @Transactional
    void patchNonExistingResponsibilityInvestigateMeasure() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateMeasure.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateMeasure
        ResponsibilityInvestigateMeasureDTO responsibilityInvestigateMeasureDTO = responsibilityInvestigateMeasureMapper.toDto(
            responsibilityInvestigateMeasure
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateMeasureMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, responsibilityInvestigateMeasureDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(responsibilityInvestigateMeasureDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ResponsibilityInvestigateMeasure in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchResponsibilityInvestigateMeasure() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateMeasure.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateMeasure
        ResponsibilityInvestigateMeasureDTO responsibilityInvestigateMeasureDTO = responsibilityInvestigateMeasureMapper.toDto(
            responsibilityInvestigateMeasure
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateMeasureMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(responsibilityInvestigateMeasureDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ResponsibilityInvestigateMeasure in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamResponsibilityInvestigateMeasure() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        responsibilityInvestigateMeasure.setId(longCount.incrementAndGet());

        // Create the ResponsibilityInvestigateMeasure
        ResponsibilityInvestigateMeasureDTO responsibilityInvestigateMeasureDTO = responsibilityInvestigateMeasureMapper.toDto(
            responsibilityInvestigateMeasure
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restResponsibilityInvestigateMeasureMockMvc
            .perform(
                patch(ENTITY_API_URL)
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(responsibilityInvestigateMeasureDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ResponsibilityInvestigateMeasure in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteResponsibilityInvestigateMeasure() throws Exception {
        // Initialize the database
        insertedResponsibilityInvestigateMeasure = responsibilityInvestigateMeasureRepository.saveAndFlush(
            responsibilityInvestigateMeasure
        );

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the responsibilityInvestigateMeasure
        restResponsibilityInvestigateMeasureMockMvc
            .perform(delete(ENTITY_API_URL_ID, responsibilityInvestigateMeasure.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return responsibilityInvestigateMeasureRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ResponsibilityInvestigateMeasure getPersistedResponsibilityInvestigateMeasure(
        ResponsibilityInvestigateMeasure responsibilityInvestigateMeasure
    ) {
        return responsibilityInvestigateMeasureRepository.findById(responsibilityInvestigateMeasure.getId()).orElseThrow();
    }

    protected void assertPersistedResponsibilityInvestigateMeasureToMatchAllProperties(
        ResponsibilityInvestigateMeasure expectedResponsibilityInvestigateMeasure
    ) {
        assertResponsibilityInvestigateMeasureAllPropertiesEquals(
            expectedResponsibilityInvestigateMeasure,
            getPersistedResponsibilityInvestigateMeasure(expectedResponsibilityInvestigateMeasure)
        );
    }

    protected void assertPersistedResponsibilityInvestigateMeasureToMatchUpdatableProperties(
        ResponsibilityInvestigateMeasure expectedResponsibilityInvestigateMeasure
    ) {
        assertResponsibilityInvestigateMeasureAllUpdatablePropertiesEquals(
            expectedResponsibilityInvestigateMeasure,
            getPersistedResponsibilityInvestigateMeasure(expectedResponsibilityInvestigateMeasure)
        );
    }
}
