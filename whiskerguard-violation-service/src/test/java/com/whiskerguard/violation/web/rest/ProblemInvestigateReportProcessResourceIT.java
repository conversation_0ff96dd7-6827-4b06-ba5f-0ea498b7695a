package com.whiskerguard.violation.web.rest;

import static com.whiskerguard.violation.domain.ProblemInvestigateReportProcessAsserts.*;
import static com.whiskerguard.violation.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.violation.IntegrationTest;
import com.whiskerguard.violation.domain.ProblemInvestigateReportProcess;
import com.whiskerguard.violation.repository.ProblemInvestigateReportProcessRepository;
import com.whiskerguard.violation.service.dto.ProblemInvestigateReportProcessDTO;
import com.whiskerguard.violation.service.mapper.ProblemInvestigateReportProcessMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ProblemInvestigateReportProcessResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ProblemInvestigateReportProcessResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_REPORT_ID = 1L;
    private static final Long UPDATED_REPORT_ID = 2L;

    private static final Long DEFAULT_PROCESS_ID = 1L;
    private static final Long UPDATED_PROCESS_ID = 2L;

    private static final Integer DEFAULT_TIME_LIMIT = 1;
    private static final Integer UPDATED_TIME_LIMIT = 2;

    private static final Integer DEFAULT_IS_URGED = 1;
    private static final Integer UPDATED_IS_URGED = 2;

    private static final Integer DEFAULT_URGED_INTERVAL = 1;
    private static final Integer UPDATED_URGED_INTERVAL = 2;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/problem-investigate-report-processes";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ProblemInvestigateReportProcessRepository problemInvestigateReportProcessRepository;

    @Autowired
    private ProblemInvestigateReportProcessMapper problemInvestigateReportProcessMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restProblemInvestigateReportProcessMockMvc;

    private ProblemInvestigateReportProcess problemInvestigateReportProcess;

    private ProblemInvestigateReportProcess insertedProblemInvestigateReportProcess;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ProblemInvestigateReportProcess createEntity() {
        return new ProblemInvestigateReportProcess()
            .tenantId(DEFAULT_TENANT_ID)
            .reportId(DEFAULT_REPORT_ID)
            .processId(DEFAULT_PROCESS_ID)
            .timeLimit(DEFAULT_TIME_LIMIT)
            .isUrged(DEFAULT_IS_URGED)
            .urgedInterval(DEFAULT_URGED_INTERVAL)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ProblemInvestigateReportProcess createUpdatedEntity() {
        return new ProblemInvestigateReportProcess()
            .tenantId(UPDATED_TENANT_ID)
            .reportId(UPDATED_REPORT_ID)
            .processId(UPDATED_PROCESS_ID)
            .timeLimit(UPDATED_TIME_LIMIT)
            .isUrged(UPDATED_IS_URGED)
            .urgedInterval(UPDATED_URGED_INTERVAL)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        problemInvestigateReportProcess = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedProblemInvestigateReportProcess != null) {
            problemInvestigateReportProcessRepository.delete(insertedProblemInvestigateReportProcess);
            insertedProblemInvestigateReportProcess = null;
        }
    }

    @Test
    @Transactional
    void createProblemInvestigateReportProcess() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ProblemInvestigateReportProcess
        ProblemInvestigateReportProcessDTO problemInvestigateReportProcessDTO = problemInvestigateReportProcessMapper.toDto(
            problemInvestigateReportProcess
        );
        var returnedProblemInvestigateReportProcessDTO = om.readValue(
            restProblemInvestigateReportProcessMockMvc
                .perform(
                    post(ENTITY_API_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(om.writeValueAsBytes(problemInvestigateReportProcessDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ProblemInvestigateReportProcessDTO.class
        );

        // Validate the ProblemInvestigateReportProcess in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedProblemInvestigateReportProcess = problemInvestigateReportProcessMapper.toEntity(
            returnedProblemInvestigateReportProcessDTO
        );
        assertProblemInvestigateReportProcessUpdatableFieldsEquals(
            returnedProblemInvestigateReportProcess,
            getPersistedProblemInvestigateReportProcess(returnedProblemInvestigateReportProcess)
        );

        insertedProblemInvestigateReportProcess = returnedProblemInvestigateReportProcess;
    }

    @Test
    @Transactional
    void createProblemInvestigateReportProcessWithExistingId() throws Exception {
        // Create the ProblemInvestigateReportProcess with an existing ID
        problemInvestigateReportProcess.setId(1L);
        ProblemInvestigateReportProcessDTO problemInvestigateReportProcessDTO = problemInvestigateReportProcessMapper.toDto(
            problemInvestigateReportProcess
        );

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restProblemInvestigateReportProcessMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateReportProcess in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateReportProcess.setTenantId(null);

        // Create the ProblemInvestigateReportProcess, which fails.
        ProblemInvestigateReportProcessDTO problemInvestigateReportProcessDTO = problemInvestigateReportProcessMapper.toDto(
            problemInvestigateReportProcess
        );

        restProblemInvestigateReportProcessMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkReportIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateReportProcess.setReportId(null);

        // Create the ProblemInvestigateReportProcess, which fails.
        ProblemInvestigateReportProcessDTO problemInvestigateReportProcessDTO = problemInvestigateReportProcessMapper.toDto(
            problemInvestigateReportProcess
        );

        restProblemInvestigateReportProcessMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkProcessIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateReportProcess.setProcessId(null);

        // Create the ProblemInvestigateReportProcess, which fails.
        ProblemInvestigateReportProcessDTO problemInvestigateReportProcessDTO = problemInvestigateReportProcessMapper.toDto(
            problemInvestigateReportProcess
        );

        restProblemInvestigateReportProcessMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        problemInvestigateReportProcess.setCreatedBy(null);

        // Create the ProblemInvestigateReportProcess, which fails.
        ProblemInvestigateReportProcessDTO problemInvestigateReportProcessDTO = problemInvestigateReportProcessMapper.toDto(
            problemInvestigateReportProcess
        );

        restProblemInvestigateReportProcessMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllProblemInvestigateReportProcesses() throws Exception {
        // Initialize the database
        insertedProblemInvestigateReportProcess = problemInvestigateReportProcessRepository.saveAndFlush(problemInvestigateReportProcess);

        // Get all the problemInvestigateReportProcessList
        restProblemInvestigateReportProcessMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(problemInvestigateReportProcess.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].reportId").value(hasItem(DEFAULT_REPORT_ID.intValue())))
            .andExpect(jsonPath("$.[*].processId").value(hasItem(DEFAULT_PROCESS_ID.intValue())))
            .andExpect(jsonPath("$.[*].timeLimit").value(hasItem(DEFAULT_TIME_LIMIT)))
            .andExpect(jsonPath("$.[*].isUrged").value(hasItem(DEFAULT_IS_URGED)))
            .andExpect(jsonPath("$.[*].urgedInterval").value(hasItem(DEFAULT_URGED_INTERVAL)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getProblemInvestigateReportProcess() throws Exception {
        // Initialize the database
        insertedProblemInvestigateReportProcess = problemInvestigateReportProcessRepository.saveAndFlush(problemInvestigateReportProcess);

        // Get the problemInvestigateReportProcess
        restProblemInvestigateReportProcessMockMvc
            .perform(get(ENTITY_API_URL_ID, problemInvestigateReportProcess.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(problemInvestigateReportProcess.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.reportId").value(DEFAULT_REPORT_ID.intValue()))
            .andExpect(jsonPath("$.processId").value(DEFAULT_PROCESS_ID.intValue()))
            .andExpect(jsonPath("$.timeLimit").value(DEFAULT_TIME_LIMIT))
            .andExpect(jsonPath("$.isUrged").value(DEFAULT_IS_URGED))
            .andExpect(jsonPath("$.urgedInterval").value(DEFAULT_URGED_INTERVAL))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingProblemInvestigateReportProcess() throws Exception {
        // Get the problemInvestigateReportProcess
        restProblemInvestigateReportProcessMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingProblemInvestigateReportProcess() throws Exception {
        // Initialize the database
        insertedProblemInvestigateReportProcess = problemInvestigateReportProcessRepository.saveAndFlush(problemInvestigateReportProcess);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateReportProcess
        ProblemInvestigateReportProcess updatedProblemInvestigateReportProcess = problemInvestigateReportProcessRepository
            .findById(problemInvestigateReportProcess.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedProblemInvestigateReportProcess are not directly saved in db
        em.detach(updatedProblemInvestigateReportProcess);
        updatedProblemInvestigateReportProcess
            .tenantId(UPDATED_TENANT_ID)
            .reportId(UPDATED_REPORT_ID)
            .processId(UPDATED_PROCESS_ID)
            .timeLimit(UPDATED_TIME_LIMIT)
            .isUrged(UPDATED_IS_URGED)
            .urgedInterval(UPDATED_URGED_INTERVAL)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ProblemInvestigateReportProcessDTO problemInvestigateReportProcessDTO = problemInvestigateReportProcessMapper.toDto(
            updatedProblemInvestigateReportProcess
        );

        restProblemInvestigateReportProcessMockMvc
            .perform(
                put(ENTITY_API_URL_ID, problemInvestigateReportProcessDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateReportProcessDTO))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateReportProcess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedProblemInvestigateReportProcessToMatchAllProperties(updatedProblemInvestigateReportProcess);
    }

    @Test
    @Transactional
    void putNonExistingProblemInvestigateReportProcess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateReportProcess.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateReportProcess
        ProblemInvestigateReportProcessDTO problemInvestigateReportProcessDTO = problemInvestigateReportProcessMapper.toDto(
            problemInvestigateReportProcess
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restProblemInvestigateReportProcessMockMvc
            .perform(
                put(ENTITY_API_URL_ID, problemInvestigateReportProcessDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateReportProcess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchProblemInvestigateReportProcess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateReportProcess.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateReportProcess
        ProblemInvestigateReportProcessDTO problemInvestigateReportProcessDTO = problemInvestigateReportProcessMapper.toDto(
            problemInvestigateReportProcess
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateReportProcessMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateReportProcess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamProblemInvestigateReportProcess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateReportProcess.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateReportProcess
        ProblemInvestigateReportProcessDTO problemInvestigateReportProcessDTO = problemInvestigateReportProcessMapper.toDto(
            problemInvestigateReportProcess
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateReportProcessMockMvc
            .perform(
                put(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(problemInvestigateReportProcessDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ProblemInvestigateReportProcess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateProblemInvestigateReportProcessWithPatch() throws Exception {
        // Initialize the database
        insertedProblemInvestigateReportProcess = problemInvestigateReportProcessRepository.saveAndFlush(problemInvestigateReportProcess);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateReportProcess using partial update
        ProblemInvestigateReportProcess partialUpdatedProblemInvestigateReportProcess = new ProblemInvestigateReportProcess();
        partialUpdatedProblemInvestigateReportProcess.setId(problemInvestigateReportProcess.getId());

        partialUpdatedProblemInvestigateReportProcess
            .timeLimit(UPDATED_TIME_LIMIT)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restProblemInvestigateReportProcessMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedProblemInvestigateReportProcess.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedProblemInvestigateReportProcess))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateReportProcess in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertProblemInvestigateReportProcessUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedProblemInvestigateReportProcess, problemInvestigateReportProcess),
            getPersistedProblemInvestigateReportProcess(problemInvestigateReportProcess)
        );
    }

    @Test
    @Transactional
    void fullUpdateProblemInvestigateReportProcessWithPatch() throws Exception {
        // Initialize the database
        insertedProblemInvestigateReportProcess = problemInvestigateReportProcessRepository.saveAndFlush(problemInvestigateReportProcess);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the problemInvestigateReportProcess using partial update
        ProblemInvestigateReportProcess partialUpdatedProblemInvestigateReportProcess = new ProblemInvestigateReportProcess();
        partialUpdatedProblemInvestigateReportProcess.setId(problemInvestigateReportProcess.getId());

        partialUpdatedProblemInvestigateReportProcess
            .tenantId(UPDATED_TENANT_ID)
            .reportId(UPDATED_REPORT_ID)
            .processId(UPDATED_PROCESS_ID)
            .timeLimit(UPDATED_TIME_LIMIT)
            .isUrged(UPDATED_IS_URGED)
            .urgedInterval(UPDATED_URGED_INTERVAL)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restProblemInvestigateReportProcessMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedProblemInvestigateReportProcess.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedProblemInvestigateReportProcess))
            )
            .andExpect(status().isOk());

        // Validate the ProblemInvestigateReportProcess in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertProblemInvestigateReportProcessUpdatableFieldsEquals(
            partialUpdatedProblemInvestigateReportProcess,
            getPersistedProblemInvestigateReportProcess(partialUpdatedProblemInvestigateReportProcess)
        );
    }

    @Test
    @Transactional
    void patchNonExistingProblemInvestigateReportProcess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateReportProcess.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateReportProcess
        ProblemInvestigateReportProcessDTO problemInvestigateReportProcessDTO = problemInvestigateReportProcessMapper.toDto(
            problemInvestigateReportProcess
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restProblemInvestigateReportProcessMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, problemInvestigateReportProcessDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(problemInvestigateReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateReportProcess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchProblemInvestigateReportProcess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateReportProcess.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateReportProcess
        ProblemInvestigateReportProcessDTO problemInvestigateReportProcessDTO = problemInvestigateReportProcessMapper.toDto(
            problemInvestigateReportProcess
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateReportProcessMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(problemInvestigateReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProblemInvestigateReportProcess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamProblemInvestigateReportProcess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        problemInvestigateReportProcess.setId(longCount.incrementAndGet());

        // Create the ProblemInvestigateReportProcess
        ProblemInvestigateReportProcessDTO problemInvestigateReportProcessDTO = problemInvestigateReportProcessMapper.toDto(
            problemInvestigateReportProcess
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProblemInvestigateReportProcessMockMvc
            .perform(
                patch(ENTITY_API_URL)
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(problemInvestigateReportProcessDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ProblemInvestigateReportProcess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteProblemInvestigateReportProcess() throws Exception {
        // Initialize the database
        insertedProblemInvestigateReportProcess = problemInvestigateReportProcessRepository.saveAndFlush(problemInvestigateReportProcess);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the problemInvestigateReportProcess
        restProblemInvestigateReportProcessMockMvc
            .perform(delete(ENTITY_API_URL_ID, problemInvestigateReportProcess.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return problemInvestigateReportProcessRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ProblemInvestigateReportProcess getPersistedProblemInvestigateReportProcess(
        ProblemInvestigateReportProcess problemInvestigateReportProcess
    ) {
        return problemInvestigateReportProcessRepository.findById(problemInvestigateReportProcess.getId()).orElseThrow();
    }

    protected void assertPersistedProblemInvestigateReportProcessToMatchAllProperties(
        ProblemInvestigateReportProcess expectedProblemInvestigateReportProcess
    ) {
        assertProblemInvestigateReportProcessAllPropertiesEquals(
            expectedProblemInvestigateReportProcess,
            getPersistedProblemInvestigateReportProcess(expectedProblemInvestigateReportProcess)
        );
    }

    protected void assertPersistedProblemInvestigateReportProcessToMatchUpdatableProperties(
        ProblemInvestigateReportProcess expectedProblemInvestigateReportProcess
    ) {
        assertProblemInvestigateReportProcessAllUpdatablePropertiesEquals(
            expectedProblemInvestigateReportProcess,
            getPersistedProblemInvestigateReportProcess(expectedProblemInvestigateReportProcess)
        );
    }
}
