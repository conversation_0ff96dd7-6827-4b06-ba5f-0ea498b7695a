package com.whiskerguard.violation.web.rest;

import static com.whiskerguard.violation.domain.ContinuousImprovementReportProcessAsserts.*;
import static com.whiskerguard.violation.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.violation.IntegrationTest;
import com.whiskerguard.violation.domain.ContinuousImprovementReportProcess;
import com.whiskerguard.violation.repository.ContinuousImprovementReportProcessRepository;
import com.whiskerguard.violation.service.dto.ContinuousImprovementReportProcessDTO;
import com.whiskerguard.violation.service.mapper.ContinuousImprovementReportProcessMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ContinuousImprovementReportProcessResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ContinuousImprovementReportProcessResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_REPORT_ID = 1L;
    private static final Long UPDATED_REPORT_ID = 2L;

    private static final Long DEFAULT_PROCESS_ID = 1L;
    private static final Long UPDATED_PROCESS_ID = 2L;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/continuous-improvement-report-processes";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ContinuousImprovementReportProcessRepository continuousImprovementReportProcessRepository;

    @Autowired
    private ContinuousImprovementReportProcessMapper continuousImprovementReportProcessMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restContinuousImprovementReportProcessMockMvc;

    private ContinuousImprovementReportProcess continuousImprovementReportProcess;

    private ContinuousImprovementReportProcess insertedContinuousImprovementReportProcess;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContinuousImprovementReportProcess createEntity() {
        return new ContinuousImprovementReportProcess()
            .tenantId(DEFAULT_TENANT_ID)
            .reportId(DEFAULT_REPORT_ID)
            .processId(DEFAULT_PROCESS_ID)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContinuousImprovementReportProcess createUpdatedEntity() {
        return new ContinuousImprovementReportProcess()
            .tenantId(UPDATED_TENANT_ID)
            .reportId(UPDATED_REPORT_ID)
            .processId(UPDATED_PROCESS_ID)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        continuousImprovementReportProcess = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedContinuousImprovementReportProcess != null) {
            continuousImprovementReportProcessRepository.delete(insertedContinuousImprovementReportProcess);
            insertedContinuousImprovementReportProcess = null;
        }
    }

    @Test
    @Transactional
    void createContinuousImprovementReportProcess() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ContinuousImprovementReportProcess
        ContinuousImprovementReportProcessDTO continuousImprovementReportProcessDTO = continuousImprovementReportProcessMapper.toDto(
            continuousImprovementReportProcess
        );
        var returnedContinuousImprovementReportProcessDTO = om.readValue(
            restContinuousImprovementReportProcessMockMvc
                .perform(
                    post(ENTITY_API_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(om.writeValueAsBytes(continuousImprovementReportProcessDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ContinuousImprovementReportProcessDTO.class
        );

        // Validate the ContinuousImprovementReportProcess in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedContinuousImprovementReportProcess = continuousImprovementReportProcessMapper.toEntity(
            returnedContinuousImprovementReportProcessDTO
        );
        assertContinuousImprovementReportProcessUpdatableFieldsEquals(
            returnedContinuousImprovementReportProcess,
            getPersistedContinuousImprovementReportProcess(returnedContinuousImprovementReportProcess)
        );

        insertedContinuousImprovementReportProcess = returnedContinuousImprovementReportProcess;
    }

    @Test
    @Transactional
    void createContinuousImprovementReportProcessWithExistingId() throws Exception {
        // Create the ContinuousImprovementReportProcess with an existing ID
        continuousImprovementReportProcess.setId(1L);
        ContinuousImprovementReportProcessDTO continuousImprovementReportProcessDTO = continuousImprovementReportProcessMapper.toDto(
            continuousImprovementReportProcess
        );

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restContinuousImprovementReportProcessMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementReportProcess in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementReportProcess.setTenantId(null);

        // Create the ContinuousImprovementReportProcess, which fails.
        ContinuousImprovementReportProcessDTO continuousImprovementReportProcessDTO = continuousImprovementReportProcessMapper.toDto(
            continuousImprovementReportProcess
        );

        restContinuousImprovementReportProcessMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkReportIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementReportProcess.setReportId(null);

        // Create the ContinuousImprovementReportProcess, which fails.
        ContinuousImprovementReportProcessDTO continuousImprovementReportProcessDTO = continuousImprovementReportProcessMapper.toDto(
            continuousImprovementReportProcess
        );

        restContinuousImprovementReportProcessMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkProcessIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementReportProcess.setProcessId(null);

        // Create the ContinuousImprovementReportProcess, which fails.
        ContinuousImprovementReportProcessDTO continuousImprovementReportProcessDTO = continuousImprovementReportProcessMapper.toDto(
            continuousImprovementReportProcess
        );

        restContinuousImprovementReportProcessMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        continuousImprovementReportProcess.setCreatedBy(null);

        // Create the ContinuousImprovementReportProcess, which fails.
        ContinuousImprovementReportProcessDTO continuousImprovementReportProcessDTO = continuousImprovementReportProcessMapper.toDto(
            continuousImprovementReportProcess
        );

        restContinuousImprovementReportProcessMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllContinuousImprovementReportProcesses() throws Exception {
        // Initialize the database
        insertedContinuousImprovementReportProcess = continuousImprovementReportProcessRepository.saveAndFlush(
            continuousImprovementReportProcess
        );

        // Get all the continuousImprovementReportProcessList
        restContinuousImprovementReportProcessMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(continuousImprovementReportProcess.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].reportId").value(hasItem(DEFAULT_REPORT_ID.intValue())))
            .andExpect(jsonPath("$.[*].processId").value(hasItem(DEFAULT_PROCESS_ID.intValue())))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getContinuousImprovementReportProcess() throws Exception {
        // Initialize the database
        insertedContinuousImprovementReportProcess = continuousImprovementReportProcessRepository.saveAndFlush(
            continuousImprovementReportProcess
        );

        // Get the continuousImprovementReportProcess
        restContinuousImprovementReportProcessMockMvc
            .perform(get(ENTITY_API_URL_ID, continuousImprovementReportProcess.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(continuousImprovementReportProcess.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.reportId").value(DEFAULT_REPORT_ID.intValue()))
            .andExpect(jsonPath("$.processId").value(DEFAULT_PROCESS_ID.intValue()))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingContinuousImprovementReportProcess() throws Exception {
        // Get the continuousImprovementReportProcess
        restContinuousImprovementReportProcessMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingContinuousImprovementReportProcess() throws Exception {
        // Initialize the database
        insertedContinuousImprovementReportProcess = continuousImprovementReportProcessRepository.saveAndFlush(
            continuousImprovementReportProcess
        );

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the continuousImprovementReportProcess
        ContinuousImprovementReportProcess updatedContinuousImprovementReportProcess = continuousImprovementReportProcessRepository
            .findById(continuousImprovementReportProcess.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedContinuousImprovementReportProcess are not directly saved in db
        em.detach(updatedContinuousImprovementReportProcess);
        updatedContinuousImprovementReportProcess
            .tenantId(UPDATED_TENANT_ID)
            .reportId(UPDATED_REPORT_ID)
            .processId(UPDATED_PROCESS_ID)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ContinuousImprovementReportProcessDTO continuousImprovementReportProcessDTO = continuousImprovementReportProcessMapper.toDto(
            updatedContinuousImprovementReportProcess
        );

        restContinuousImprovementReportProcessMockMvc
            .perform(
                put(ENTITY_API_URL_ID, continuousImprovementReportProcessDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementReportProcessDTO))
            )
            .andExpect(status().isOk());

        // Validate the ContinuousImprovementReportProcess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedContinuousImprovementReportProcessToMatchAllProperties(updatedContinuousImprovementReportProcess);
    }

    @Test
    @Transactional
    void putNonExistingContinuousImprovementReportProcess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementReportProcess.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementReportProcess
        ContinuousImprovementReportProcessDTO continuousImprovementReportProcessDTO = continuousImprovementReportProcessMapper.toDto(
            continuousImprovementReportProcess
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContinuousImprovementReportProcessMockMvc
            .perform(
                put(ENTITY_API_URL_ID, continuousImprovementReportProcessDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementReportProcess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchContinuousImprovementReportProcess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementReportProcess.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementReportProcess
        ContinuousImprovementReportProcessDTO continuousImprovementReportProcessDTO = continuousImprovementReportProcessMapper.toDto(
            continuousImprovementReportProcess
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementReportProcessMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementReportProcess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamContinuousImprovementReportProcess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementReportProcess.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementReportProcess
        ContinuousImprovementReportProcessDTO continuousImprovementReportProcessDTO = continuousImprovementReportProcessMapper.toDto(
            continuousImprovementReportProcess
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementReportProcessMockMvc
            .perform(
                put(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(continuousImprovementReportProcessDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContinuousImprovementReportProcess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateContinuousImprovementReportProcessWithPatch() throws Exception {
        // Initialize the database
        insertedContinuousImprovementReportProcess = continuousImprovementReportProcessRepository.saveAndFlush(
            continuousImprovementReportProcess
        );

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the continuousImprovementReportProcess using partial update
        ContinuousImprovementReportProcess partialUpdatedContinuousImprovementReportProcess = new ContinuousImprovementReportProcess();
        partialUpdatedContinuousImprovementReportProcess.setId(continuousImprovementReportProcess.getId());

        partialUpdatedContinuousImprovementReportProcess.createdBy(UPDATED_CREATED_BY);

        restContinuousImprovementReportProcessMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContinuousImprovementReportProcess.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContinuousImprovementReportProcess))
            )
            .andExpect(status().isOk());

        // Validate the ContinuousImprovementReportProcess in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContinuousImprovementReportProcessUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedContinuousImprovementReportProcess, continuousImprovementReportProcess),
            getPersistedContinuousImprovementReportProcess(continuousImprovementReportProcess)
        );
    }

    @Test
    @Transactional
    void fullUpdateContinuousImprovementReportProcessWithPatch() throws Exception {
        // Initialize the database
        insertedContinuousImprovementReportProcess = continuousImprovementReportProcessRepository.saveAndFlush(
            continuousImprovementReportProcess
        );

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the continuousImprovementReportProcess using partial update
        ContinuousImprovementReportProcess partialUpdatedContinuousImprovementReportProcess = new ContinuousImprovementReportProcess();
        partialUpdatedContinuousImprovementReportProcess.setId(continuousImprovementReportProcess.getId());

        partialUpdatedContinuousImprovementReportProcess
            .tenantId(UPDATED_TENANT_ID)
            .reportId(UPDATED_REPORT_ID)
            .processId(UPDATED_PROCESS_ID)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restContinuousImprovementReportProcessMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContinuousImprovementReportProcess.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContinuousImprovementReportProcess))
            )
            .andExpect(status().isOk());

        // Validate the ContinuousImprovementReportProcess in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContinuousImprovementReportProcessUpdatableFieldsEquals(
            partialUpdatedContinuousImprovementReportProcess,
            getPersistedContinuousImprovementReportProcess(partialUpdatedContinuousImprovementReportProcess)
        );
    }

    @Test
    @Transactional
    void patchNonExistingContinuousImprovementReportProcess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementReportProcess.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementReportProcess
        ContinuousImprovementReportProcessDTO continuousImprovementReportProcessDTO = continuousImprovementReportProcessMapper.toDto(
            continuousImprovementReportProcess
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContinuousImprovementReportProcessMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, continuousImprovementReportProcessDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(continuousImprovementReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementReportProcess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchContinuousImprovementReportProcess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementReportProcess.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementReportProcess
        ContinuousImprovementReportProcessDTO continuousImprovementReportProcessDTO = continuousImprovementReportProcessMapper.toDto(
            continuousImprovementReportProcess
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementReportProcessMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(continuousImprovementReportProcessDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContinuousImprovementReportProcess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamContinuousImprovementReportProcess() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        continuousImprovementReportProcess.setId(longCount.incrementAndGet());

        // Create the ContinuousImprovementReportProcess
        ContinuousImprovementReportProcessDTO continuousImprovementReportProcessDTO = continuousImprovementReportProcessMapper.toDto(
            continuousImprovementReportProcess
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContinuousImprovementReportProcessMockMvc
            .perform(
                patch(ENTITY_API_URL)
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(continuousImprovementReportProcessDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContinuousImprovementReportProcess in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteContinuousImprovementReportProcess() throws Exception {
        // Initialize the database
        insertedContinuousImprovementReportProcess = continuousImprovementReportProcessRepository.saveAndFlush(
            continuousImprovementReportProcess
        );

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the continuousImprovementReportProcess
        restContinuousImprovementReportProcessMockMvc
            .perform(delete(ENTITY_API_URL_ID, continuousImprovementReportProcess.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return continuousImprovementReportProcessRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ContinuousImprovementReportProcess getPersistedContinuousImprovementReportProcess(
        ContinuousImprovementReportProcess continuousImprovementReportProcess
    ) {
        return continuousImprovementReportProcessRepository.findById(continuousImprovementReportProcess.getId()).orElseThrow();
    }

    protected void assertPersistedContinuousImprovementReportProcessToMatchAllProperties(
        ContinuousImprovementReportProcess expectedContinuousImprovementReportProcess
    ) {
        assertContinuousImprovementReportProcessAllPropertiesEquals(
            expectedContinuousImprovementReportProcess,
            getPersistedContinuousImprovementReportProcess(expectedContinuousImprovementReportProcess)
        );
    }

    protected void assertPersistedContinuousImprovementReportProcessToMatchUpdatableProperties(
        ContinuousImprovementReportProcess expectedContinuousImprovementReportProcess
    ) {
        assertContinuousImprovementReportProcessAllUpdatablePropertiesEquals(
            expectedContinuousImprovementReportProcess,
            getPersistedContinuousImprovementReportProcess(expectedContinuousImprovementReportProcess)
        );
    }
}
