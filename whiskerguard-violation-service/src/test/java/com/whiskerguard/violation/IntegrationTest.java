package com.whiskerguard.violation;

import com.whiskerguard.violation.config.AsyncSyncConfiguration;
import com.whiskerguard.violation.config.EmbeddedRedis;
import com.whiskerguard.violation.config.EmbeddedSQL;
import com.whiskerguard.violation.config.JacksonConfiguration;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * Base composite annotation for integration tests.
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@SpringBootTest(classes = { WhiskerguardViolationServiceApp.class, JacksonConfiguration.class, AsyncSyncConfiguration.class })
@EmbeddedRedis
@EmbeddedSQL
public @interface IntegrationTest {
}
