# ===================================================================
# Spring Boot configuration.
#
# This configuration is used for unit/integration tests with testcontainers database containers.
#
# To activate this configuration launch integration tests with the 'testcontainers' profile
#
# More information on database containers: https://www.testcontainers.org/modules/databases/
# ===================================================================

spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      poolName: Hikari
      auto-commit: false
      maximum-pool-size: 1
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
  jpa:
    open-in-view: false
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
    properties:
      hibernate.id.new_generator_mappings: true
      hibernate.connection.provider_disables_autocommit: true
      hibernate.cache.use_second_level_cache: false
      hibernate.cache.use_query_cache: false
      hibernate.generate_statistics: false
      hibernate.hbm2ddl.auto: none #TODO: temp relief for integration tests, revisit required
      hibernate.type.preferred_instant_jdbc_type: TIMESTAMP
      hibernate.jdbc.time_zone: UTC
      hibernate.timezone.default_storage: NORMALIZE
      hibernate.query.fail_on_pagination_over_collection_fetch: true
