# This file is inspired by https://github.com/alexkaratarakis/gitattributes
#
# Auto detect text files and perform LF normalization
# http://davidlaing.com/2012/09/19/customise-your-gitattributes-to-become-a-git-ninja/
* text=auto

# The above will handle all files NOT found below
# These files are text and should be normalized (Convert crlf => lf)

*.bat           text eol=crlf
*.cmd           text eol=crlf
*.ps1           text eol=crlf
*.coffee        text
*.css           text
*.cql           text
*.df            text
*.ejs           text
*.html          text
*.java          text
*.js            text
*.json          text
*.less          text
*.properties    text
*.sass          text
*.scss          text
*.sh            text eol=lf
*.sql           text
*.txt           text
*.ts            text
*.xml           text
*.yaml          text
*.yml           text

# Documents
*.doc           diff=astextplain
*.DOC           diff=astextplain
*.docx          diff=astextplain
*.DOCX          diff=astextplain
*.dot           diff=astextplain
*.DOT           diff=astextplain
*.pdf           diff=astextplain
*.PDF           diff=astextplain
*.rtf           diff=astextplain
*.RTF           diff=astextplain
*.markdown      text
*.md            text
*.adoc          text
*.textile       text
*.mustache      text
*.csv           text
*.tab           text
*.tsv           text
*.txt           text
AUTHORS         text
CHANGELOG       text
CHANGES         text
CONTRIBUTING    text
COPYING         text
copyright       text
*COPYRIGHT*     text
INSTALL         text
license         text
LICENSE         text
NEWS            text
readme          text
*README*        text
TODO            text

# Graphics
*.png           binary
*.jpg           binary
*.jpeg          binary
*.gif           binary
*.tif           binary
*.tiff          binary
*.ico           binary
# SVG treated as an asset (binary) by default. If you want to treat it as text,
# comment-out the following line and uncomment the line after.
*.svg           binary
#*.svg          text
*.eps           binary

# These files are binary and should be left untouched
# (binary is a macro for -text -diff)
*.class         binary
*.jar           binary
*.war           binary

## LINTERS
.csslintrc      text
.eslintrc       text
.jscsrc         text
.jshintrc       text
.jshintignore   text
.stylelintrc    text

## CONFIGS
*.conf          text
*.config        text
.editorconfig   text
.gitattributes  text
.gitconfig      text
.gitignore      text
.htaccess       text
*.npmignore     text

## HEROKU
Procfile        text
.slugignore     text

## AUDIO
*.kar           binary
*.m4a           binary
*.mid           binary
*.midi          binary
*.mp3           binary
*.ogg           binary
*.ra            binary

## VIDEO
*.3gpp          binary
*.3gp           binary
*.as            binary
*.asf           binary
*.asx           binary
*.fla           binary
*.flv           binary
*.m4v           binary
*.mng           binary
*.mov           binary
*.mp4           binary
*.mpeg          binary
*.mpg           binary
*.swc           binary
*.swf           binary
*.webm          binary

## ARCHIVES
*.7z            binary
*.gz            binary
*.rar           binary
*.tar           binary
*.zip           binary

## FONTS
*.ttf           binary
*.eot           binary
*.otf           binary
*.woff          binary
*.woff2         binary
