{"generator-jhipster": {"applicationType": "microservice", "authenticationType": "jwt", "baseName": "whiskerguardViolationService", "buildTool": "maven", "cacheProvider": "redis", "clientFramework": "no", "clientTestFrameworks": null, "clientTheme": null, "creationTimestamp": 1746174597497, "databaseType": "sql", "devDatabaseType": "mysql", "enableHibernateCache": false, "enableTranslation": true, "entities": ["ViolationAttachment", "ViolationChannel", "ViolationChannelAccess", "ViolationChannelAnonymous", "ViolationChannelAnonymousDesensitize", "ViolationChannelAppearance", "ViolationChannelForm", "ViolationChannelNoticeBase", "ViolationChannelNoticeFeedback", "ViolationChannelNoticeNew", "ViolationChannelNoticeRemind", "ViolationChannelNoticeStatus", "ViolationChannelNoticeTemplate", "ViolationChannelProcess", "ViolationChannelProcessRule", "ViolationDeal", "ViolationDetail", "ViolationEvidence", "ViolationPolicy", "ViolationPolicyAttachment", "ViolationPolicyHistory", "ViolationPolicyLaws", "ViolationProcessRecord", "ProblemInvestigateInvolve", "ProblemInvestigateAttachment", "ProblemInvestigateRecord", "ProblemInvestigateReport", "ProblemInvestigateReportProcess", "ProblemInvestigateTask", "ResponsibilityInvestigateCorrection", "ResponsibilityInvestigateDeal", "ResponsibilityInvestigateMeasure", "ContinuousImprovementAttachment", "ContinuousImprovementExperience", "ContinuousImprovementImprove", "ContinuousImprovementReport", "ContinuousImprovementReportProcess"], "feignClient": true, "jhipsterVersion": "8.10.0", "jwtSecretKey": "NjBlMTE0MDE3YjdkNDNlMzE1MmZlMzUzZGU4NWU2OWNmNWMzNjc3ZGFhNWQ1ZDBmZjExYjhhNDA2M2NhZjc2ZGY2YWFiMmIzYWFhYTA0N2UwMjNhZGZmNGU5N2E5ODFjOWE2MTFmNWIzNDdhN2I2ZWFhOTg2NDJhODg1MDE5Mjk=", "languages": ["zh-cn", "en"], "lastLiquibaseTimestamp": 1747995049000, "microfrontend": null, "microfrontends": [], "monorepository": true, "nativeLanguage": "zh-cn", "packageName": "com.whiskerguard.violation", "prodDatabaseType": "mysql", "reactive": false, "serverPort": "8186", "serviceDiscoveryType": "consul", "skipClient": true, "skipCommitHook": true, "skipUserManagement": true, "syncUserWithIdp": null, "testFrameworks": [], "withAdminUi": null}}