# 🛡️ WhiskerGuard Violation Service

[![JHipster](https://img.shields.io/badge/JHipster-8.10.0-blue.svg)](https://www.jhipster.tech/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.x-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![Java](https://img.shields.io/badge/Java-17+-orange.svg)](https://openjdk.java.net/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-blue.svg)](https://www.mysql.com/)
[![Redis](https://img.shields.io/badge/Redis-Cache-red.svg)](https://redis.io/)
[![Consul](https://img.shields.io/badge/Consul-Service%20Discovery-purple.svg)](https://www.consul.io/)

## 📋 项目概述

WhiskerGuard Violation Service 是一个基于 JHipster 8.10.0 构建的企业级违规举报管理微服务系统。该服务提供完整的违规举报处理流程，包括举报管理、问题调查、责任追究和持续改进等核心功能模块。

### 🎯 核心功能

- **🚨 违规举报管理** - 完整的举报渠道配置、举报详情处理和证据管理
- **🔍 问题调查系统** - 专业的调查任务管理、调查记录和调查报告生成
- **⚖️ 责任追究机制** - 系统化的责任认定、处理措施和整改跟踪
- **📈 持续改进优化** - 经验教训总结、改进措施制定和优化报告管理
- **📋 政策法规管理** - 违规政策配置、法规关联和历史版本管理

### 🏗️ 技术架构

- **微服务架构** - 基于 Spring Cloud 的分布式微服务设计
- **服务发现** - 使用 Consul 进行服务注册与发现
- **数据持久化** - MySQL 数据库 + JPA/Hibernate ORM
- **缓存机制** - Redis 分布式缓存
- **安全认证** - JWT Token 认证机制
- **逻辑删除** - 所有删除操作采用逻辑删除，保证数据完整性和审计追踪

## 📁 项目结构

```
whiskerguard-violation-service/
├── 📂 src/main/java/com/whiskerguard/violation/
│   ├── 📂 config/          # 配置类
│   ├── 📂 domain/          # 实体类 (33个核心实体)
│   ├── 📂 repository/      # 数据访问层
│   ├── 📂 service/         # 业务逻辑层
│   │   ├── 📂 dto/         # 数据传输对象
│   │   ├── 📂 impl/        # 服务实现类 (已实现逻辑删除)
│   │   └── 📂 mapper/      # 对象映射器
│   └── 📂 web/rest/        # REST API 控制器
├── 📂 src/main/resources/
│   ├── 📂 config/          # 配置文件
│   │   ├── application.yml
│   │   ├── application-dev.yml
│   │   └── application-prod.yml
│   ├── 📂 i18n/            # 国际化资源 (中文/英文)
│   └── 📂 config/liquibase/ # 数据库迁移脚本
├── 📂 src/main/docker/     # Docker 配置文件
├── 📂 jdl/                 # JDL 实体定义文件
│   ├── continuous-improvement-system.jdl
│   ├── problem-investigation-system.jdl
│   └── responsibility-tracking-system.jdl
└── 📂 .jhipster/           # JHipster 实体配置文件
```

### 🗃️ 核心实体模块

#### 1. 违规举报管理模块 (Violation Management)
- `ViolationDetail` - 举报详情
- `ViolationChannel` - 举报渠道
- `ViolationAttachment` - 举报附件
- `ViolationEvidence` - 举报证据
- `ViolationDeal` - 举报处理
- `ViolationPolicy` - 违规政策
- `ViolationProcessRecord` - 处理记录

#### 2. 问题调查系统 (Problem Investigation)
- `ProblemInvestigateTask` - 调查任务
- `ProblemInvestigateRecord` - 调查记录
- `ProblemInvestigateReport` - 调查报告
- `ProblemInvestigateInvolve` - 涉及人员
- `ProblemInvestigateAttachment` - 调查附件

#### 3. 责任追究机制 (Responsibility Tracking)
- `ResponsibilityInvestigateDeal` - 责任处理
- `ResponsibilityInvestigateCorrection` - 整改措施
- `ResponsibilityInvestigateMeasure` - 处理措施

#### 4. 持续改进优化 (Continuous Improvement)
- `ContinuousImprovementExperience` - 经验教训
- `ContinuousImprovementImprove` - 改进措施
- `ContinuousImprovementReport` - 优化报告

## 🚀 快速开始

### 📋 环境要求

- **Java 17+** - 推荐使用 OpenJDK 17 或更高版本
- **Maven 3.8+** - 构建工具
- **MySQL 8.0+** - 主数据库
- **Redis 6.0+** - 缓存服务
- **Consul 1.15+** - 服务发现与配置中心
- **Node.js 18+** - 前端工具链 (可选)

### 🔧 本地开发环境搭建

#### 1. 启动依赖服务

```bash
# 启动 MySQL 和 Redis
docker compose -f src/main/docker/mysql.yml up -d
docker compose -f src/main/docker/redis.yml up -d

# 启动 Consul (服务发现)
docker compose -f src/main/docker/consul.yml up -d
```

#### 2. 配置数据库

确保 MySQL 数据库已创建：
```sql
CREATE DATABASE whiskerguard_violation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 3. 启动应用 (开发模式)

```bash
# 使用 Maven 启动
./mvnw spring-boot:run

# 或者使用 dev profile
./mvnw -Pdev
```

应用将在 `http://localhost:8186` 启动

#### 4. 访问服务

- **API 文档**: http://localhost:8186/swagger-ui/
- **健康检查**: http://localhost:8186/management/health
- **应用信息**: http://localhost:8186/management/info

## 🏭 生产环境部署

### 📦 JAR 包构建

```bash
# 构建生产环境 JAR 包
./mvnw -Pprod clean verify

# 运行生产环境应用
java -jar target/whiskerguard-violation-service-*.jar
```

### 🐳 Docker 部署

```bash
# 构建 Docker 镜像
npm run java:docker

# 或构建 ARM64 镜像 (Apple M1/M2)
npm run java:docker:arm64

# 启动完整应用栈
docker compose -f src/main/docker/app.yml up -d
```

### ⚙️ 环境配置

生产环境需要配置以下环境变量：

```bash
# 数据库配置
SPRING_DATASOURCE_URL=**************************************************
SPRING_DATASOURCE_USERNAME=your_username
SPRING_DATASOURCE_PASSWORD=your_password

# Redis 配置
SPRING_DATA_REDIS_HOST=localhost
SPRING_DATA_REDIS_PORT=6379

# Consul 配置
SPRING_CLOUD_CONSUL_HOST=localhost
SPRING_CLOUD_CONSUL_PORT=8500

# JWT 密钥
JHIPSTER_SECURITY_AUTHENTICATION_JWT_BASE64_SECRET=your_jwt_secret
```

## 🧪 测试

### 单元测试和集成测试

```bash
# 运行所有测试
./mvnw verify

# 运行单元测试
./mvnw test

# 运行集成测试
./mvnw integration-test

# 生成测试报告
./mvnw surefire-report:report
```

### API 测试

使用 Swagger UI 进行 API 测试：
- 开发环境: http://localhost:8186/swagger-ui/
- 生产环境: https://your-domain/swagger-ui/

## 📊 监控和管理

### JHipster Control Center

启动 JHipster 控制中心进行应用监控：

```bash
docker compose -f src/main/docker/jhipster-control-center.yml up
```

访问地址: http://localhost:7419

### 应用监控端点

- **健康检查**: `/management/health`
- **应用信息**: `/management/info`
- **指标监控**: `/management/prometheus`
- **日志管理**: `/management/loggers`
- **配置信息**: `/management/configprops`
- **环境变量**: `/management/env`

### 日志管理

应用日志文件位置：
```
logs/whiskerguard-violation-service.log
```

日志级别配置：
```yaml
logging:
  level:
    com.whiskerguard.violation: DEBUG
    org.springframework.security: DEBUG
```

## 🔍 代码质量

### SonarQube 代码分析

启动 SonarQube 服务器：

```bash
docker compose -f src/main/docker/sonar.yml up -d
```

运行代码质量分析：

```bash
# 完整分析
./mvnw -Pprod clean verify sonar:sonar

# 使用自定义配置
./mvnw sonar:sonar -Dsonar.host.url=http://localhost:9001
```

访问 SonarQube: http://localhost:9001 (admin/admin)

## 🐳 Docker 支持

### 服务依赖管理

启动所有依赖服务：

```bash
# 启动所有服务 (MySQL, Redis, Consul)
docker compose -f src/main/docker/services.yml up -d

# 停止所有服务
docker compose -f src/main/docker/services.yml down
```

### 完整应用容器化

```bash
# 构建应用镜像
npm run java:docker

# 启动完整应用栈
docker compose -f src/main/docker/app.yml up -d
```

### 可用的 Docker Compose 文件

- `mysql.yml` - MySQL 数据库
- `redis.yml` - Redis 缓存
- `consul.yml` - Consul 服务发现
- `sonar.yml` - SonarQube 代码质量分析
- `jhipster-control-center.yml` - JHipster 控制中心
- `services.yml` - 所有依赖服务
- `app.yml` - 完整应用栈

## 📚 API 文档

### REST API 端点

#### 违规举报管理
- `GET /api/violation-details` - 获取举报详情列表
- `POST /api/violation-details` - 创建新举报
- `PUT /api/violation-details/{id}` - 更新举报信息
- `DELETE /api/violation-details/{id}` - 删除举报 (逻辑删除)

#### 问题调查系统
- `GET /api/problem-investigate-tasks` - 获取调查任务列表
- `POST /api/problem-investigate-tasks` - 创建调查任务
- `GET /api/problem-investigate-reports` - 获取调查报告列表

#### 责任追究机制
- `GET /api/responsibility-investigate-deals` - 获取责任处理列表
- `POST /api/responsibility-investigate-corrections` - 创建整改措施

#### 持续改进优化
- `GET /api/continuous-improvement-experiences` - 获取经验教训列表
- `POST /api/continuous-improvement-improves` - 创建改进措施

### 认证和授权

所有 API 端点都需要 JWT Token 认证：

```bash
# 获取 Token (示例)
curl -X POST http://localhost:8186/api/authenticate \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin"}'

# 使用 Token 访问 API
curl -X GET http://localhost:8186/api/violation-details \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🔧 开发指南

### 逻辑删除实现

所有实体的删除操作都已实现逻辑删除：

```java
@Override
public void delete(Long id) {
    repository.findById(id).ifPresent(entity -> {
        entity.setIsDeleted(true);
        entity.setUpdatedAt(Instant.now());
        repository.save(entity);
    });
}
```

### 添加新实体

1. 创建 JDL 文件定义实体
2. 运行 JHipster 生成器：`jhipster entity YourEntity`
3. 实现业务逻辑
4. 添加测试用例

### 国际化支持

系统支持中文和英文：
- 中文 (zh-cn) - 默认语言
- 英文 (en) - 备用语言

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 代码规范

- 遵循 Java 编码规范
- 使用 Prettier 格式化代码
- 编写单元测试
- 更新相关文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持和帮助

- **JHipster 文档**: https://www.jhipster.tech/
- **Spring Boot 文档**: https://spring.io/projects/spring-boot
- **问题反馈**: 请在 GitHub Issues 中提交

---

**WhiskerGuard Violation Service** - 企业级违规举报管理微服务系统 🛡️
